# Checking Headers and Functions for cairo

add_definitions(-DHAVE_CONFIG_H)

include( CheckIncludeFile )
include( CheckFunctionExists )
include( CheckLibraryExists )
include( CheckTypeSize)

if(WIN32)
    set(SHARED_LIB_EXT dll)
    if(MSVC)
        set( CMAKE_REQUIRED_INCLUDES ${CMAKE_INCLUDE_PATH} )
    else(MSVC)
        set( CMAKE_REQUIRED_INCLUDES ${CMAKE_INCLUDE_PATH} )
    endif(MSVC)
endif(WIN32)

if(FREETYPE_FOUND)
    set( CMAKE_REQUIRED_LIBRARIES ${CMAKE_REQUIRED_LIBRARIES} ${FREETYPE_LIBRARIES} )
    set( CMAKE_REQUIRED_INCLUDES ${CMAKE_REQUIRED_INCLUDES}  ${FREETYPE_INCLUDE_DIR} )
endif()

if(FONTCONFIG_FOUND)
    set( CMAKE_REQUIRED_LIBRARIES ${CMAKE_REQUIRED_LIBRARIES} ${FONTCONFIG_LIBRARIES})
    set( CMAKE_REQUIRED_INCLUDES ${CMAKE_REQUIRED_INCLUDES}  ${FONTCONFIG_INCLUDE_DIR})
endif()

if(THREADS_FOUND)
    set(CAIRO_HAR_REAL_PTHREAD 1)
	set(CAIRO_HAS_PTHREAD 1)
endif()

if(VALGRIND_FOUND)
	set(HAVE_VALGRIND 1)
endif()

check_include_file( "byteswap.h" HAVE_BYTESWAP_H )
check_include_file( "dirent.h" HAVE_DIRENT_H )
check_include_file( "dlfcn.h" HAVE_DLFCN_H )
check_include_file( "fenv.h" HAVE_FENV_H )
check_include_file( "fcntl.h" HAVE_FCNTL_H )
check_include_file( "inttypes.h" HAVE_INTTYPES_H )
check_include_file( "io.h" HAVE_IO_H )
check_include_file( "libgen.h" HAVE_LIBGEN_H )
check_include_file( "memory.h" HAVE_MEMORY_H )
check_include_file( "ndir.h" HAVE_NDIR_H )
check_include_file( "regex.h" HAVE_REGEX_H )
check_include_file( "sched.h" HAVE_SCHED_H )
check_include_file( "setjmp.h" HAVE_SETJMP_H )
check_include_file( "signal.h" HAVE_SIGNAL_H )
check_include_file( "stdint.h" HAVE_STDINT_H )
check_include_file( "stdlib.h" HAVE_STDLIB_H )
check_include_file( "strings.h" HAVE_STRINGS_H )
check_include_file( "string.h" HAVE_STRING_H )
check_include_file( "sys/dir.h" HAVE_SYS_DIR_H )
check_include_file( "sys/int_types.h" HAVE_SYS_INT_TYPES_H )
check_include_file( "sys/ioctl.h" HAVE_SYS_IOCTL_H )
check_include_file( "sys/mman.h" HAVE_SYS_MMAN_H )
check_include_file( "sys/mount.h" HAVE_SYS_MOUNT_H )
check_include_file( "sys/ndir.h" HAVE_SYS_NDIR_H )
check_include_file( "sys/param.h" HAVE_SYS_PARAM_H )
check_include_file( "sys/poll.h" HAVE_SYS_POLL_H )
check_include_file( "sys/socket.h" HAVE_SYS_SOCKET_H )
check_include_file( "sys/statfs.h" HAVE_SYS_STATFS_H )
check_include_file( "sys/stat.h" HAVE_SYS_STAT_H )
check_include_file( "sys/types.h" HAVE_SYS_TYPES_H )
check_include_file( "sys/vfs.h" HAVE_SYS_VFS_H )
check_include_file( "sys/un.h" HAVE_SYS_UN_H )
check_include_file( "sys/wait.h" HAVE_SYS_WAIT_H )
check_include_file( "time.h" HAVE_TIME_H )
check_include_file( "unistd.h" HAVE_UNISTD_H )
check_include_file( "windows.h" HAVE_WINDOWS_H )
check_include_file( "xmlparse.h" HAVE_XMLPARSE_H )


check_function_exists( _doprnt HAVE_DOPRNT )
check_function_exists( _mktemp_s HAVE__MKTEMP_S )
check_function_exists( alarm HAVE_ALARM )
check_function_exists( chsize HAVE_CHSIZE )
check_function_exists( clock_gettime HAVE_CLOCK_GETTIME )
check_function_exists( ctime_r HAVE_CTIME_R )
check_function_exists( drand48 HAVE_DRAND48 )
check_function_exists( FcFini HAVE_FCFINI )
check_function_exists( FcInit HAVE_FCINIT )
check_function_exists( flockfile HAVE_FLOCKFILE )
check_function_exists( fork HAVE_FORK )
check_function_exists( fstatfs HAVE_FSTATFS )
check_function_exists( fstatvfs HAVE_FSTATVFS )
check_function_exists( ftruncate HAVE_FTRUNCATE )
check_function_exists( FT_Get_BDF_Property HAVE_FT_GET_BDF_PROPERTY )
check_function_exists( FT_Get_Next_Char HAVE_FT_GET_NEXT_CHAR )
check_function_exists( FT_Get_PS_Font_Info HAVE_FT_GET_PS_FONT_INFO )
check_function_exists( FT_Get_X11_Font_Format HAVE_FT_GET_X11_FONT_FORMAT )
check_function_exists( FT_GlyphSlot_Embolden HAVE_FT_GLYPHSLOT_EMBOLDEN )
check_function_exists( FT_GlyphSlot_Oblique HAVE_FT_GLYPHSLOT_OBLIQUE )
check_function_exists( FT_Has_PS_Glyph_Names HAVE_FT_HAS_PS_GLYPH_NAMES )
check_function_exists( FT_Library_SetLcdFilter HAVE_FT_LIBRARY_SETLCDFILTER )
check_function_exists( FT_Load_Sfnt_Table HAVE_FT_LOAD_SFNT_TABLE )
check_function_exists( FT_Select_Size HAVE_FT_SELECT_SIZE )
check_function_exists( funlockfile HAVE_FUNLOCKFILE )
check_function_exists( gcov HAVE_GCOV )
check_function_exists( getexecname HAVE_GETEXECNAME )
check_function_exists( geteuid HAVE_GETEUID )
check_function_exists( getline HAVE_GETLINE )
check_function_exists( getopt HAVE_GETOPT )
check_function_exists( getopt_long HAVE_GETOPT_LONG )
check_function_exists( getpagesize HAVE_GETPAGESIZE )
check_function_exists( getprogname HAVE_GETPROGNAME )
check_function_exists( getuid HAVE_GETUID )
check_function_exists( link HAVE_LINK )
check_function_exists( lrand48 HAVE_LRAND48 )
check_function_exists( lstat HAVE_LSTAT )
check_function_exists( memmove HAVE_MEMMOVE )
check_function_exists( memset HAVE_MEMSET )
check_function_exists( mkdir HAVE_MKDIR )
check_function_exists( mkdtemp HAVE_MKDTEMP )
check_function_exists( mkostemp HAVE_MKOSTEMP )
check_function_exists( mkstemp HAVE_MKSTEMP )
check_function_exists( mmap HAVE_MMAP )
check_function_exists( poppler_page_render HAVE_POPPLER_PAGE_RENDER)
check_function_exists( posix_fadivse HAVE_POSIX_FADVISE )
check_function_exists( raise HAVE_RAISE )
check_function_exists( rand HAVE_RAND )
check_function_exists( rand_r HAVE_RAND_R )
check_function_exists( random HAVE_RANDOM )
check_function_exists( random_r HAVE_RANDOM_R )
check_function_exists( readlink HAVE_READLINK )
check_function_exists( regcomp HAVE_REGCOMP )
check_function_exists( regerror HAVE_REGERROR )
check_function_exists( regexec HAVE_REGEXEC )
check_function_exists( regfree HAVE_REGFREE )
check_function_exists( rsvg_pixbuf_from_file HAVE_RSVG_PIXBUF_FROM_FILE )
check_function_exists( scandir HAVE_SCANDIR )
check_function_exists( sched_getaffinity HAVE_SCHED_GETAFFINITY )
check_function_exists( strchr HAVE_STRCHR )
check_function_exists( strndup HAVE_STRNDUP )
check_function_exists( strrchr HAVE_STRRCHR )
check_function_exists( strtol HAVE_STRTOL )
check_function_exists( sysconf HAVE_SYSCONF )
check_function_exists( vprintf HAVE_VPRINTF )
check_function_exists( waitpid HAVE_WAITPID )
check_function_exists( XML_SetDoctypeDeclHandler HAVE_XML_SETDOCTYPEDECLHANDLER )

CHECK_TYPE_SIZE("int"       SIZEOF_INT)
CHECK_TYPE_SIZE("long"      SIZEOF_LONG)
CHECK_TYPE_SIZE("long long" SIZEOF_LONG_LONG)
CHECK_TYPE_SIZE("size_t"     SIZEOF_SIZE_T)
CHECK_TYPE_SIZE("void *"    SIZEOF_VOID_P)

CHECK_TYPE_SIZE("__uint128_t"   SIZEOF___UINT128_T)
CHECK_TYPE_SIZE("uint128_t"   SIZEOF_UINT128_T)
CHECK_TYPE_SIZE("uint64_t"    SIZEOF_UINT64_T)
if(SIZEOF___UINT128_T)
    set(HAVE___UINT128_T 1)
endif()
if(SIZEOF_UINT128_T)
    set(HAVE_UINT128_T 1)
endif()
if(SIZEOF_UINT64_T)
    set(HAVE_UINT64_T 1)
endif()
#

#INTEL ATOMIC PRIMITIVES 
if(NOT MSVC)
set( HAVE_INTEL_ATOMIC_PRIMITIVES 1) 
endif()

configure_file( ${CMAKE_CURRENT_SOURCE_DIR}/config.h.cmake ${CMAKE_CURRENT_BINARY_DIR}/config.h )
configure_file( ${CMAKE_CURRENT_SOURCE_DIR}/config.h.cmake ${CMAKE_CURRENT_BINARY_DIR}/src/config.h )
