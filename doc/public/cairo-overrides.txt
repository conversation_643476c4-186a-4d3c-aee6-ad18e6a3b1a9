<MACRO>
<NAME>CAIRO_HAS_FT_FONT</NAME>
#define CAIRO_HAS_FT_FONT
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_FC_FONT</NAME>
#define CAIRO_HAS_FC_FONT
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_PNG_FUNCTIONS</NAME>
#define CAIRO_HAS_PNG_FUNCTIONS
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_SVG_SURFACE</NAME>
#define CAIRO_HAS_SVG_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_PDF_SURFACE</NAME>
#define CAIRO_HAS_PDF_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_PS_SURFACE</NAME>
#define CAIRO_HAS_PS_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_SCRIPT_SURFACE</NAME>
#define CAIRO_HAS_SCRIPT_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_XLIB_SURFACE</NAME>
#define CAIRO_HAS_XLIB_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_XLIB_XRENDER_SURFACE</NAME>
#define CAIRO_HAS_XLIB_XRENDER_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_XCB_SURFACE</NAME>
#define CAIRO_HAS_XCB_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_XLIB_XCB_FUNCTIONS</NAME>
#define CAIRO_HAS_XLIB_XCB_FUNCTIONS
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_XCB_SHM_FUNCTIONS</NAME>
#define CAIRO_HAS_XCB_SHM_FUNCTIONS
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_QUARTZ_SURFACE</NAME>
#define CAIRO_HAS_QUARTZ_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_QUARTZ_IMAGE_SURFACE</NAME>
#define CAIRO_HAS_QUARTZ_IMAGE_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_QUARTZ_FONT</NAME>
#define CAIRO_HAS_QUARTZ_FONT
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_WIN32_SURFACE</NAME>
#define CAIRO_HAS_WIN32_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_WIN32_FONT</NAME>
#define CAIRO_HAS_WIN32_FONT
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_DWRITE_FONT</NAME>
#define CAIRO_HAS_DWRITE_FONT
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_GOBJECT_FUNCTIONS</NAME>
#define CAIRO_HAS_GOBJECT_FUNCTIONS
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_IMAGE_SURFACE</NAME>
#define CAIRO_HAS_IMAGE_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_USER_FONT</NAME>
#define CAIRO_HAS_USER_FONT
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_MIME_SURFACE</NAME>
#define CAIRO_HAS_MIME_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_RECORDING_SURFACE</NAME>
#define CAIRO_HAS_RECORDING_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_OBSERVER_SURFACE</NAME>
#define CAIRO_HAS_OBSERVER_SURFACE
</MACRO>

<MACRO>
<NAME>CAIRO_HAS_TEE_SURFACE</NAME>
#define CAIRO_HAS_TEE_SURFACE
</MACRO>
