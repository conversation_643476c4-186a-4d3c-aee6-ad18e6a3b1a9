<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE book PUBLIC "-//OASIS//DTD DocBook XML V4.2//EN"
		"http://www.oasis-open.org/docbook/xml/4.2/docbookx.dtd" [
<!ENTITY version SYSTEM "version.xml">
]>
<book lang="en" id="cairo" xmlns:xi="http://www.w3.org/2003/XInclude">
<title>Cairo: A Vector Graphics Library</title>
  <bookinfo>
    <title>Cairo: A Vector Graphics Library</title>
    <releaseinfo>for Cairo &version;</releaseinfo>
  </bookinfo>
  <chapter id="cairo-drawing">
    <title>Drawing</title>
    <xi:include href="xml/cairo.xml"/>
    <xi:include href="xml/cairo-paths.xml"/>
    <xi:include href="xml/cairo-pattern.xml"/>
    <xi:include href="xml/cairo-region.xml"/>
    <xi:include href="xml/cairo-transforms.xml"/>
    <xi:include href="xml/cairo-text.xml"/>
    <xi:include href="xml/cairo-raster-source.xml"/>
    <xi:include href="xml/cairo-tag.xml"/>
  </chapter>
  <chapter id="cairo-fonts">
    <title>Fonts</title>
    <xi:include href="xml/cairo-font-face.xml"/>
    <xi:include href="xml/cairo-scaled-font.xml"/>
    <xi:include href="xml/cairo-font-options.xml"/>
    <xi:include href="xml/cairo-ft.xml"/>
    <xi:include href="xml/cairo-win32-fonts.xml"/>
    <xi:include href="xml/cairo-dwrite-fonts.xml"/>
    <xi:include href="xml/cairo-quartz-fonts.xml"/>
    <xi:include href="xml/cairo-user-fonts.xml"/>
  </chapter>
  <chapter id="cairo-surfaces">
    <title>Surfaces</title>
    <xi:include href="xml/cairo-device.xml"/>
    <xi:include href="xml/cairo-surface.xml"/>
    <xi:include href="xml/cairo-image.xml"/>
    <xi:include href="xml/cairo-pdf.xml"/>
    <xi:include href="xml/cairo-png.xml"/>
    <xi:include href="xml/cairo-ps.xml"/>
    <xi:include href="xml/cairo-recording.xml"/>
    <xi:include href="xml/cairo-win32.xml"/>
    <xi:include href="xml/cairo-svg.xml"/>
    <xi:include href="xml/cairo-quartz.xml" />
    <xi:include href="xml/cairo-xcb.xml"/>
    <xi:include href="xml/cairo-xlib.xml"/>
    <xi:include href="xml/cairo-xlib-xrender.xml"/>
    <xi:include href="xml/cairo-script.xml"/>
    <xi:include href="xml/cairo-surface-observer.xml"/>
    <xi:include href="xml/cairo-tee.xml"/>
  </chapter>
  <chapter id="cairo-support">
    <title>Utilities</title>
    <xi:include href="xml/cairo-matrix.xml"/>
    <xi:include href="xml/cairo-status.xml"/>
    <xi:include href="xml/cairo-version.xml"/>
    <xi:include href="xml/cairo-types.xml"/>
  </chapter>
  <chapter id="api-index-all">
    <title>Index</title>
    <xi:include href="xml/api-index-full.xml"/>
  </chapter>
  <!--index id="deprecated-api-index">
    <title>Index of deprecated symbols</title>
    <xi:include href="xml/api-index-deprecated.xml"/>
  </index-->
  <chapter id="api-index-1-0">
    <title>Index of new symbols in 1.0</title>
    <xi:include href="xml/api-index-1.0.xml"/>
  </chapter>
  <chapter id="api-index-1-2">
    <title>Index of new symbols in 1.2</title>
    <xi:include href="xml/api-index-1.2.xml"/>
  </chapter>
  <chapter id="api-index-1-4">
    <title>Index of new symbols in 1.4</title>
    <xi:include href="xml/api-index-1.4.xml"/>
  </chapter>
  <chapter id="api-index-1-6">
    <title>Index of new symbols in 1.6</title>
    <xi:include href="xml/api-index-1.6.xml"/>
  </chapter>
  <chapter id="api-index-1-8">
    <title>Index of new symbols in 1.8</title>
    <xi:include href="xml/api-index-1.8.xml"/>
  </chapter>
  <chapter id="api-index-1-10">
    <title>Index of new symbols in 1.10</title>
    <xi:include href="xml/api-index-1.10.xml"/>
  </chapter>
  <chapter id="api-index-1-12">
    <title>Index of new symbols in 1.12</title>
    <xi:include href="xml/api-index-1.12.xml"/>
  </chapter>
  <chapter id="api-index-1-14">
    <title>Index of new symbols in 1.14</title>
    <xi:include href="xml/api-index-1.14.xml"/>
  </chapter>
  <chapter id="api-index-1-16">
    <title>Index of new symbols in 1.16</title>
    <xi:include href="xml/api-index-1.16.xml"/>
  </chapter>
  <chapter id="api-index-1-18">
    <title>Index of new symbols in 1.18</title>
    <xi:include href="xml/api-index-1.18.xml"/>
  </chapter>
  <xi:include href="xml/language-bindings.xml"/>
</book>
