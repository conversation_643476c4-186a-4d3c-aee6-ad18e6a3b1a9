<SECTION>
<FILE>cairo-ft</FILE>
CAIRO_HAS_FT_FONT
CAIRO_HAS_FC_FONT
cairo_ft_font_face_create_for_ft_face
cairo_ft_font_face_create_for_pattern
cairo_ft_font_options_substitute
cairo_ft_scaled_font_lock_face
cairo_ft_scaled_font_unlock_face
cairo_ft_synthesize_t
cairo_ft_font_face_get_synthesize
cairo_ft_font_face_set_synthesize
cairo_ft_font_face_unset_synthesize
</SECTION>

<SECTION>
<FILE>cairo-win32-fonts</FILE>
CAIRO_HAS_WIN32_FONT
cairo_win32_font_face_create_for_logfontw
cairo_win32_font_face_create_for_hfont
cairo_win32_font_face_create_for_logfontw_hfont
cairo_win32_scaled_font_select_font
cairo_win32_scaled_font_done_font
cairo_win32_scaled_font_get_metrics_factor
cairo_win32_scaled_font_get_logical_to_device
cairo_win32_scaled_font_get_device_to_logical
</SECTION>

<SECTION>
<FILE>cairo-dwrite-fonts</FILE>
CAIRO_HAS_DWRITE_FONT
cairo_dwrite_font_face_create_for_dwrite_fontface
cairo_dwrite_font_face_get_rendering_params
cairo_dwrite_font_face_set_rendering_params
cairo_dwrite_font_face_get_measuring_mode
cairo_dwrite_font_face_set_measuring_mode
</SECTION>

<SECTION>
<FILE>cairo-quartz-fonts</FILE>
CAIRO_HAS_QUARTZ_FONT
cairo_quartz_font_face_create_for_cgfont
cairo_quartz_font_face_create_for_atsu_font_id
</SECTION>

<SECTION>
<FILE>cairo-user-fonts</FILE>
CAIRO_HAS_USER_FONT
cairo_user_scaled_font_init_func_t
cairo_user_scaled_font_render_glyph_func_t
cairo_user_scaled_font_text_to_glyphs_func_t
cairo_user_scaled_font_unicode_to_glyph_func_t
cairo_user_font_face_create
cairo_user_font_face_set_init_func
cairo_user_font_face_get_init_func
cairo_user_font_face_set_render_glyph_func
cairo_user_font_face_get_render_glyph_func
cairo_user_font_face_set_render_color_glyph_func
cairo_user_font_face_get_render_color_glyph_func
cairo_user_font_face_set_unicode_to_glyph_func
cairo_user_font_face_get_unicode_to_glyph_func
cairo_user_font_face_set_text_to_glyphs_func
cairo_user_font_face_get_text_to_glyphs_func
cairo_user_scaled_font_get_foreground_marker
cairo_user_scaled_font_get_foreground_source
</SECTION>

<SECTION>
<FILE>cairo-image</FILE>
CAIRO_HAS_IMAGE_SURFACE
cairo_format_t
cairo_format_stride_for_width
cairo_image_surface_create
cairo_image_surface_create_for_data
cairo_image_surface_get_data
cairo_image_surface_get_format
cairo_image_surface_get_width
cairo_image_surface_get_height
cairo_image_surface_get_stride
</SECTION>

<SECTION>
<FILE>cairo-pdf</FILE>
CAIRO_HAS_PDF_SURFACE
CAIRO_PDF_OUTLINE_ROOT
cairo_pdf_outline_flags_t
cairo_pdf_metadata_t
cairo_pdf_surface_create
cairo_pdf_surface_create_for_stream
cairo_pdf_surface_restrict_to_version
cairo_pdf_version_t
cairo_pdf_get_versions
cairo_pdf_version_to_string
cairo_pdf_surface_set_size
cairo_pdf_surface_add_outline
cairo_pdf_surface_set_metadata
cairo_pdf_surface_set_custom_metadata
cairo_pdf_surface_set_page_label
cairo_pdf_surface_set_thumbnail_size
</SECTION>

<SECTION>
<FILE>cairo-png</FILE>
CAIRO_HAS_PNG_FUNCTIONS
cairo_image_surface_create_from_png
cairo_read_func_t
cairo_image_surface_create_from_png_stream
cairo_surface_write_to_png
cairo_write_func_t
cairo_surface_write_to_png_stream
</SECTION>

<SECTION>
<FILE>cairo-ps</FILE>
CAIRO_HAS_PS_SURFACE
cairo_ps_surface_create
cairo_ps_surface_create_for_stream
cairo_ps_surface_restrict_to_level
cairo_ps_level_t
cairo_ps_get_levels
cairo_ps_level_to_string
cairo_ps_surface_set_eps
cairo_ps_surface_get_eps
cairo_ps_surface_set_size
cairo_ps_surface_dsc_begin_setup
cairo_ps_surface_dsc_begin_page_setup
cairo_ps_surface_dsc_comment
</SECTION>

<SECTION>
<FILE>cairo-recording</FILE>
CAIRO_HAS_RECORDING_SURFACE
cairo_recording_surface_create
cairo_recording_surface_ink_extents
cairo_recording_surface_get_extents
</SECTION>

<SECTION>
<FILE>cairo-win32</FILE>
CAIRO_HAS_WIN32_SURFACE
cairo_win32_surface_create
cairo_win32_surface_create_with_dib
cairo_win32_surface_create_with_ddb
cairo_win32_surface_create_with_format
cairo_win32_printing_surface_create
cairo_win32_surface_get_dc
cairo_win32_surface_get_image
<SUBSECTION Private>
SB_NONE
SHADEBLENDCAPS
WIN32_FONT_LOGICAL_SCALE
cairo_win32_device_t
cairo_win32_display_surface_t
cairo_win32_printing_surface_t
cairo_win32_surface_t
to_win32_device
to_win32_device_from_surface
to_win32_display_surface
to_win32_printing_surface
to_win32_surface
</SECTION>

<SECTION>
<FILE>cairo-quartz</FILE>
CAIRO_HAS_QUARTZ_SURFACE
CAIRO_HAS_QUARTZ_IMAGE_SURFACE
cairo_quartz_surface_create
cairo_quartz_surface_create_for_cg_context
cairo_quartz_surface_get_cg_context
cairo_quartz_image_surface_create
cairo_quartz_image_surface_get_image
</SECTION>

<SECTION>
<FILE>cairo-xlib</FILE>
CAIRO_HAS_XLIB_SURFACE
CAIRO_HAS_XLIB_XCB_FUNCTIONS
cairo_xlib_surface_create
cairo_xlib_surface_create_for_bitmap
cairo_xlib_surface_set_size
cairo_xlib_surface_get_display
cairo_xlib_surface_get_screen
cairo_xlib_surface_set_drawable
cairo_xlib_surface_get_drawable
cairo_xlib_surface_get_visual
cairo_xlib_surface_get_width
cairo_xlib_surface_get_height
cairo_xlib_surface_get_depth
cairo_xlib_device_debug_cap_xrender_version
cairo_xlib_device_debug_get_precision
cairo_xlib_device_debug_set_precision
</SECTION>

<SECTION>
<FILE>cairo-xlib-xrender</FILE>
CAIRO_HAS_XLIB_XRENDER_SURFACE
cairo_xlib_surface_create_with_xrender_format
cairo_xlib_surface_get_xrender_format
</SECTION>

<SECTION>
<FILE>cairo-xcb</FILE>
CAIRO_HAS_XCB_SURFACE
CAIRO_HAS_XCB_SHM_FUNCTIONS
cairo_xcb_surface_create
cairo_xcb_surface_create_for_bitmap
cairo_xcb_surface_create_with_xrender_format
cairo_xcb_surface_set_size
cairo_xcb_surface_set_drawable
cairo_xcb_device_get_connection
cairo_xcb_device_debug_cap_xrender_version
cairo_xcb_device_debug_cap_xshm_version
cairo_xcb_device_debug_get_precision
cairo_xcb_device_debug_set_precision
</SECTION>

<SECTION>
<FILE>cairo-svg</FILE>
CAIRO_HAS_SVG_SURFACE
cairo_svg_surface_create
cairo_svg_surface_create_for_stream
cairo_svg_surface_get_document_unit
cairo_svg_surface_set_document_unit
cairo_svg_surface_restrict_to_version
cairo_svg_version_t
cairo_svg_get_versions
cairo_svg_version_to_string
cairo_svg_unit_t
</SECTION>

<SECTION>
<FILE>cairo-device</FILE>
cairo_device_t
cairo_device_reference
cairo_device_destroy
cairo_device_status
cairo_device_finish
cairo_device_flush
cairo_device_type_t
cairo_device_get_type
cairo_device_get_reference_count
cairo_device_set_user_data
cairo_device_get_user_data
cairo_device_acquire
cairo_device_release
cairo_device_observer_elapsed
cairo_device_observer_fill_elapsed
cairo_device_observer_glyphs_elapsed
cairo_device_observer_mask_elapsed
cairo_device_observer_paint_elapsed
cairo_device_observer_print
cairo_device_observer_stroke_elapsed
</SECTION>

<SECTION>
<FILE>cairo-surface</FILE>
CAIRO_HAS_MIME_SURFACE
CAIRO_MIME_TYPE_CCITT_FAX
CAIRO_MIME_TYPE_CCITT_FAX_PARAMS
CAIRO_MIME_TYPE_EPS
CAIRO_MIME_TYPE_EPS_PARAMS
CAIRO_MIME_TYPE_JBIG2
CAIRO_MIME_TYPE_JBIG2_GLOBAL
CAIRO_MIME_TYPE_JBIG2_GLOBAL_ID
CAIRO_MIME_TYPE_JP2
CAIRO_MIME_TYPE_JPEG
CAIRO_MIME_TYPE_PNG
CAIRO_MIME_TYPE_URI
CAIRO_MIME_TYPE_UNIQUE_ID
cairo_surface_t
cairo_content_t
cairo_surface_create_similar
cairo_surface_create_similar_image
cairo_surface_create_for_rectangle
cairo_surface_reference
cairo_surface_destroy
cairo_surface_status
cairo_surface_finish
cairo_surface_flush
cairo_surface_get_device
cairo_surface_get_font_options
cairo_surface_get_content
cairo_surface_mark_dirty
cairo_surface_mark_dirty_rectangle
cairo_surface_set_device_offset
cairo_surface_get_device_offset
cairo_surface_get_device_scale
cairo_surface_set_device_scale
cairo_surface_set_fallback_resolution
cairo_surface_get_fallback_resolution
cairo_surface_type_t
cairo_surface_get_type
cairo_surface_get_reference_count
cairo_surface_set_user_data
cairo_surface_get_user_data
cairo_surface_copy_page
cairo_surface_show_page
cairo_surface_has_show_text_glyphs
cairo_surface_set_mime_data
cairo_surface_get_mime_data
cairo_surface_supports_mime_type
cairo_surface_map_to_image
cairo_surface_unmap_image
</SECTION>

<SECTION>
<FILE>cairo-surface-observer</FILE>
CAIRO_HAS_OBSERVER_SURFACE
cairo_surface_create_observer
cairo_surface_observer_add_fill_callback
cairo_surface_observer_add_finish_callback
cairo_surface_observer_add_flush_callback
cairo_surface_observer_add_glyphs_callback
cairo_surface_observer_add_mask_callback
cairo_surface_observer_add_paint_callback
cairo_surface_observer_add_stroke_callback
cairo_surface_observer_callback_t
cairo_surface_observer_elapsed
cairo_surface_observer_mode_t
cairo_surface_observer_print
</SECTION>

<SECTION>
<FILE>cairo-version</FILE>
CAIRO_VERSION
CAIRO_VERSION_MAJOR
CAIRO_VERSION_MINOR
CAIRO_VERSION_MICRO
CAIRO_VERSION_STRING
CAIRO_VERSION_ENCODE
CAIRO_VERSION_STRINGIZE
cairo_version
cairo_version_string
<SUBSECTION Private>
CAIRO_VERSION_STRINGIZE_
</SECTION>

<SECTION>
<FILE>cairo-region</FILE>
cairo_region_t
cairo_region_create
cairo_region_create_rectangle
cairo_region_create_rectangles
cairo_region_copy
cairo_region_reference
cairo_region_destroy
cairo_region_status
cairo_region_get_extents
cairo_region_num_rectangles
cairo_region_get_rectangle
cairo_region_is_empty
cairo_region_contains_point
cairo_region_overlap_t
cairo_region_contains_rectangle
cairo_region_equal
cairo_region_translate
cairo_region_intersect
cairo_region_intersect_rectangle
cairo_region_subtract
cairo_region_subtract_rectangle
cairo_region_union
cairo_region_union_rectangle
cairo_region_xor
cairo_region_xor_rectangle
</SECTION>

<SECTION>
<FILE>cairo-pattern</FILE>
cairo_pattern_t
cairo_pattern_add_color_stop_rgb
cairo_pattern_add_color_stop_rgba
cairo_pattern_get_color_stop_count
cairo_pattern_get_color_stop_rgba
cairo_pattern_create_rgb
cairo_pattern_create_rgba
cairo_pattern_get_rgba
cairo_pattern_create_for_surface
cairo_pattern_get_surface
cairo_pattern_create_linear
cairo_pattern_get_linear_points
cairo_pattern_create_radial
cairo_pattern_get_radial_circles
cairo_pattern_create_mesh
cairo_mesh_pattern_begin_patch
cairo_mesh_pattern_end_patch
cairo_mesh_pattern_move_to
cairo_mesh_pattern_line_to
cairo_mesh_pattern_curve_to
cairo_mesh_pattern_set_control_point
cairo_mesh_pattern_set_corner_color_rgb
cairo_mesh_pattern_set_corner_color_rgba
cairo_mesh_pattern_get_patch_count
cairo_mesh_pattern_get_path
cairo_mesh_pattern_get_control_point
cairo_mesh_pattern_get_corner_color_rgba
cairo_pattern_reference
cairo_pattern_destroy
cairo_pattern_status
cairo_extend_t
cairo_pattern_set_extend
cairo_pattern_get_extend
cairo_filter_t
cairo_pattern_set_filter
cairo_pattern_get_filter
cairo_pattern_set_matrix
cairo_pattern_get_matrix
cairo_pattern_type_t
cairo_pattern_get_type
cairo_pattern_get_reference_count
cairo_pattern_set_user_data
cairo_pattern_get_user_data
cairo_dither_t
cairo_pattern_set_dither
cairo_pattern_get_dither
</SECTION>

<SECTION>
<FILE>cairo-raster-source</FILE>
cairo_pattern_create_raster_source
cairo_raster_source_pattern_set_callback_data
cairo_raster_source_pattern_get_callback_data
cairo_raster_source_pattern_set_acquire
cairo_raster_source_pattern_get_acquire
cairo_raster_source_pattern_set_snapshot
cairo_raster_source_pattern_get_snapshot
cairo_raster_source_pattern_set_copy
cairo_raster_source_pattern_get_copy
cairo_raster_source_pattern_set_finish
cairo_raster_source_pattern_get_finish
cairo_raster_source_acquire_func_t
cairo_raster_source_release_func_t
cairo_raster_source_snapshot_func_t
cairo_raster_source_copy_func_t
cairo_raster_source_finish_func_t
</SECTION>

<SECTION>
<FILE>cairo-tag</FILE>
CAIRO_TAG_DEST
CAIRO_TAG_LINK
CAIRO_TAG_CONTENT
CAIRO_TAG_CONTENT_REF
cairo_tag_begin
cairo_tag_end
</SECTION>

<SECTION>
<FILE>cairo-matrix</FILE>
cairo_matrix_t
cairo_matrix_init
cairo_matrix_init_identity
cairo_matrix_init_translate
cairo_matrix_init_scale
cairo_matrix_init_rotate
cairo_matrix_translate
cairo_matrix_scale
cairo_matrix_rotate
cairo_matrix_invert
cairo_matrix_multiply
cairo_matrix_transform_distance
cairo_matrix_transform_point
</SECTION>

<SECTION>
<FILE>cairo-status</FILE>
cairo_status_t
cairo_status_to_string
cairo_debug_reset_static_data
</SECTION>

<SECTION>
<FILE>cairo-font-face</FILE>
cairo_font_face_t
cairo_font_face_reference
cairo_font_face_destroy
cairo_font_face_status
cairo_font_type_t
cairo_font_face_get_type
cairo_font_face_get_reference_count
cairo_font_face_set_user_data
cairo_font_face_get_user_data
</SECTION>

<SECTION>
<FILE>cairo-scaled-font</FILE>
cairo_scaled_font_t
cairo_scaled_font_create
cairo_scaled_font_reference
cairo_scaled_font_destroy
cairo_scaled_font_status
cairo_font_extents_t
cairo_scaled_font_extents
cairo_text_extents_t
cairo_scaled_font_text_extents
cairo_scaled_font_glyph_extents
cairo_scaled_font_text_to_glyphs
cairo_scaled_font_get_font_face
cairo_scaled_font_get_font_options
cairo_scaled_font_get_font_matrix
cairo_scaled_font_get_ctm
cairo_scaled_font_get_scale_matrix
cairo_scaled_font_get_type
cairo_scaled_font_get_reference_count
cairo_scaled_font_set_user_data
cairo_scaled_font_get_user_data
</SECTION>

<SECTION>
<FILE>cairo-font-options</FILE>
cairo_font_options_t
cairo_font_options_create
cairo_font_options_copy
cairo_font_options_destroy
cairo_font_options_status
cairo_font_options_merge
cairo_font_options_hash
cairo_font_options_equal
cairo_font_options_set_antialias
cairo_font_options_get_antialias
cairo_subpixel_order_t
cairo_font_options_set_subpixel_order
cairo_font_options_get_subpixel_order
cairo_hint_style_t
cairo_font_options_set_hint_style
cairo_font_options_get_hint_style
cairo_hint_metrics_t
cairo_font_options_set_hint_metrics
cairo_font_options_get_hint_metrics
cairo_font_options_get_variations
cairo_font_options_set_variations
cairo_color_mode_t
cairo_font_options_set_color_mode
cairo_font_options_get_color_mode
CAIRO_COLOR_PALETTE_DEFAULT
cairo_font_options_set_color_palette
cairo_font_options_get_color_palette
cairo_font_options_set_custom_palette_color
cairo_font_options_get_custom_palette_color
</SECTION>

<SECTION>
<FILE>cairo-types</FILE>
cairo_bool_t
cairo_user_data_key_t
cairo_destroy_func_t
cairo_rectangle_int_t
</SECTION>

<SECTION>
<FILE>cairo-transforms</FILE>
cairo_translate
cairo_scale
cairo_rotate
cairo_transform
cairo_set_matrix
cairo_get_matrix
cairo_identity_matrix
cairo_user_to_device
cairo_user_to_device_distance
cairo_device_to_user
cairo_device_to_user_distance
</SECTION>


<SECTION>
<FILE>cairo-paths</FILE>
cairo_path_t
cairo_path_data_t
cairo_path_data_type_t
cairo_copy_path
cairo_copy_path_flat
cairo_path_destroy
cairo_append_path
cairo_has_current_point
cairo_get_current_point
cairo_new_path
cairo_new_sub_path
cairo_close_path
cairo_arc
cairo_arc_negative
cairo_curve_to
cairo_line_to
cairo_move_to
cairo_rectangle
cairo_glyph_path
cairo_text_path
cairo_rel_curve_to
cairo_rel_line_to
cairo_rel_move_to
cairo_path_extents
</SECTION>

<SECTION>
<FILE>cairo-text</FILE>
cairo_glyph_t
cairo_font_slant_t
cairo_font_weight_t
cairo_text_cluster_t
cairo_text_cluster_flags_t
cairo_select_font_face
cairo_set_font_size
cairo_set_font_matrix
cairo_get_font_matrix
cairo_set_font_options
cairo_get_font_options
cairo_set_font_face
cairo_get_font_face
cairo_set_scaled_font
cairo_get_scaled_font
cairo_show_text
cairo_show_glyphs
cairo_show_text_glyphs
cairo_font_extents
cairo_text_extents
cairo_glyph_extents
cairo_toy_font_face_create
cairo_toy_font_face_get_family
cairo_toy_font_face_get_slant
cairo_toy_font_face_get_weight
cairo_glyph_allocate
cairo_glyph_free
cairo_text_cluster_allocate
cairo_text_cluster_free
</SECTION>

<SECTION>
<FILE>cairo</FILE>
cairo_t
cairo_create
cairo_reference
cairo_destroy
cairo_status
cairo_save
cairo_restore
cairo_get_target
cairo_push_group
cairo_push_group_with_content
cairo_pop_group
cairo_pop_group_to_source
cairo_get_group_target
cairo_set_source_rgb
cairo_set_source_rgba
cairo_set_source
cairo_set_source_surface
cairo_get_source
cairo_antialias_t
cairo_set_antialias
cairo_get_antialias
cairo_set_dash
cairo_get_dash_count
cairo_get_dash
cairo_fill_rule_t
cairo_set_fill_rule
cairo_get_fill_rule
cairo_line_cap_t
cairo_set_line_cap
cairo_get_line_cap
cairo_line_join_t
cairo_set_line_join
cairo_get_line_join
cairo_set_line_width
cairo_get_line_width
cairo_set_miter_limit
cairo_get_miter_limit
cairo_operator_t
cairo_set_operator
cairo_get_operator
cairo_set_tolerance
cairo_get_tolerance
cairo_clip
cairo_clip_preserve
cairo_clip_extents
cairo_in_clip
cairo_reset_clip
cairo_rectangle_t
cairo_rectangle_list_t
cairo_rectangle_list_destroy
cairo_copy_clip_rectangle_list
cairo_fill
cairo_fill_preserve
cairo_fill_extents
cairo_in_fill
cairo_mask
cairo_mask_surface
cairo_paint
cairo_paint_with_alpha
cairo_stroke
cairo_stroke_preserve
cairo_stroke_extents
cairo_in_stroke
cairo_copy_page
cairo_show_page
cairo_get_reference_count
cairo_set_user_data
cairo_get_user_data
cairo_set_hairline
cairo_get_hairline
<SUBSECTION Private>
cairo_public
CAIRO_BEGIN_DECLS
CAIRO_END_DECLS
cairo_current_font_extents
cairo_get_font_extents
cairo_current_operator
cairo_current_tolerance
cairo_current_point
cairo_current_fill_rule
cairo_current_line_width
cairo_current_line_cap
cairo_current_line_join
cairo_current_miter_limit
cairo_current_matrix
cairo_current_target_surface
cairo_get_status
cairo_concat_matrix
cairo_scale_font
cairo_select_font
cairo_transform_font
cairo_transform_point
cairo_transform_distance
cairo_inverse_transform_point
cairo_inverse_transform_distance
cairo_init_clip
cairo_surface_create_for_image
cairo_default_matrix
cairo_matrix_set_affine
cairo_matrix_set_identity
cairo_pattern_add_color_stop
cairo_set_rgb_color
cairo_set_pattern
cairo_xlib_surface_create_for_pixmap_with_visual
cairo_xlib_surface_create_for_window_with_visual
cairo_xcb_surface_create_for_pixmap_with_visual
cairo_xcb_surface_create_for_window_with_visual
cairo_ps_surface_set_dpi
cairo_pdf_surface_set_dpi
cairo_svg_surface_set_dpi
cairo_current_path
cairo_current_path_flat
cairo_get_path
cairo_get_path_flat
cairo_set_alpha
cairo_show_surface
cairo_copy
cairo_surface_set_repeat
cairo_surface_set_matrix
cairo_surface_get_matrix
cairo_surface_set_filter
cairo_surface_get_filter
cairo_matrix_create
cairo_matrix_destroy
cairo_matrix_copy
cairo_matrix_get_affine
cairo_set_target_surface
cairo_set_target_image
cairo_set_target_pdf
cairo_set_target_png
cairo_set_target_ps
cairo_set_target_quartz
cairo_set_target_win32
cairo_set_target_xcb
cairo_set_target_drawable
cairo_get_status_string
cairo_status_string
CAIRO_FONT_TYPE_ATSUI
cairo_atsui_font_face_create_for_atsu_font_id
CAIRO_HAS_GOBJECT_FUNCTIONS
</SECTION>

<SECTION>
<FILE>cairo-script</FILE>
CAIRO_HAS_SCRIPT_SURFACE
cairo_script_create
cairo_script_create_for_stream
cairo_script_from_recording_surface
cairo_script_get_mode
cairo_script_mode_t
cairo_script_set_mode
cairo_script_surface_create
cairo_script_surface_create_for_target
cairo_script_write_comment
</SECTION>

<SECTION>
<FILE>cairo-tee</FILE>
CAIRO_HAS_TEE_SURFACE
cairo_tee_surface_create
cairo_tee_surface_add
cairo_tee_surface_index
cairo_tee_surface_remove
</SECTION>
