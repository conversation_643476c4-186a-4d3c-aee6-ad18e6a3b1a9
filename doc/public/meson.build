gnome = import('gnome')

ignore_headers = [
  # Sub-directories
  'win32',

  # Internal headers
  'cairoint.h',
  'cairo-features.h',
  'cairo-features-win32.h',

  # Inlined API
  'cairo-box-inline.h',
  'cairo-clip-inline.h',
  'cairo-combsort-inline.h',
  'cairo-contour-inline.h',
  'cairo-error-inline.h',
  'cairo-image-surface-inline.h',
  'cairo-line-inline.h',
  'cairo-list-inline.h',
  'cairo-pattern-inline.h',
  'cairo-recording-surface-inline.h',
  'cairo-surface-inline.h',
  'cairo-surface-observer-inline.h',
  'cairo-surface-snapshot-inline.h',
  'cairo-surface-subsurface-inline.h',

  # Private headers
  'cairo-analysis-surface-private.h',
  'cairo-arc-private.h',
  'cairo-array-private.h',
  'cairo-atomic-private.h',
  'cairo-backend-private.h',
  'cairo-boxes-private.h',
  'cairo-cache-private.h',
  'cairo-clip-private.h',
  'cairo-compiler-private.h',
  'cairo-composite-rectangles-private.h',
  'cairo-compositor-private.h',
  'cairo-contour-private.h',
  'cairo-damage-private.h',
  'cairo-default-context-private.h',
  'cairo-deprecated.h',
  'cairo-device-private.h',
  'cairo-error-private.h',
  'cairo-fixed-private.h',
  'cairo-fixed-type-private.h',
  'cairo-fontconfig-private.h',
  'cairo-freed-pool-private.h',
  'cairo-freelist-private.h',
  'cairo-freelist-type-private.h',
  'cairo-ft-private.h',
  'cairo-gstate-private.h',
  'cairo-hash-private.h',
  'cairo-image-info-private.h',
  'cairo-image-surface-private.h',
  'cairo-line-private.h',
  'cairo-list-private.h',
  'cairo-malloc-private.h',
  'cairo-mempool-private.h',
  'cairo-mutex-impl-private.h',
  'cairo-mutex-list-private.h',
  'cairo-mutex-private.h',
  'cairo-mutex-type-private.h',
  'cairo-output-stream-private.h',
  'cairo-paginated-private.h',
  'cairo-paginated-surface-private.h',
  'cairo-path-fixed-private.h',
  'cairo-path-private.h',
  'cairo-pattern-private.h',
  'cairo-pdf-operators-private.h',
  'cairo-pdf-shading-private.h',
  'cairo-pdf-surface-private.h',
  'cairo-pixman-private.h',
  'cairo-private.h',
  'cairo-ps-surface-private.h',
  'cairo-quartz-private.h',
  'cairo-recording-surface-private.h',
  'cairo-reference-count-private.h',
  'cairo-region-private.h',
  'cairo-rtree-private.h',
  'cairo-scaled-font-private.h',
  'cairo-scaled-font-subsets-private.h',
  'cairo-script-private.h',
  'cairo-slope-private.h',
  'cairo-spans-compositor-private.h',
  'cairo-spans-private.h',
  'cairo-stroke-dash-private.h',
  'cairo-surface-backend-private.h',
  'cairo-surface-clipper-private.h',
  'cairo-surface-fallback-private.h',
  'cairo-surface-observer-private.h',
  'cairo-surface-offset-private.h',
  'cairo-surface-private.h',
  'cairo-surface-snapshot-private.h',
  'cairo-surface-subsurface-private.h',
  'cairo-surface-wrapper-private.h',
  'cairo-svg-surface-private.h',
  'cairo-tag-attributes-private.h',
  'cairo-tag-stack-private.h',
  'cairo-time-private.h',
  'cairo-traps-private.h',
  'cairo-tristrip-private.h',
  'cairo-truetype-subset-private.h',
  'cairo-type1-private.h',
  'cairo-type3-glyph-surface-private.h',
  'cairo-types-private.h',
  'cairo-user-font-private.h',
  'cairo-wideint-private.h',
  'cairo-wideint-type-private.h',
  'cairo-xcb-private.h',
  'cairo-xlib-private.h',
  'cairo-xlib-surface-private.h',
  'cairo-xlib-xrender-private.h',
  'test-compositor-surface-private.h',
]

ignore_decorators = [
  'cairo_public',
  'cairo_private',
]

docpath = get_option('prefix') / get_option('datadir') / 'gtk-doc/html'
version_conf = configuration_data()
version_conf.set('CAIRO_VERSION', meson.project_version())
configure_file(
  input: 'version.xml.in',
  output: 'version.xml',
  configuration: version_conf
)

gnome.gtkdoc('cairo',
  main_xml: 'cairo-docs.xml',
  namespace: 'cairo',
  mode: 'sgml',
  src_dir: doc_srcdir,
  dependencies: libcairo_dep,
  scan_args: [
    '--ignore-decorators=' + '|'.join(ignore_decorators),
    '--ignore-headers=' + ' '.join(ignore_headers),
  ],
  mkdb_args: ['--source-suffixes=h,c,cpp'],
  content_files: [
    'language-bindings.xml',
  ],
  expand_content_files: [
    'language-bindings.xml',
  ],
  html_assets: [
  ],
  fixxref_args: [
    '--html-dir=' + docpath,
  ],
  install: true,
  check: true,
)
