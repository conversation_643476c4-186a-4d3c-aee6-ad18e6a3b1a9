<?xml version="1.0" ?>
<svg width="1024" height="768"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    xmlns:ss="http://www.svgslides.org/svgslides0.1"
    fill="black">


	<defs id="cairo-artwork_defs">
		<g id="hacker_emblem">
			<!-- Note: This is similar though not identical to <PERSON>'s SVG version
				of the hacker emblem (http://www.catb.org/hacker-emblem/glider.svg) -->
			<g id="hacker_emblem_grid" fill="white" stroke="none">
				<!-- Outside: Top, Right, Bottom, Left -->
				<rect x="-2.95" y="-3.05" width="6"   height="0.1" />
				<rect x="2.95"  y="-2.95" width="0.1" height="6" />
				<rect x="-3.05" y="2.95"  width="6"   height="0.1" />
				<rect x="-3.05" y="-3.05" width="0.1" height="6" />
				<!-- Vertical: Left, Right -->
				<rect x="-1.05" y="-2.95" width="0.1" height="5.9" />
				<rect x="0.95"  y="-2.95" width="0.1" height="5.9" />
				<!-- Horizontal: TopLeft, TopMiddle, TopRight -->
				<rect x="-2.95" y="-1.05" width="1.9" height="0.1" />
				<rect x="-0.95" y="-1.05" width="1.9" height="0.1" />
				<rect x="1.05"  y="-1.05" width="1.9" height="0.1" />
				<!-- Horizontal: BottomLeft, BottomMiddle, BottomRight -->
				<rect x="-2.95" y="0.95"  width="1.9" height="0.1" />
				<rect x="-0.95" y="0.95"  width="1.9" height="0.1" />
				<rect x="1.05"  y="0.95"  width="1.9" height="0.1" />
			</g>
			<g id="hacker_emblem_dots" fill="white">
				<circle cx="0"  cy="-2" r="0.7" />
				<circle cx="2"  cy="0"  r="0.7" />
				<circle cx="-2" cy="2"  r="0.7" />
				<circle cx="0"  cy="2"  r="0.7" />
				<circle cx="2"  cy="2"  r="0.7" />
			</g>
		</g>
		<g id="scarab" fill="#3B80AE">
			<g transform="translate(-150, -170)">
			<path id="scarab_head" d="M205.599,94.567c0-11.668-24.914-21.129-55.628-21.129
				c-30.723,0-55.624,9.46-55.624,21.129c0,10.203,24.901,7.346,55.624,7.346C180.685,101.913,205.599,104.233,205.599,94.567z"/>
			<path id="scarab_torso" d="M136.423,161.506c0,0,12.751,12.577,13.547,13.362
				c2.262-2.232,13.545-13.362,13.545-13.362c7.135-7.036,87.111-6.399,91.066-6.363c-0.469-6.298-1.254-12.472-2.325-18.519
				c-15.183-19.279-42.811-32.225-74.485-32.225h-55.518c-31.745,0-59.439,13.011-74.598,32.37c-1.054,6-1.829,12.128-2.296,18.374
				C49.321,155.106,129.288,154.47,136.423,161.506z"/>
			<path id="scarab_spine" d="M149.97,301.187c2.005-24.729,8.386-103.483,8.405-103.721
				c-0.09-0.219-6.478-15.578-8.405-20.214c-1.936,4.655-8.316,19.995-8.408,20.214C141.582,197.704,147.965,276.458,149.97,301.187z"/>
			<path id="scarab_wing_left" d="M140.403,197.149l8.862-21.31l-13.686-13.499
				c-5.65-5.573-67.074-6.235-90.259-6.019l-0.006-0.622c-0.154,2.144-0.271,4.302-0.35,6.475
				c-0.076,2.207,10.392,4.706,10.392,6.717c0,2.319-10.457,5.084-10.359,7.631c2.993,73.349,48.53,131.631,104.372,132.048
				l-9.02-111.29L140.403,197.149z"/>
			<path id="scarab_wing_right" d="M244.585,168.891c0-2.011,10.467-4.506,10.391-6.715
				c-0.079-2.174-0.195-4.332-0.351-6.479l-0.004,0.624c-23.186-0.216-84.608,0.445-90.26,6.017l-13.688,13.502l8.915,21.438
				l-9.017,111.29c55.854-0.417,101.378-58.698,104.373-132.049C255.04,173.976,244.585,171.209,244.585,168.891z"/>
			<path id="scarab_leg_front_left" d="M44.506,141.12c-4.135-0.856-4.895-1.54-7.935-2.92
				c-9.59-3.364-10.376-5.481-16.08-11.86c-7.426-8.306-12.661-20.142-17.1-29.463c-3.576-7.525-3.984-16.409-2.86-24.273
				c0.991-6.935,7.144-12.869,12.074-18.92c5.844-7.191,10.356-14.822,17.924-21.354c7.736-6.682,23.203-9.809,26.168-19.648
				C57.86,8.819,54.334,1.766,61.482,0c-0.366,4.703,3.639,8.477,2.397,13.575c-1.129,4.627-4.368,5.811-9.611,9.099
				c-7.564,4.746-18.366,8.779-24.748,13.965c-7.175,5.827-4.369,13.771-10.569,20.057c-2.001,2.03-7.901,4.706-9.137,6.83
				c-1.861,3.199-0.297,9.572-0.116,13.12c0.425,8.284,5.588,14.244,9.555,22.045c4.152,8.141,6.429,15.409,13.411,22.519
				c4.183,4.262,11.429,4.802,16.21,10.647l-3.555,4.186L44.506,141.12z"/>
			<path id="scarab_leg_middle_left" d="M43.94,191.922l-0.809-7.346
				c-9.506-4.579-10.339-9.772-20.738-12.466c-23.728-6.151-21.361,11.25-15.532,26.373c5.676,14.726,8.237,30.23,14.345,44.795
				c2.805,6.688,6.919,13.213,14.298,15.127c0.372-8.435-0.917-10.651-6.113-16.919c-4.395-5.293-3.326-12.548-6.072-18.504
				c-3.581-7.804-4.196-15.646-7.279-23.502c-1.363-3.479-8.33-13.966-6.452-17.861c3.183-6.603,9.178-0.083,12.179,2.077
				c4.218,3.036,6.467,2.223,11.681,2.898C34.041,186.673,37.005,188.756,43.94,191.922z"/>
			<path id="scarab_leg_back_left" d="M65.839,257.063l-2.771-4.837
				c-6.68,8.928-6.993,16.228-10.056,23.347c-5.277,12.263-0.157,28.851,9.854,37.676c6.052,5.375,15.907,9.618,23.122,13.136
				c10.035,4.892,20.113,11.286,31.336,13.396c2.482,0.466,8.798,1.295,6.693-3.522c-0.975-2.237-8.091-4.591-10.146-5.734
				c-8.312-4.623-16.377-10.524-24.142-16.176c-9.498-6.862-20.843-11.186-28.311-20.684c-3.054-3.885-3.544-4.922-2.816-9.39
				c0.693-4.263,1.344-9.174,2.241-13.439C61.855,266.029,63.274,261.378,65.839,257.063z"/>
			<path id="scarab_leg_front_right" d="M255.487,141.12c4.134-0.856,4.896-1.54,7.936-2.92
				c9.583-3.364,10.369-5.481,16.071-11.86c7.428-8.306,12.661-20.142,17.115-29.463c3.574-7.525,3.983-16.409,2.86-24.273
				c-0.992-6.935-7.157-12.869-12.087-18.92c-5.843-7.191-10.356-14.822-17.919-21.354c-7.735-6.682-23.202-9.809-26.167-19.648
				C242.135,8.819,245.66,1.766,238.511,0c0.366,4.703-3.637,8.477-2.396,13.575c1.131,4.627,4.368,5.811,9.611,9.099
				c7.563,4.746,18.367,8.779,24.747,13.965c7.17,5.827,4.362,13.771,10.563,20.057c2.001,2.03,7.901,4.706,9.139,6.83
				c1.859,3.199,0.295,9.572,0.113,13.12c-0.424,8.284-5.588,14.244-9.553,22.045c-4.152,8.141-6.431,15.409-13.404,22.519
				c-4.184,4.262-11.429,4.802-16.211,10.647l3.556,4.186L255.487,141.12z"/>
			<path id="scarab_leg_middle_right" d="M256.053,191.922l0.81-7.346
				c9.507-4.579,10.34-9.772,20.73-12.466c23.741-6.151,21.374,11.25,15.534,26.373c-5.676,14.726-8.238,30.23-14.347,44.795
				c-2.804,6.688-6.911,13.213-14.291,15.127c-0.371-8.435,0.918-10.651,6.113-16.919c4.39-5.293,3.319-12.548,6.066-18.504
				c3.58-7.804,4.197-15.646,7.278-23.502c1.363-3.479,8.33-13.966,6.453-17.861c-3.184-6.603-9.179-0.083-12.181,2.077
				c-4.217,3.036-6.458,2.223-11.672,2.898C265.951,186.673,262.986,188.756,256.053,191.922z"/>
			<path id="scarab_leg_back_right" d="M234.155,257.063l2.771-4.837
				c6.679,8.928,6.991,16.228,10.057,23.347c5.274,12.263,0.154,28.851-9.854,37.676c-6.055,5.375-15.903,9.618-23.117,13.136
				c-10.034,4.892-20.127,11.286-31.351,13.396c-2.481,0.466-8.789,1.295-6.691-3.522c0.976-2.237,8.092-4.591,10.146-5.734
				c8.312-4.623,16.392-10.524,24.155-16.176c9.498-6.862,20.838-11.186,28.305-20.684c3.055-3.885,3.543-4.922,2.818-9.39
				c-0.696-4.263-1.346-9.174-2.244-13.439C238.137,266.029,236.718,261.378,234.155,257.063z"/>
			</g>
		</g>
		<radialGradient id="gradient_radial_dung"
				cx="0" cy="0" r="60"
				fx="0" fy="0" gradientUnits="userSpaceOnUse"
		>
			<stop offset="0"    stop-color="#9a9a9a" />
			<stop offset="0.70" stop-color="#bababa" />
			<stop offset="0.95" stop-color="#FFFFFF" />
		</radialGradient>
		<g id="dung">
			<circle cx="0" cy="0" r="60" fill="url(#gradient_radial_dung)" />
			<g transform="translate(-61, -61)">
				<!-- rough equivalent: <circle cx="0" cy="0" r="60" stroke="#8a8a8a" stroke-width="2" /> -->
				<path fill="#8a8a8a" d="M0,61c0,33.636,27.364,61,61,61s61-27.364,61-61S94.636,0,61,0S0,27.364,0,61z
							M2,61C2,28.467,28.467,2,61,2c32.532,0,59,26.467,59,59c0,32.533-26.468,59-59,59C28.467,120,2,93.533,2,61z"/>
			</g>
			<use xlink:href="#hacker_emblem" x="0" y="0" transform="scale(9)" />
		</g>

		<!-- scarab dimensions: 300x340 -->
		<!-- dung dimensions: 120x120 (radius: 60) -->
		<!-- scarab and dung dimensions: 300x400 -->

		<g id="cairo_logo">
			<!-- dimensions: 300x400, centered -->
			<!-- The logo (scarab and dung), with the center-point of the bounding box at (0,0) -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(0, -140)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0, 30)" />
		</g>
		<g id="cairo_logo_dung-centered">
			<!-- The logo (scarab and dung), with the dung at (0,0), the scarab below -->
			<use xlink:href="#dung" x="0" y="0" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0,170)" />
		</g>
		<g id="cairo_logo_scarab-centered">
			<!-- The logo (scarab and dung), with the scarab's rotational center at (0,0), the dung above -->
			<!-- The scarab's rotational center in this case is not the center of its bounding box,
				but is calculated to be the intersection-point of the torso, spine and wings -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(0, -175.85)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0, -5.85)" />
		</g>
		<g id="cairo_logo_top-centered">
			<!-- The logo (scarab and dung), with the top-center point of the bounding box at (0,0) -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(0, 60)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0, 230)" /><!-- (0,170+60) -->
		</g>
		<g id="cairo_logo_bottom-centered">
			<!-- The logo (scarab and dung), with the bottom-center point of the bounding box at (0,0) -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(0, -340)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0, -170)" />
		</g>
		<g id="cairo_logo_right-centered">
			<!-- The logo (scarab and dung), with the right-center point of the bounding box at (0,0) -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(-150, -140)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(-150, 30)" />
		</g>
		<g id="cairo_logo_left-centered">
			<!-- The logo (scarab and dung), with the left-center point of the bounding box at (0,0) -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(150, -140)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(150, 30)" />
		</g>
		<g id="cairo_logo_topleft-centered">
			<!-- The logo (scarab and dung), with the top-left point of the bounding box at (0,0) -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(150, 60)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(150, 230)" /><!-- (150, 170+60) -->
		</g>
		<g id="cairo_logo_topright-centered">
			<!-- The logo (scarab and dung), with the top-right point of the bounding box at (0,0) -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(-150, 60)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(-150, 230)" /><!-- (-150,170+60) -->
		</g>
		<g id="cairo_logo_bottomleft-centered">
			<!-- The logo (scarab and dung), with the bottom-left point of the bounding box at (0,0) -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(150, -340)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(150, -170)" />
		</g>
		<g id="cairo_logo_bottomright-centered">
			<!-- The logo (scarab and dung), with the bottom-right point of the bounding box at (0,0) -->
			<use xlink:href="#dung" x="0" y="0" transform="translate(-150, -340)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(-150, -170)" />
		</g>

		<g id="cairo_text" transform="translate(0,-97)">
		    <g transform="scale(0.1484,0.1484)"> <g transform="translate(-1139,-208.5)">
			 <!-- 63 (c), advance 444, 0 horiBearing 38,522 -->
			 <path transform="translate(65,0)" d="
			       M 412, 433 
			       C 385, 422 336, 413 298, 413 
			       C 142, 413 38, 525 38, 680 
			       C 38, 826 144, 947 298, 947 
			       C 332, 947 377, 944 416, 926 
			       L 409, 842 
			       C 380, 861 340, 871 308, 871 
			       C 187, 871 138, 771 138, 680 
			       C 138, 583 197, 489 302, 489 
			       C 332, 489 368, 496 404, 511 
			       L 412, 433 " />
			 <!-- 61 (a), advance 556, 0 horiBearing 46,522 -->
			 <path transform="translate(486.75,0)" d="
			       M 109, 541 
			       C 147, 509 204, 489 257, 489 
			       C 351, 489 383, 534 383, 622 
			       C 346, 620 320, 620 283, 620 
			       C 186, 620 46, 660 46, 788 
			       C 46, 899 123, 947 233, 947 
			       C 319, 947 369, 900 391, 869 
			       L 393, 869 
			       L 393, 935 
			       L 481, 935 
			       C 479, 920 477, 893 477, 835 
			       L 477, 624 
			       C 477, 485 418, 413 272, 413 
			       C 207, 413 151, 433 104, 461 
			       L 109, 541 
			       M 383, 737 
			       C 383, 813 334, 871 241, 871 
			       C 198, 871 146, 842 146, 788 
			       C 146, 698 272, 690 323, 690 
			       C 343, 690 363, 692 383, 692 
			       L 383, 737 " />
			 <!-- 69 (i), advance 278, 0 horiBearing 86,730 -->
			 <path transform="translate(1000,0)" d="
			       M 92, 935 
			       L 186, 935 
			       L 186, 425 
			       L 92, 425 
			       L 92, 935 
			       M 88, 261
			       A 51, 51 0 1 1 190,261
			       A 51, 51 0 1 1 88,261" />
			 <!-- 72 (r), advance 389, 0 horiBearing 80,522 -->
			 <path transform="translate(1234.25,0)" d="
			       M 80, 935 
			       L 174, 935 
			       L 174, 703 
			       C 174, 575 229, 495 313, 495 
			       C 329, 495 348, 497 365, 504 
			       L 365, 420 
			       C 345, 416 331, 413 303, 413 
			       C 249, 413 195, 451 170, 504 
			       L 168, 504 
			       L 168, 425 
			       L 80, 425 
			       L 80, 935 " />
			 <!-- 6f (o), advance 611, 0 horiBearing 46,522 -->
			 <path transform="translate(1610,0)" d="
			       M 46, 680 
			       C 46, 826 152, 947 306, 947 
			       C 459, 947 565, 826 565, 680 
			       C 565, 525 461, 413 306, 413 
			       C 150, 413 46, 525 46, 680 
			       M 146, 680 
			       C 146, 583 205, 489 306, 489 
			       C 406, 489 465, 583 465, 680 
			       C 465, 771 416, 871 306, 871 
			       C 195, 871 146, 771 146, 680 " />
			 <!-- bounds: 38, 205 <-> 2232, 947 -->
		    </g> </g>
		</g>

		<!-- scaled by 0.72, shifted around to hit pixel boundaries -->
		<g id="cairo_text_small_spaced" transform="translate(0,-71)">
		    <g transform="scale(0.085,0.085)"> <g transform="translate(-1139,-208.5)">
			 <!-- 63 (c), advance 444, 0 horiBearing 38,522 -->
			 <path transform="translate(-151,0)" d="
			       M 412, 433 
			       C 385, 422 336, 413 298, 413 
			       C 142, 413 38, 525 38, 680 
			       C 38, 826 144, 947 298, 947 
			       C 332, 947 377, 944 416, 926 
			       L 409, 842 
			       C 380, 861 340, 871 308, 871 
			       C 187, 871 138, 771 138, 680 
			       C 138, 583 197, 489 302, 489 
			       C 332, 489 368, 496 404, 511 
			       L 412, 433 " />
			 <!-- 61 (a), advance 556, 0 horiBearing 46,522 -->
			 <path transform="translate(379.5,0)" d="
			       M 109, 541 
			       C 147, 509 204, 489 257, 489 
			       C 351, 489 383, 534 383, 622 
			       C 346, 620 320, 620 283, 620 
			       C 186, 620 46, 660 46, 788 
			       C 46, 899 123, 947 233, 947 
			       C 319, 947 369, 900 391, 869 
			       L 393, 869 
			       L 393, 935 
			       L 481, 935 
			       C 479, 920 477, 893 477, 835 
			       L 477, 624 
			       C 477, 485 418, 413 272, 413 
			       C 207, 413 151, 433 104, 461 
			       L 109, 541 
			       M 383, 737 
			       C 383, 813 334, 871 241, 871 
			       C 198, 871 146, 842 146, 788 
			       C 146, 698 272, 690 323, 690 
			       C 343, 690 363, 692 383, 692 
			       L 383, 737 " />
			 <!-- 69 (i), advance 278, 0 horiBearing 86,730 -->
			 <path transform="translate(1000,0)" d="
			       M 92, 935 
			       L 186, 935 
			       L 186, 425 
			       L 92, 425 
			       L 92, 935 
			       M 88, 261
			       A 51, 51 0 1 1 190,261
			       A 51, 51 0 1 1 88,261" />
			 <!-- 72 (r), advance 389, 0 horiBearing 80,522 -->
			 <path transform="translate(1341.5,0)" d="
			       M 80, 935 
			       L 174, 935 
			       L 174, 703 
			       C 174, 575 229, 495 313, 495 
			       C 329, 495 348, 497 365, 504 
			       L 365, 420 
			       C 345, 416 331, 413 303, 413 
			       C 249, 413 195, 451 170, 504 
			       L 168, 504 
			       L 168, 425 
			       L 80, 425 
			       L 80, 935 " />
			 <!-- 6f (o), advance 611, 0 horiBearing 46,522 -->
			 <path transform="translate(1826,0)" d="
			       M 46, 680 
			       C 46, 826 152, 947 306, 947 
			       C 459, 947 565, 826 565, 680 
			       C 565, 525 461, 413 306, 413 
			       C 150, 413 46, 525 46, 680 
			       M 146, 680 
			       C 146, 583 205, 489 306, 489 
			       C 406, 489 465, 583 465, 680 
			       C 465, 771 416, 871 306, 871 
			       C 195, 871 146, 771 146, 680 " />
			 <!-- bounds: 38, 205 <-> 2232, 947 -->
		    </g> </g>
		</g>


		<!-- scaled by 0.72, shifted around to hit pixel boundaries -->
		<g id="cairo_text_small" transform="translate(0,-71)">
		    <g transform="scale(0.085,0.085)"> <g transform="translate(-1139,-208.5)">
			 <!-- 63 (c), advance 444, 0 horiBearing 38,522 -->
			 <path transform="translate(-151,0)" d="
			       M 412, 433 
			       C 385, 422 336, 413 298, 413 
			       C 142, 413 38, 525 38, 680 
			       C 38, 826 144, 947 298, 947 
			       C 332, 947 377, 944 416, 926 
			       L 409, 842 
			       C 380, 861 340, 871 308, 871 
			       C 187, 871 138, 771 138, 680 
			       C 138, 583 197, 489 302, 489 
			       C 332, 489 368, 496 404, 511 
			       L 412, 433 " />
			 <!-- 61 (a), advance 556, 0 horiBearing 46,522 -->
			 <path transform="translate(261.75,0)" d="
			       M 109, 541 
			       C 147, 509 204, 489 257, 489 
			       C 351, 489 383, 534 383, 622 
			       C 346, 620 320, 620 283, 620 
			       C 186, 620 46, 660 46, 788 
			       C 46, 899 123, 947 233, 947 
			       C 319, 947 369, 900 391, 869 
			       L 393, 869 
			       L 393, 935 
			       L 481, 935 
			       C 479, 920 477, 893 477, 835 
			       L 477, 624 
			       C 477, 485 418, 413 272, 413 
			       C 207, 413 151, 433 104, 461 
			       L 109, 541 
			       M 383, 737 
			       C 383, 813 334, 871 241, 871 
			       C 198, 871 146, 842 146, 788 
			       C 146, 698 272, 690 323, 690 
			       C 343, 690 363, 692 383, 692 
			       L 383, 737 " />
			 <!-- 69 (i), advance 278, 0 horiBearing 86,730 -->
			 <path transform="translate(764.75)" d="
			       M 92, 935 
			       L 186, 935 
			       L 186, 425 
			       L 92, 425 
			       L 92, 935 
			       M 88, 261
			       A 51, 51 0 1 1 190,261
			       A 51, 51 0 1 1 88,261" />
			 <!-- 72 (r), advance 389, 0 horiBearing 80,522 -->
			 <path transform="translate(988.5,0)" d="
			       M 80, 935 
			       L 174, 935 
			       L 174, 703 
			       C 174, 575 229, 495 313, 495 
			       C 329, 495 348, 497 365, 504 
			       L 365, 420 
			       C 345, 416 331, 413 303, 413 
			       C 249, 413 195, 451 170, 504 
			       L 168, 504 
			       L 168, 425 
			       L 80, 425 
			       L 80, 935 " />
			 <!-- 6f (o), advance 611, 0 horiBearing 46,522 -->
			 <path transform="translate(1355.5,0)" d="
			       M 46, 680 
			       C 46, 826 152, 947 306, 947 
			       C 459, 947 565, 826 565, 680 
			       C 565, 525 461, 413 306, 413 
			       C 150, 413 46, 525 46, 680 
			       M 146, 680 
			       C 146, 583 205, 489 306, 489 
			       C 406, 489 465, 583 465, 680 
			       C 465, 771 416, 871 306, 871 
			       C 195, 871 146, 771 146, 680 " />
			 <!-- bounds: 38, 205 <-> 2232, 947 -->
		    </g> </g>
		</g>

		<g id="cairo_logo_text_small">
			<!-- The logo on the left, the text 'cairo' on the right -->
			<use xlink:href="#cairo_logo_bottomleft-centered" transform="translate(0, 78), scale(0.1944)" />
			<use xlink:href="#cairo_text_small" transform="translate(175,82)"/>
		</g>

		<g id="cairo_logo_with_text">
			<!-- The logo (scarab and dung), with the text 'cairo' below, the dot of the 'i' positioned between the hind legs of the scarab -->
			<!-- dimensions: 300x490, centered -->
			<use xlink:href="#cairo_logo_top-centered" transform="translate(0, -245)" />
			<use xlink:href="#cairo_text" transform="translate(0, 245)" />
		</g>

		<g id="cairo_banner">
			<!-- The logo on the left, the text 'cairo' in the center, and a mirror image of the logo on the right -->
			<!-- The logos are scaled such that the scarab body nearly matches the height of the text characters (excepting the 'i')
				and the dung should nearly aligns with the dot of the 'i'. The bottoms of the logos are aligned with the bottom of the text. -->
			<!-- dimensions: 370x88, centered -->
			<use xlink:href="#cairo_logo_bottomleft-centered" transform="translate(-180, 40), scale(0.1944)" />
			<use xlink:href="#cairo_text_small" transform="translate(0, 42)" fill="black" />
			<use xlink:href="#cairo_logo_bottomleft-centered" transform="translate(180, 40), scale(0.1944), scale(-1, 1)" />
		</g>

		<g id="freedesktop_org_logo" style="fill:#FFFFFF;stroke:#3B80AE;stroke-width:2.4588;">
		  <g>
		    <path style="stroke:#BABABA;" d="M85.277,40.796c2.058,7.884-2.667,15.942-10.551,17.999L27.143,71.21c-7.884,2.057-15.943-2.667-18-10.552
						     l-7.448-28.55c-2.057-7.884,2.667-15.942,10.551-17.999L59.83,1.695c7.884-2.057,15.942,2.667,17.999,10.551
						     l7.449,28.55z"/>>
		    <path style="fill:#3B80AE;stroke:none;" d="M80.444,39.778c1.749,7.854-1.816,13.621-9.504,15.447l-42.236,11.02c-7.569,2.396-14.089-1.181
							       -15.838-8.836L6.53,33.127c-1.749-8.145,0.709-12.889,9.503-15.447L58.27,6.661
							       c8.144-1.826,14.089,1.363,15.838,8.835l6.336,24.282z"/>>
		    </g>g>
		    <path style="opacity:0.5;fill:none;stroke:#FFFFFF;" d="M45.542,51.793L24.104,31.102l38.1-4.393L45.542,51.793z"/>>
		    <path d="M72.325,28.769c0.405,1.55-0.525,3.136-2.075,3.541l-12.331,3.217c-1.551,0.404-3.137-0.525-3.542-2.076l-2.295-8.801
			     c-0.405-1.551,0.524-3.137,2.076-3.542l12.33-3.217c1.551-0.405,3.137,0.525,3.542,2.076l2.295,8.801z"/>>
		    <path d="M36.51,33.625c0.496,1.9-0.645,3.844-2.545,4.34l-15.112,3.943c-1.901,0.496-3.845-0.644-4.34-2.544l-2.814-10.786
			     c-0.496-1.901,0.644-3.844,2.544-4.34l15.113-3.942c1.901-0.496,3.845,0.643,4.34,2.544l2.814,10.786z"/>>
		    <path d="M52.493,53.208c0.278,1.065-0.36,2.154-1.425,2.432L42.6,57.848c-1.064,0.277-2.153-0.36-2.431-1.426l-1.577-6.043
			     c-0.277-1.064,0.36-2.153,1.425-2.432l8.468-2.209c1.064-0.277,2.154,0.361,2.431,1.426l1.577,6.043z"/>>
		    </g>g>
	</defs>

  <!-- Blue bar at top of slide -->
  <rect x="0" y="0" width="1024" height="170" fill="#162284" />
  
  <g font-family="Frutiger">
    <!-- Slide title -->
    <g id="slide_title" transform="translate(512, 133)">
      <text text-anchor="middle"
	    fill="white"
	    font-weight="bold"
	    x="0"
	    y="4" font-size="55"
	    ss:variable="title">Slide Title</text>
    </g>
    
    <!-- Slide content -->
    <g ss:region="default">
      <rect x="112" y="200" width="800" height="480" fill="none" stroke="blue"/>
      <text font-size="35" fill="black"
	    x="112" y="232">Slide content</text>
    </g>
    
    <!-- Footer -->
    <text ss:variable="URL" x="1016" y="753" text-anchor="end" font-size="20">https://cairographics.org</text>
  </g>

</svg>
