<?xml version="1.0" ?>
<svg width="1024" height="768"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    xmlns:ss="http://www.svgslides.org/svgslides0.1"
    fill="black">



	<defs id="cairo-artwork_defs">
		<g id="hacker_emblem">
			<!-- Note: This is similar though not identical to <PERSON>'s SVG version
				of the hacker emblem (http://www.catb.org/hacker-emblem/glider.svg) -->
			<g id="hacker_emblem_grid" fill="white" stroke="none">
				<!-- Outside: Top, Right, Bottom, Left -->
				<rect x="-2.95" y="-3.05" width="6"   height="0.1" />
				<rect x="2.95"  y="-2.95" width="0.1" height="6" />
				<rect x="-3.05" y="2.95"  width="6"   height="0.1" />
				<rect x="-3.05" y="-3.05" width="0.1" height="6" />
				<!-- Vertical: Left, Right -->
				<rect x="-1.05" y="-2.95" width="0.1" height="5.9" />
				<rect x="0.95"  y="-2.95" width="0.1" height="5.9" />
				<!-- Horizontal: TopLeft, TopMiddle, TopRight -->
				<rect x="-2.95" y="-1.05" width="1.9" height="0.1" />
				<rect x="-0.95" y="-1.05" width="1.9" height="0.1" />
				<rect x="1.05"  y="-1.05" width="1.9" height="0.1" />
				<!-- Horizontal: BottomLeft, BottomMiddle, BottomRight -->
				<rect x="-2.95" y="0.95"  width="1.9" height="0.1" />
				<rect x="-0.95" y="0.95"  width="1.9" height="0.1" />
				<rect x="1.05"  y="0.95"  width="1.9" height="0.1" />
			</g>
			<g id="hacker_emblem_dots" fill="white">
				<circle cx="0"  cy="-2" r="0.7" />
				<circle cx="2"  cy="0"  r="0.7" />
				<circle cx="-2" cy="2"  r="0.7" />
				<circle cx="0"  cy="2"  r="0.7" />
				<circle cx="2"  cy="2"  r="0.7" />
			</g>
		</g>
		<g id="scarab" fill="#f19a14">
			<g transform="translate(-150, -170)">
			<path id="scarab_head" d="M205.599,94.567c0-11.668-24.914-21.129-55.628-21.129
				c-30.723,0-55.624,9.46-55.624,21.129c0,10.203,24.901,7.346,55.624,7.346C180.685,101.913,205.599,104.233,205.599,94.567z"/>
			<path id="scarab_torso" d="M136.423,161.506c0,0,12.751,12.577,13.547,13.362
				c2.262-2.232,13.545-13.362,13.545-13.362c7.135-7.036,87.111-6.399,91.066-6.363c-0.469-6.298-1.254-12.472-2.325-18.519
				c-15.183-19.279-42.811-32.225-74.485-32.225h-55.518c-31.745,0-59.439,13.011-74.598,32.37c-1.054,6-1.829,12.128-2.296,18.374
				C49.321,155.106,129.288,154.47,136.423,161.506z"/>
			<path id="scarab_spine" d="M149.97,301.187c2.005-24.729,8.386-103.483,8.405-103.721
				c-0.09-0.219-6.478-15.578-8.405-20.214c-1.936,4.655-8.316,19.995-8.408,20.214C141.582,197.704,147.965,276.458,149.97,301.187z"/>
			<path id="scarab_wing_left" d="M140.403,197.149l8.862-21.31l-13.686-13.499
				c-5.65-5.573-67.074-6.235-90.259-6.019l-0.006-0.622c-0.154,2.144-0.271,4.302-0.35,6.475
				c-0.076,2.207,10.392,4.706,10.392,6.717c0,2.319-10.457,5.084-10.359,7.631c2.993,73.349,48.53,131.631,104.372,132.048
				l-9.02-111.29L140.403,197.149z"/>
			<path id="scarab_wing_right" d="M244.585,168.891c0-2.011,10.467-4.506,10.391-6.715
				c-0.079-2.174-0.195-4.332-0.351-6.479l-0.004,0.624c-23.186-0.216-84.608,0.445-90.26,6.017l-13.688,13.502l8.915,21.438
				l-9.017,111.29c55.854-0.417,101.378-58.698,104.373-132.049C255.04,173.976,244.585,171.209,244.585,168.891z"/>
			<path id="scarab_leg_front_left" d="M44.506,141.12c-4.135-0.856-4.895-1.54-7.935-2.92
				c-9.59-3.364-10.376-5.481-16.08-11.86c-7.426-8.306-12.661-20.142-17.1-29.463c-3.576-7.525-3.984-16.409-2.86-24.273
				c0.991-6.935,7.144-12.869,12.074-18.92c5.844-7.191,10.356-14.822,17.924-21.354c7.736-6.682,23.203-9.809,26.168-19.648
				C57.86,8.819,54.334,1.766,61.482,0c-0.366,4.703,3.639,8.477,2.397,13.575c-1.129,4.627-4.368,5.811-9.611,9.099
				c-7.564,4.746-18.366,8.779-24.748,13.965c-7.175,5.827-4.369,13.771-10.569,20.057c-2.001,2.03-7.901,4.706-9.137,6.83
				c-1.861,3.199-0.297,9.572-0.116,13.12c0.425,8.284,5.588,14.244,9.555,22.045c4.152,8.141,6.429,15.409,13.411,22.519
				c4.183,4.262,11.429,4.802,16.21,10.647l-3.555,4.186L44.506,141.12z"/>
			<path id="scarab_leg_middle_left" d="M43.94,191.922l-0.809-7.346
				c-9.506-4.579-10.339-9.772-20.738-12.466c-23.728-6.151-21.361,11.25-15.532,26.373c5.676,14.726,8.237,30.23,14.345,44.795
				c2.805,6.688,6.919,13.213,14.298,15.127c0.372-8.435-0.917-10.651-6.113-16.919c-4.395-5.293-3.326-12.548-6.072-18.504
				c-3.581-7.804-4.196-15.646-7.279-23.502c-1.363-3.479-8.33-13.966-6.452-17.861c3.183-6.603,9.178-0.083,12.179,2.077
				c4.218,3.036,6.467,2.223,11.681,2.898C34.041,186.673,37.005,188.756,43.94,191.922z"/>
			<path id="scarab_leg_back_left" d="M65.839,257.063l-2.771-4.837
				c-6.68,8.928-6.993,16.228-10.056,23.347c-5.277,12.263-0.157,28.851,9.854,37.676c6.052,5.375,15.907,9.618,23.122,13.136
				c10.035,4.892,20.113,11.286,31.336,13.396c2.482,0.466,8.798,1.295,6.693-3.522c-0.975-2.237-8.091-4.591-10.146-5.734
				c-8.312-4.623-16.377-10.524-24.142-16.176c-9.498-6.862-20.843-11.186-28.311-20.684c-3.054-3.885-3.544-4.922-2.816-9.39
				c0.693-4.263,1.344-9.174,2.241-13.439C61.855,266.029,63.274,261.378,65.839,257.063z"/>
			<path id="scarab_leg_front_right" d="M255.487,141.12c4.134-0.856,4.896-1.54,7.936-2.92
				c9.583-3.364,10.369-5.481,16.071-11.86c7.428-8.306,12.661-20.142,17.115-29.463c3.574-7.525,3.983-16.409,2.86-24.273
				c-0.992-6.935-7.157-12.869-12.087-18.92c-5.843-7.191-10.356-14.822-17.919-21.354c-7.735-6.682-23.202-9.809-26.167-19.648
				C242.135,8.819,245.66,1.766,238.511,0c0.366,4.703-3.637,8.477-2.396,13.575c1.131,4.627,4.368,5.811,9.611,9.099
				c7.563,4.746,18.367,8.779,24.747,13.965c7.17,5.827,4.362,13.771,10.563,20.057c2.001,2.03,7.901,4.706,9.139,6.83
				c1.859,3.199,0.295,9.572,0.113,13.12c-0.424,8.284-5.588,14.244-9.553,22.045c-4.152,8.141-6.431,15.409-13.404,22.519
				c-4.184,4.262-11.429,4.802-16.211,10.647l3.556,4.186L255.487,141.12z"/>
			<path id="scarab_leg_middle_right" d="M256.053,191.922l0.81-7.346
				c9.507-4.579,10.34-9.772,20.73-12.466c23.741-6.151,21.374,11.25,15.534,26.373c-5.676,14.726-8.238,30.23-14.347,44.795
				c-2.804,6.688-6.911,13.213-14.291,15.127c-0.371-8.435,0.918-10.651,6.113-16.919c4.39-5.293,3.319-12.548,6.066-18.504
				c3.58-7.804,4.197-15.646,7.278-23.502c1.363-3.479,8.33-13.966,6.453-17.861c-3.184-6.603-9.179-0.083-12.181,2.077
				c-4.217,3.036-6.458,2.223-11.672,2.898C265.951,186.673,262.986,188.756,256.053,191.922z"/>
			<path id="scarab_leg_back_right" d="M234.155,257.063l2.771-4.837
				c6.679,8.928,6.991,16.228,10.057,23.347c5.274,12.263,0.154,28.851-9.854,37.676c-6.055,5.375-15.903,9.618-23.117,13.136
				c-10.034,4.892-20.127,11.286-31.351,13.396c-2.481,0.466-8.789,1.295-6.691-3.522c0.976-2.237,8.092-4.591,10.146-5.734
				c8.312-4.623,16.392-10.524,24.155-16.176c9.498-6.862,20.838-11.186,28.305-20.684c3.055-3.885,3.543-4.922,2.818-9.39
				c-0.696-4.263-1.346-9.174-2.244-13.439C238.137,266.029,236.718,261.378,234.155,257.063z"/>
			</g>
		</g>
		<radialGradient id="gradient_radial_dung"
				cx="0" cy="0" r="60"
				fx="0" fy="0" gradientUnits="userSpaceOnUse"
		>
			<stop offset="0"    stop-color="#9a9a9a" />
			<stop offset="0.70" stop-color="#bababa" />
			<stop offset="0.95" stop-color="#FFFFFF" />
		</radialGradient>
		<g id="dung">
			<circle cx="0" cy="0" r="60" fill="url(#gradient_radial_dung)" />
			<g transform="translate(-61, -61)">
				<!-- rough equivalent: <circle cx="0" cy="0" r="60" stroke="#8a8a8a" stroke-width="2" /> -->
				<path fill="#8a8a8a" d="M0,61c0,33.636,27.364,61,61,61s61-27.364,61-61S94.636,0,61,0S0,27.364,0,61z
							M2,61C2,28.467,28.467,2,61,2c32.532,0,59,26.467,59,59c0,32.533-26.468,59-59,59C28.467,120,2,93.533,2,61z"/>
			</g>
			<use xlink:href="#hacker_emblem" x="0" y="0" transform="scale(9)" />
		</g>
		<g id="dung_2_color">
			<!-- This would be simple a circle like so:
				<circle cx="0" cy="0" r="48" stroke-width="2" fill="none" stroke="white"/>
			     but there appears to currently be a bug in the cairo
			     PDF backend that results in an ugly spike in that
			     case. So we use 8 splines instead.
			-->
			<path stroke-width="4" stroke="white" fill="none" d="
M 48,			0
C 48,			12.730391512298112, 
  42.94287166245995,	24.939379331448613
  33.941125496954285,	33.941125496954278
C 24.939379331448624,	42.942871662459943
  12.730391512298114,	48
  0,			48
C -12.730391512298109,	48, 
  -24.939379331448613,	42.94287166245995
  -33.941125496954278,	33.941125496954285
C -42.942871662459943,	24.939379331448624,
  -48,			12.730391512298118,
  -48,			0,
C -48,			-12.730391512298105, 
  -42.94287166245995,	-24.939379331448613,
  -33.941125496954285,	-33.941125496954278
C -24.939379331448624,	-42.942871662459943,
  -12.730391512298119,	-48, 
  0,			-48
C 12.730391512298104,	-48, 
  24.939379331448606,	-42.942871662459943,
  33.941125496954271,	-33.941125496954285
C 42.942871662459936,	-24.939379331448624,
  48,			-12.730391512298123,
  48,			0" />
			<g transform="scale(9)" fill="white" stroke="none" stroke-width="0.22222">
			  	<!-- Hacker emblem grid -->
				<!--
				<path stroke="white" fill="none"
				      d="M -3,-3 L  3,-3 L 3,3 L-3,3 Z
					 M -1,-3 L -1, 3
					 M  1,-3 L  1, 3
					 M -3,-1 L  3,-1
					 M -3, 1 L  3, 1"/>
					 -->

			  	<!-- Hacker emblem dots -->
				<circle cx="0"  cy="-2" r="0.7" />
				<circle cx="2"  cy="0"  r="0.7" />
				<circle cx="-2" cy="2"  r="0.7" />
				<circle cx="0"  cy="2"  r="0.7" />
				<circle cx="2"  cy="2"  r="0.7" />
			</g>
		</g>

		<!-- scarab dimensions: 300x340 -->
		<!-- dung dimensions: 120x120 (radius: 60) -->
		<!-- scarab and dung dimensions: 300x400 -->

		<g id="cairo_logo">
			<!-- dimensions: 300x400, centered -->
			<!-- The logo (scarab and dung), with the center-point of the bounding box at (0,0) -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(0, -140)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0, 30)" />
		</g>
		<g id="cairo_logo_dung-centered">
			<!-- The logo (scarab and dung), with the dung at (0,0), the scarab below -->
			<use xlink:href="#dung_2_color" x="0" y="0" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0,170)" />
		</g>
		<g id="cairo_logo_scarab-centered">
			<!-- The logo (scarab and dung), with the scarab's rotational center at (0,0), the dung above -->
			<!-- The scarab's rotational center in this case is not the center of its bounding box,
				but is calculated to be the intersection-point of the torso, spine and wings -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(0, -175.85)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0, -5.85)" />
		</g>
		<g id="cairo_logo_top-centered">
			<!-- The logo (scarab and dung), with the top-center point of the bounding box at (0,0) -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(0, 60)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0, 230)" /><!-- (0,170+60) -->
		</g>
		<g id="cairo_logo_bottom-centered">
			<!-- The logo (scarab and dung), with the bottom-center point of the bounding box at (0,0) -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(0, -340)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(0, -170)" />
		</g>
		<g id="cairo_logo_right-centered">
			<!-- The logo (scarab and dung), with the right-center point of the bounding box at (0,0) -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(-150, -140)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(-150, 30)" />
		</g>
		<g id="cairo_logo_left-centered">
			<!-- The logo (scarab and dung), with the left-center point of the bounding box at (0,0) -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(150, -140)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(150, 30)" />
		</g>
		<g id="cairo_logo_topleft-centered">
			<!-- The logo (scarab and dung), with the top-left point of the bounding box at (0,0) -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(150, 60)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(150, 230)" /><!-- (150, 170+60) -->
		</g>
		<g id="cairo_logo_topright-centered">
			<!-- The logo (scarab and dung), with the top-right point of the bounding box at (0,0) -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(-150, 60)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(-150, 230)" /><!-- (-150,170+60) -->
		</g>
		<g id="cairo_logo_bottomleft-centered">
			<!-- The logo (scarab and dung), with the bottom-left point of the bounding box at (0,0) -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(150, -340)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(150, -170)" />
		</g>
		<g id="cairo_logo_bottomright-centered">
			<!-- The logo (scarab and dung), with the bottom-right point of the bounding box at (0,0) -->
			<use xlink:href="#dung_2_color" x="0" y="0" transform="translate(-150, -340)" />
			<use xlink:href="#scarab" x="0" y="0" transform="translate(-150, -170)" />
		</g>

		<g id="cairo_text" transform="translate(0,-97)">
		    <g transform="scale(0.1484,0.1484)"> <g transform="translate(-1139,-208.5)">
			 <!-- 63 (c), advance 444, 0 horiBearing 38,522 -->
			 <path transform="translate(65,0)" d="
			       M 412, 433 
			       C 385, 422 336, 413 298, 413 
			       C 142, 413 38, 525 38, 680 
			       C 38, 826 144, 947 298, 947 
			       C 332, 947 377, 944 416, 926 
			       L 409, 842 
			       C 380, 861 340, 871 308, 871 
			       C 187, 871 138, 771 138, 680 
			       C 138, 583 197, 489 302, 489 
			       C 332, 489 368, 496 404, 511 
			       L 412, 433 " />
			 <!-- 61 (a), advance 556, 0 horiBearing 46,522 -->
			 <path transform="translate(486.75,0)" d="
			       M 109, 541 
			       C 147, 509 204, 489 257, 489 
			       C 351, 489 383, 534 383, 622 
			       C 346, 620 320, 620 283, 620 
			       C 186, 620 46, 660 46, 788 
			       C 46, 899 123, 947 233, 947 
			       C 319, 947 369, 900 391, 869 
			       L 393, 869 
			       L 393, 935 
			       L 481, 935 
			       C 479, 920 477, 893 477, 835 
			       L 477, 624 
			       C 477, 485 418, 413 272, 413 
			       C 207, 413 151, 433 104, 461 
			       L 109, 541 
			       M 383, 737 
			       C 383, 813 334, 871 241, 871 
			       C 198, 871 146, 842 146, 788 
			       C 146, 698 272, 690 323, 690 
			       C 343, 690 363, 692 383, 692 
			       L 383, 737 " />
			 <!-- 69 (i), advance 278, 0 horiBearing 86,730 -->
			 <path transform="translate(1000,0)" d="
			       M 92, 935 
			       L 186, 935 
			       L 186, 425 
			       L 92, 425 
			       L 92, 935 
			       M 88, 261
			       A 51, 51 0 1 1 190,261
			       A 51, 51 0 1 1 88,261" />
			 <!-- 72 (r), advance 389, 0 horiBearing 80,522 -->
			 <path transform="translate(1234.25,0)" d="
			       M 80, 935 
			       L 174, 935 
			       L 174, 703 
			       C 174, 575 229, 495 313, 495 
			       C 329, 495 348, 497 365, 504 
			       L 365, 420 
			       C 345, 416 331, 413 303, 413 
			       C 249, 413 195, 451 170, 504 
			       L 168, 504 
			       L 168, 425 
			       L 80, 425 
			       L 80, 935 " />
			 <!-- 6f (o), advance 611, 0 horiBearing 46,522 -->
			 <path transform="translate(1610,0)" d="
			       M 46, 680 
			       C 46, 826 152, 947 306, 947 
			       C 459, 947 565, 826 565, 680 
			       C 565, 525 461, 413 306, 413 
			       C 150, 413 46, 525 46, 680 
			       M 146, 680 
			       C 146, 583 205, 489 306, 489 
			       C 406, 489 465, 583 465, 680 
			       C 465, 771 416, 871 306, 871 
			       C 195, 871 146, 771 146, 680 " />
			 <!-- bounds: 38, 205 <-> 2232, 947 -->
		    </g> </g>
		</g>

		<!-- scaled by 0.72, shifted around to hit pixel boundaries -->
		<g id="cairo_text_small_spaced" transform="translate(0,-71)">
		    <g transform="scale(0.085,0.085)"> <g transform="translate(-1139,-208.5)">
			 <!-- 63 (c), advance 444, 0 horiBearing 38,522 -->
			 <path transform="translate(-151,0)" d="
			       M 412, 433 
			       C 385, 422 336, 413 298, 413 
			       C 142, 413 38, 525 38, 680 
			       C 38, 826 144, 947 298, 947 
			       C 332, 947 377, 944 416, 926 
			       L 409, 842 
			       C 380, 861 340, 871 308, 871 
			       C 187, 871 138, 771 138, 680 
			       C 138, 583 197, 489 302, 489 
			       C 332, 489 368, 496 404, 511 
			       L 412, 433 " />
			 <!-- 61 (a), advance 556, 0 horiBearing 46,522 -->
			 <path transform="translate(379.5,0)" d="
			       M 109, 541 
			       C 147, 509 204, 489 257, 489 
			       C 351, 489 383, 534 383, 622 
			       C 346, 620 320, 620 283, 620 
			       C 186, 620 46, 660 46, 788 
			       C 46, 899 123, 947 233, 947 
			       C 319, 947 369, 900 391, 869 
			       L 393, 869 
			       L 393, 935 
			       L 481, 935 
			       C 479, 920 477, 893 477, 835 
			       L 477, 624 
			       C 477, 485 418, 413 272, 413 
			       C 207, 413 151, 433 104, 461 
			       L 109, 541 
			       M 383, 737 
			       C 383, 813 334, 871 241, 871 
			       C 198, 871 146, 842 146, 788 
			       C 146, 698 272, 690 323, 690 
			       C 343, 690 363, 692 383, 692 
			       L 383, 737 " />
			 <!-- 69 (i), advance 278, 0 horiBearing 86,730 -->
			 <path transform="translate(1000,0)" d="
			       M 92, 935 
			       L 186, 935 
			       L 186, 425 
			       L 92, 425 
			       L 92, 935 
			       M 88, 261
			       A 51, 51 0 1 1 190,261
			       A 51, 51 0 1 1 88,261" />
			 <!-- 72 (r), advance 389, 0 horiBearing 80,522 -->
			 <path transform="translate(1341.5,0)" d="
			       M 80, 935 
			       L 174, 935 
			       L 174, 703 
			       C 174, 575 229, 495 313, 495 
			       C 329, 495 348, 497 365, 504 
			       L 365, 420 
			       C 345, 416 331, 413 303, 413 
			       C 249, 413 195, 451 170, 504 
			       L 168, 504 
			       L 168, 425 
			       L 80, 425 
			       L 80, 935 " />
			 <!-- 6f (o), advance 611, 0 horiBearing 46,522 -->
			 <path transform="translate(1826,0)" d="
			       M 46, 680 
			       C 46, 826 152, 947 306, 947 
			       C 459, 947 565, 826 565, 680 
			       C 565, 525 461, 413 306, 413 
			       C 150, 413 46, 525 46, 680 
			       M 146, 680 
			       C 146, 583 205, 489 306, 489 
			       C 406, 489 465, 583 465, 680 
			       C 465, 771 416, 871 306, 871 
			       C 195, 871 146, 771 146, 680 " />
			 <!-- bounds: 38, 205 <-> 2232, 947 -->
		    </g> </g>
		</g>


		<!-- scaled by 0.72, shifted around to hit pixel boundaries -->
		<g id="cairo_text_small" transform="translate(0,-71)">
		    <g transform="scale(0.085,0.085)"> <g transform="translate(-1139,-208.5)">
			 <!-- 63 (c), advance 444, 0 horiBearing 38,522 -->
			 <path transform="translate(-151,0)" d="
			       M 412, 433 
			       C 385, 422 336, 413 298, 413 
			       C 142, 413 38, 525 38, 680 
			       C 38, 826 144, 947 298, 947 
			       C 332, 947 377, 944 416, 926 
			       L 409, 842 
			       C 380, 861 340, 871 308, 871 
			       C 187, 871 138, 771 138, 680 
			       C 138, 583 197, 489 302, 489 
			       C 332, 489 368, 496 404, 511 
			       L 412, 433 " />
			 <!-- 61 (a), advance 556, 0 horiBearing 46,522 -->
			 <path transform="translate(261.75,0)" d="
			       M 109, 541 
			       C 147, 509 204, 489 257, 489 
			       C 351, 489 383, 534 383, 622 
			       C 346, 620 320, 620 283, 620 
			       C 186, 620 46, 660 46, 788 
			       C 46, 899 123, 947 233, 947 
			       C 319, 947 369, 900 391, 869 
			       L 393, 869 
			       L 393, 935 
			       L 481, 935 
			       C 479, 920 477, 893 477, 835 
			       L 477, 624 
			       C 477, 485 418, 413 272, 413 
			       C 207, 413 151, 433 104, 461 
			       L 109, 541 
			       M 383, 737 
			       C 383, 813 334, 871 241, 871 
			       C 198, 871 146, 842 146, 788 
			       C 146, 698 272, 690 323, 690 
			       C 343, 690 363, 692 383, 692 
			       L 383, 737 " />
			 <!-- 69 (i), advance 278, 0 horiBearing 86,730 -->
			 <path transform="translate(764.75)" d="
			       M 92, 935 
			       L 186, 935 
			       L 186, 425 
			       L 92, 425 
			       L 92, 935 
			       M 88, 261
			       A 51, 51 0 1 1 190,261
			       A 51, 51 0 1 1 88,261" />
			 <!-- 72 (r), advance 389, 0 horiBearing 80,522 -->
			 <path transform="translate(988.5,0)" d="
			       M 80, 935 
			       L 174, 935 
			       L 174, 703 
			       C 174, 575 229, 495 313, 495 
			       C 329, 495 348, 497 365, 504 
			       L 365, 420 
			       C 345, 416 331, 413 303, 413 
			       C 249, 413 195, 451 170, 504 
			       L 168, 504 
			       L 168, 425 
			       L 80, 425 
			       L 80, 935 " />
			 <!-- 6f (o), advance 611, 0 horiBearing 46,522 -->
			 <path transform="translate(1355.5,0)" d="
			       M 46, 680 
			       C 46, 826 152, 947 306, 947 
			       C 459, 947 565, 826 565, 680 
			       C 565, 525 461, 413 306, 413 
			       C 150, 413 46, 525 46, 680 
			       M 146, 680 
			       C 146, 583 205, 489 306, 489 
			       C 406, 489 465, 583 465, 680 
			       C 465, 771 416, 871 306, 871 
			       C 195, 871 146, 771 146, 680 " />
			 <!-- bounds: 38, 205 <-> 2232, 947 -->
		    </g> </g>
		</g>

		<g id="cairo_logo_text_small">
			<!-- The logo on the left, the text 'cairo' on the right -->
			<use xlink:href="#cairo_logo_bottomleft-centered" transform="translate(0, 78), scale(0.1944)" />
			<use xlink:href="#cairo_text_small" fill="white" transform="translate(175,82)"/>
		</g>

		<g id="cairo_logo_with_text">
			<!-- The logo (scarab and dung), with the text 'cairo' below, the dot of the 'i' positioned between the hind legs of the scarab -->
			<!-- dimensions: 300x490, centered -->
			<use xlink:href="#cairo_logo_top-centered" transform="translate(0, -245)" />
			<use xlink:href="#cairo_text" transform="translate(0, 245)" />
		</g>

		<g id="cairo_banner">
			<!-- The logo on the left, the text 'cairo' in the center, and a mirror image of the logo on the right -->
			<!-- The logos are scaled such that the scarab body nearly matches the height of the text characters (excepting the 'i')
				and the dung should nearly aligns with the dot of the 'i'. The bottoms of the logos are aligned with the bottom of the text. -->
			<!-- dimensions: 370x88, centered -->
			<use xlink:href="#cairo_logo_bottomleft-centered" transform="translate(-180, 40), scale(0.1944)" />
			<use xlink:href="#cairo_text_small" transform="translate(0, 42)" fill="black" />
			<use xlink:href="#cairo_logo_bottomleft-centered" transform="translate(180, 40), scale(0.1944), scale(-1, 1)" />
		</g>

		<g id="freedesktop_org_logo" style="fill:#FFFFFF;stroke:#3B80AE;stroke-width:2.4588;">
		  <g>
		    <path style="stroke:#BABABA;" d="M85.277,40.796c2.058,7.884-2.667,15.942-10.551,17.999L27.143,71.21c-7.884,2.057-15.943-2.667-18-10.552
						     l-7.448-28.55c-2.057-7.884,2.667-15.942,10.551-17.999L59.83,1.695c7.884-2.057,15.942,2.667,17.999,10.551
						     l7.449,28.55z"/>>
		    <path style="fill:#3B80AE;stroke:none;" d="M80.444,39.778c1.749,7.854-1.816,13.621-9.504,15.447l-42.236,11.02c-7.569,2.396-14.089-1.181
							       -15.838-8.836L6.53,33.127c-1.749-8.145,0.709-12.889,9.503-15.447L58.27,6.661
							       c8.144-1.826,14.089,1.363,15.838,8.835l6.336,24.282z"/>>
		    </g>g>
		    <path style="opacity:0.5;fill:none;stroke:#FFFFFF;" d="M45.542,51.793L24.104,31.102l38.1-4.393L45.542,51.793z"/>>
		    <path d="M72.325,28.769c0.405,1.55-0.525,3.136-2.075,3.541l-12.331,3.217c-1.551,0.404-3.137-0.525-3.542-2.076l-2.295-8.801
			     c-0.405-1.551,0.524-3.137,2.076-3.542l12.33-3.217c1.551-0.405,3.137,0.525,3.542,2.076l2.295,8.801z"/>>
		    <path d="M36.51,33.625c0.496,1.9-0.645,3.844-2.545,4.34l-15.112,3.943c-1.901,0.496-3.845-0.644-4.34-2.544l-2.814-10.786
			     c-0.496-1.901,0.644-3.844,2.544-4.34l15.113-3.942c1.901-0.496,3.845,0.643,4.34,2.544l2.814,10.786z"/>>
		    <path d="M52.493,53.208c0.278,1.065-0.36,2.154-1.425,2.432L42.6,57.848c-1.064,0.277-2.153-0.36-2.431-1.426l-1.577-6.043
			     c-0.277-1.064,0.36-2.153,1.425-2.432l8.468-2.209c1.064-0.277,2.154,0.361,2.431,1.426l1.577,6.043z"/>>
		    </g>g>
		    <g id="bullet">
		      <use x="0" y="0" xlink:href="#cairo_logo" transform="translate(-6,-2) scale(0.1, 0.1)"/>>
		    </g>
		    <g id="redhat_logo_horizontal">
		      <!-- 380x125 Red Hat log (horizontal layout) -->
                      <g fill="black" stroke="none" 
                       transform="translate(0,124),scale(1,-1),translate(-214,-258)"
                       fill-rule="evenodd"
                      >
			<!-- r -->
			<path fill="black" d="
			 M 367.0625 315.3203
			 C 367.0625 320.8765 366.9463 324.9644 366.7227 328.6597
			 L 375.811 328.6597
			 L 376.2002 320.7764
			 L 376.4971 320.7764
			 C 378.5391 326.6221 383.3809 329.5996 387.8594 329.5996
			 C 388.8843 329.5996 389.4824 329.5601 390.3218 329.373
			 L 390.3218 319.4863
			 C 389.3398 319.6763 388.4224 319.7842 387.1592 319.7842
			 C 382.1597 319.7842 378.688 316.6006 377.751 311.8447
			 C 377.5732 310.918 377.4805 309.8086 377.4805 308.6777
			 L 377.4805 287.1504
			 L 366.9766 287.1504
			 L 367.0625 315.3203
			" />

			<!-- e -->
			<path fill="black" d="
			 M 402.9927 305.0791
			 C 403.2715 297.5586 409.0918 294.2695 415.814 294.2695
			 C 420.6406 294.2695 424.0942 295.0234 427.2681 296.1924
			 L 428.8232 288.9678
			 C 425.269 287.4629 420.3413 286.3359 414.3149 286.3359
			 C 400.8384 286.3359 392.9409 294.6592 392.9409 307.3809
			 C 392.9409 318.8369 399.8911 329.6772 413.2437 329.6772
			 C 426.7397 329.6772 431.1338 318.5771 431.1338 309.4893
			 C 431.1338 307.5381 430.9624 305.9707 430.7593 305.0059
			 L 402.9927 305.0791

			 M 421.2485 312.3926
			 C 421.2954 316.2388 419.6206 322.5088 412.5903 322.5088
			 C 406.1299 322.5088 403.4438 316.645 402.9722 312.3926
			 L 421.2485 312.3926
			" />

			<!-- d -->
			<path fill="black" d="
			 M 476.355 344.667
			 L 465.8638 347.5083
			 L 465.8638 324.1914
			 L 465.6904 324.1914
			 C 463.8335 327.2563 459.7407 329.5996 454.0571 329.5996
			 C 444.0762 329.5996 435.3828 321.3374 435.4478 307.4307
			 C 435.4478 294.6719 443.2983 286.2168 453.2119 286.2168
			 C 459.2017 286.2168 464.2114 289.0723 466.6909 293.7217
			 L 466.8779 293.7217
			 L 467.3491 287.1504
			 L 476.6997 287.1504
			 C 476.5083 289.9717 476.355 294.543 476.355 298.792
			 L 476.355 344.667

			 M 465.8638 305.1504
			 C 465.8638 304.0479 465.7856 303.0234 465.5454 302.0869
			 C 464.4873 297.5439 460.7734 294.6172 456.4819 294.6172
			 C 449.8721 294.6172 446.0903 300.1885 446.0903 307.8164
			 C 446.0903 315.5166 449.8384 321.4761 456.6016 321.4761
			 C 461.3208 321.4761 464.6992 318.1484 465.6274 314.1064
			 C 465.8071 313.2559 465.8638 312.208 465.8638 311.3711
			 L 465.8638 305.1504
			" />

			<!-- h -->
			<path fill="black" d="
			 M 503.7964 329.0176
			 C 500.6836 329.0176 497.8926 328.1187 495.5493 326.6714
			 C 493.1162 325.2461 491.1353 323.0464 489.959 320.7666
			 L 489.7915 320.7666
			 L 489.7915 341.0195
			 L 485.7427 342.1226
			 L 485.7427 287.1504
			 L 489.7915 287.1504
			 L 489.7915 312.1787
			 C 489.7915 313.8408 489.9204 314.9946 490.3462 316.2109
			 C 492.0928 321.3013 496.8896 325.4805 502.689 325.4805
			 C 511.0664 325.4805 513.9673 318.7603 513.9673 311.3906
			 L 513.9673 287.1504
			 L 518.0137 287.1504
			 L 518.0137 311.8359
			 C 518.0137 327.0791 507.6753 329.0176 503.7964 329.0176
			" />

			<!-- a -->
			<path fill="black" d="
			 M 554.3413 296.873
			 C 554.3413 293.6357 554.4692 290.2832 554.9375 287.1504
			 L 551.2085 287.1504
			 L 550.6128 293.0156
			 L 550.4209 293.0156
			 C 548.438 289.8594 543.8765 286.2168 537.3726 286.2168
			 C 529.1392 286.2168 525.3057 292.0117 525.3057 297.4688
			 C 525.3057 306.9121 533.6421 312.6064 550.292 312.4321
			 L 550.292 313.5234
			 C 550.292 317.5718 549.5044 325.6475 539.8242 325.5859
			 C 536.2446 325.5859 532.5132 324.6255 529.5513 322.5366
			 L 528.2632 325.4805
			 C 532.0015 328.0137 536.5659 329.0176 540.2705 329.0176
			 C 552.0801 329.0176 554.3413 320.1509 554.3413 312.8379
			 L 554.3413 296.873

			 M 550.292 309.0234
			 C 541.3813 309.2813 529.6128 307.9336 529.6128 298.1055
			 C 529.6128 292.2246 533.4946 289.5811 537.7578 289.5811
			 C 544.5796 289.5811 548.4561 293.8018 549.8677 297.7871
			 C 550.1646 298.6621 550.292 299.5371 550.292 300.2402
			 L 550.292 309.0234
			" />

			<!-- t -->
			<path fill="black" d="
			 M 570.459 337.0996
			 L 570.459 328.0801
			 L 582.1235 328.0801
			 L 582.1235 324.7959
			 L 570.459 324.7959
			 L 570.459 298.1943
			 C 570.459 292.9912 572.0757 289.7285 576.4692 289.7285
			 C 578.5815 289.7285 580.0757 290.0078 581.1206 290.3711
			 L 581.6099 287.2354
			 C 580.2871 286.6836 578.4302 286.2539 575.9619 286.2539
			 C 572.9741 286.2539 570.4995 287.1934 568.8994 289.1543
			 C 567.0469 291.3057 566.4116 294.7412 566.4116 298.916
			 L 566.4116 324.7959
			 L 559.5059 324.7959
			 L 559.5059 328.0801
			 L 566.4116 328.0801
			 L 566.4116 335.606
			 L 570.459 337.0996
			" />

			<!-- ® for 'redhat' -->
			<path fill="black" d="
			 M 335.5 288.9707
			 L 336.0352 288.9707
			 L 336.8408 287.6445
			 L 337.3608 287.6445
			 L 336.4888 288.9937
			 C 336.9404 289.0498 337.2832 289.2881 337.2832 289.834
			 C 337.2832 290.4385 336.9258 290.7051 336.2017 290.7051
			 L 335.0366 290.7051
			 L 335.0366 287.6445
			 L 335.5 287.6445
			 L 335.5 288.9707

			 M 335.5 289.3643
			 L 335.5 290.3101
			 L 336.1318 290.3101
			 C 336.4531 290.3101 336.7979 290.2402 336.7979 289.8647
			 C 336.7979 289.3916 336.4492 289.3643 336.0566 289.3643
			 L 335.5 289.3643
			" />

			<path fill="black" d="
			 M 339.0439 289.1719
			 C 339.0439 287.5176 337.7041 286.1763 336.0493 286.1763
			 C 334.395 286.1763 333.0527 287.5176 333.0527 289.1719
			 C 333.0527 290.8271 334.395 292.1675 336.0493 292.1675
			 C 337.7041 292.1675 339.0439 290.8271 339.0439 289.1719

			 M 336.0493 291.6367
			 C 334.6865 291.6367 333.5835 290.5332 333.5835 289.1719
			 C 333.5835 287.8096 334.6865 286.7061 336.0493 286.7061
			 C 337.4082 286.7061 338.5117 287.8096 338.5117 289.1719
			 C 338.5117 290.5332 337.4082 291.6367 336.0493 291.6367
			" />

			<!-- Black background behind The Shadowman -->
			<path fill="black" d="
			 M 326.4531 286.208
			 C 324.1177 286.7451 321.6396 287.0801 319.1338 287.0801
			 C 314.8496 287.0801 310.9502 286.3389 308.0732 285.1426
			 C 307.7559 284.9844 307.5303 284.6533 307.5303 284.2764
			 C 307.5303 284.1406 307.5654 283.999 307.6172 283.8838
			 C 307.957 282.8975 307.3984 281.8281 304.6157 281.2158
			 C 300.4883 280.3096 297.8833 276.0527 296.3916 274.6367
			 C 294.6411 272.9756 289.6973 271.9531 290.4404 272.9434
			 C 291.0225 273.7188 293.2485 276.1348 294.6016 278.748
			 C 295.8125 281.083 296.8906 281.7461 298.375 283.9736
			 C 298.811 284.627 300.4995 286.9219 300.9912 288.7373
			 C 301.543 290.5107 301.356 292.7344 301.5679 293.6494
			 C 301.8721 294.9697 303.1182 297.8369 303.2129 299.4531
			 C 303.2666 300.3691 299.3916 298.1494 297.5532 298.1494
			 C 295.7144 298.1494 293.9233 299.248 292.2808 299.3281
			 C 290.248 299.4248 288.9414 297.7607 287.1025 298.0508
			 C 286.0518 298.2178 285.167 299.1426 283.3311 299.2129
			 C 280.7178 299.3086 277.5244 297.7607 271.5264 297.9531
			 C 265.6255 298.1436 260.1753 305.4082 259.4312 306.5635
			 C 258.5605 307.9199 257.4961 307.9199 256.335 306.8555
			 C 255.1738 305.792 253.7432 306.627 253.3359 307.3389
			 C 252.5615 308.6943 250.4927 312.6543 247.2881 313.4824
			 C 242.8564 314.6309 240.6118 311.0283 240.9033 308.1621
			 C 241.1987 305.252 243.0801 304.4375 243.9512 302.8906
			 C 244.8213 301.3428 245.2671 300.3428 246.9053 299.6572
			 C 248.0674 299.1758 248.5 298.458 248.1533 297.5049
			 C 247.8506 296.6738 246.6416 296.4834 245.8477 296.4463
			 C 244.1592 296.3662 242.9756 296.8242 242.1123 297.376
			 C 241.1084 298.0137 240.292 298.9033 239.416 300.4131
			 C 238.4023 302.0781 236.8052 302.8037 234.9453 302.8037
			 C 234.0586 302.8037 233.2295 302.5693 232.4922 302.1895
			 C 229.5771 300.6748 226.1064 299.7744 222.3706 299.7744
			 L 218.1572 299.7734
			 C 216.1064 305.8555 214.9951 312.3682 214.9951 319.1416
			 C 214.9951 352.6064 242.1226 379.7334 275.5859 379.7334
			 C 309.0498 379.7334 336.1758 352.6064 336.1758 319.1416
			 C 336.1758 307 332.6035 295.6895 326.4531 286.208
			" />

			<!-- The Shadowman's face -->
			<path fill="white" d="
			 M 326.4531 286.209
			 C 324.1177 286.7461 321.6396 287.084 319.1338 287.084
			 C 314.8496 287.084 310.9502 286.3418 308.0732 285.1436
			 C 307.7559 284.9873 307.5303 284.6553 307.5303 284.2783
			 C 307.5303 284.1416 307.5654 284.001 307.6172 283.8838
			 C 307.957 282.8994 307.3984 281.8311 304.6157 281.2178
			 C 300.4883 280.3115 297.8833 276.0537 296.3916 274.6416
			 C 294.6411 272.9766 289.6973 271.9551 290.4404 272.9463
			 C 291.0225 273.7197 293.2485 276.1367 294.6016 278.749
			 C 295.8125 281.083 296.8906 281.75 298.375 283.9756
			 C 298.811 284.627 300.4995 286.9238 300.9912 288.7402
			 C 301.543 290.5117 301.356 292.7354 301.5679 293.6514
			 C 301.8721 294.9727 303.1182 297.8379 303.2129 299.457
			 C 303.2666 300.3721 299.3916 298.1504 297.5532 298.1504
			 C 295.7144 298.1504 293.9233 299.251 292.2808 299.3301
			 C 290.248 299.4258 288.9414 297.7627 287.1025 298.0518
			 C 286.0518 298.2207 285.167 299.1465 283.3311 299.2148
			 C 280.7178 299.3096 277.5244 297.7627 271.5264 297.9561
			 C 265.6255 298.1475 260.1753 305.4121 259.4312 306.5674
			 C 258.5605 307.9219 257.4961 307.9219 256.335 306.8574
			 C 255.1738 305.7939 253.7432 306.6299 253.3359 307.3438
			 C 252.5615 308.6963 250.4927 312.6553 247.2881 313.4854
			 C 242.8564 314.6338 240.6118 311.0313 240.9033 308.1641
			 C 241.1987 305.2539 243.0801 304.4395 243.9512 302.8926
			 C 244.8213 301.3438 245.2671 300.3457 246.9053 299.6621
			 C 248.0674 299.1768 248.5 298.4609 248.1533 297.5068
			 C 247.8506 296.6768 246.6416 296.4873 245.8477 296.4492
			 C 244.1592 296.3672 242.9756 296.8262 242.1123 297.376
			 C 241.1084 298.0176 240.292 298.9043 239.416 300.416
			 C 238.4023 302.0801 236.8052 302.8086 234.9453 302.8086
			 C 234.0586 302.8086 233.2295 302.5723 232.4922 302.1934
			 C 229.5771 300.6748 226.1064 299.7773 222.3706 299.7773
			 L 218.1572 299.7744
			 C 226.2363 275.8105 248.8965 258.5527 275.5859 258.5527
			 C 296.9063 258.5527 315.6538 269.5635 326.4531 286.209
			" />

			<!-- nose shadow -->
			<path fill="black" d="
			 M 288.9307 291.7637
			 C 289.2422 291.46 289.7793 290.4375 289.1226 289.1396
			 C 288.7544 288.4521 288.3579 287.9678 287.6489 287.4023
			 C 286.7969 286.7188 285.1309 285.9307 282.8457 287.3799
			 C 281.6172 288.1592 281.543 288.4209 279.8467 288.2012
			 C 278.6348 288.043 278.1533 289.2656 278.5884 290.2832
			 C 279.0244 291.2969 280.8145 292.1191 283.04 290.8135
			 C 284.041 290.2256 285.6025 288.9844 286.9688 290.084
			 C 287.5356 290.5381 287.875 290.8408 288.6611 291.75
			 C 288.6963 291.7881 288.7461 291.8105 288.8018 291.8105
			 C 288.8516 291.8105 288.8965 291.793 288.9307 291.7637
			" />

			<!-- The Shadowman's red hat -->
			<path fill="#cc0000" d="
			 M 309.7769 335.2627
			 C 309.1787 333.251 308.3271 330.6763 304.5391 328.7314
			 C 303.9878 328.4497 303.7764 328.9126 304.0313 329.3477
			 C 305.4629 331.7832 305.7168 332.3921 306.1328 333.3525
			 C 306.7148 334.7568 307.02 336.7549 305.8618 340.9219
			 C 303.5835 349.1221 298.8296 360.083 295.375 363.6392
			 C 292.04 367.0698 285.998 368.0361 280.5371 366.6348
			 C 278.5264 366.1191 274.5918 364.0732 267.2939 365.7168
			 C 254.665 368.5605 252.7939 362.2368 252.0693 359.4824
			 C 251.3438 356.7271 249.6045 348.897 249.6045 348.897
			 C 249.0244 345.7085 248.2646 340.1631 267.874 336.4287
			 C 277.0088 334.6885 277.4736 332.3276 277.8779 330.6289
			 C 278.603 327.585 279.7627 325.8438 281.0674 324.9746
			 C 282.373 324.1035 281.0674 323.3828 279.6187 323.2349
			 C 275.7285 322.8311 261.3491 326.9541 252.8428 331.7881
			 C 245.8828 336.0415 245.7656 339.8721 247.3584 343.1211
			 C 236.8452 344.2573 228.9561 342.1348 227.5254 337.1582
			 C 225.0693 328.6157 246.3047 314.0264 270.4839 306.7061
			 C 295.8579 299.0225 321.9556 304.3857 324.8564 320.335
			 C 326.1738 327.5811 320.0713 332.9419 309.7769 335.2627
			" />

			<!-- shadow on hat -->
			<path fill="black" d="
			 M 270.8711 350.8813
			 C 263.8721 350.375 263.145 349.6191 261.834 348.2227
			 C 259.9854 346.2539 257.5508 350.7773 257.5508 350.7773
			 C 256.0898 351.085 254.3179 353.4404 255.2744 355.6411
			 C 256.2158 357.8174 257.9551 357.1641 258.5 356.4868
			 C 259.1626 355.6621 260.5771 354.3125 262.4141 354.3613
			 C 264.251 354.4097 266.3706 354.7959 269.3262 354.7959
			 C 272.3213 354.7959 274.335 353.6777 274.4487 352.7168
			 C 274.5459 351.8965 274.2061 351.1226 270.8711 350.8813
			" />

			<!-- another shadow on hat -->
			<path fill="black" d="
			 M 278.2236 362.4463
			 C 278.2129 362.4453 278.2021 362.4438 278.1919 362.4438
			 C 278.084 362.4438 277.9961 362.5273 277.9961 362.6274
			 C 277.9961 362.7007 278.041 362.7646 278.106 362.7939
			 C 279.4629 363.5107 281.4873 364.0811 283.8042 364.3169
			 C 284.499 364.3887 285.1787 364.4248 285.832 364.4307
			 C 285.9478 364.4307 286.0615 364.4297 286.1787 364.4277
			 C 290.062 364.3398 293.1719 362.7974 293.1255 360.9814
			 C 293.0791 359.165 289.8955 357.7637 286.0112 357.8506
			 C 284.7529 357.8794 283.5732 358.0615 282.5576 358.3545
			 C 282.4385 358.3857 282.3506 358.4883 282.3506 358.6094
			 C 282.3506 358.731 282.4385 358.834 282.5605 358.8638
			 C 284.9839 359.4248 286.6191 360.3408 286.5039 361.207
			 C 286.3511 362.3545 283.1816 362.979 279.4248 362.6011
			 C 279.0137 362.5596 278.6118 362.5068 278.2236 362.4463
			" />

			<!-- ® for The Shadowman -->
			<path fill="black" d="
			 M 588.3018 288.9707
			 L 588.8369 288.9707
			 L 589.6426 287.6445
			 L 590.1626 287.6445
			 L 589.2905 288.9937
			 C 589.7422 289.0498 590.085 289.2881 590.085 289.834
			 C 590.085 290.4385 589.7275 290.7051 589.0034 290.7051
			 L 587.8384 290.7051
			 L 587.8384 287.6445
			 L 588.3018 287.6445
			 L 588.3018 288.9707

			 M 588.3018 289.3643
			 L 588.3018 290.3101
			 L 588.9336 290.3101
			 C 589.2549 290.3101 589.5996 290.2402 589.5996 289.8647
			 C 589.5996 289.3916 589.251 289.3643 588.8584 289.3643
			 L 588.3018 289.3643
			" />

			<path fill="black" d="
			 M 591.8457 289.1719
			 C 591.8457 287.5176 590.5059 286.1763 588.8511 286.1763
			 C 587.1968 286.1763 585.8545 287.5176 585.8545 289.1719
			 C 585.8545 290.8271 587.1968 292.1675 588.8511 292.1675
			 C 590.5059 292.1675 591.8457 290.8271 591.8457 289.1719

			 M 588.8511 291.6367
			 C 587.4883 291.6367 586.3853 290.5332 586.3853 289.1719
			 C 586.3853 287.8096 587.4883 286.7061 588.8511 286.7061
			 C 590.21 286.7061 591.3135 287.8096 591.3135 289.1719
			 C 591.3135 290.5332 590.21 291.6367 588.8511 291.6367
			" />

		      </g>
		    </g>

	</defs>

  <g id="watermark" transform="translate(200, 185), rotate(-50), scale(2.5)">
    <use xlink:href="#scarab" x="0" y="170" fill-opacity="0.2"/>
  </g>

  <!-- Blue bar at top of slide -->
  <rect x="0" y="0" width="1024" height="170" fill="#162284" />

  <!-- Scarab and "cairo" at upper-left -->
  <g transform="translate(10,0)">
    <use stroke="none" xlink:href="#cairo_logo_text_small"/>
  </g>

  <!-- Presentation title at upper-right -->
  <text ss:variable="presentation-subtitle" text-anchor="end"
     fill="white" x="1016" y="28" font-size="20">Presentation Sub-title</text>

  <!-- Red Hat logo at lower-left -->
  <use xlink:href="#redhat_logo_horizontal" transform="translate(8,768),scale(.5,.5),translate(0, -125)" />

  <g font-family="Frutiger">
    <text text-anchor="middle"
	  fill="black"
	  x="512"
	  y="300" font-size="80"
	  font-weight="bold"
	  ss:variable="title">Slide Title</text>
    
    <!-- Slide content -->
    <g ss:region="default">
      <rect x="512" y="400" width="2" height="280" fill="none" stroke="blue"/>
      <text font-size="50" fill="black" text-anchor="middle"
	    x="512" y="450">Slide content</text>
    </g>
    
    <!-- Footer -->
    <text ss:variable="URL" x="1016" y="753" text-anchor="end" font-size="20">https://cairographics.org</text>
  </g>

</svg>
