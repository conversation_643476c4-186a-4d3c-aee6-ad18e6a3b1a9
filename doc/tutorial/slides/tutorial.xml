<?xml version="1.0" ?>
<svgslides
    xmlns="http://www.svgslides.org/svgslides0.1"
    xmlns:svg="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    >

  <slides theme="cairo"
	  presentation="Drawing with cairo"
	  presentation-subtitle="drawing with cairo"
	  URL="https://cairographics.org"
	  bullet="bullet">

    <slide title="Tutorial preparation" variant="blank" bullet="">
      <lc></lc>
      <lc align="center">https://cairographics.org/tutorial</lc>
      <lc></lc>
      <li>wget https://cairographics.org/cairo-tutorial.tar.gz</li>
      <li>tar xzf cairo-tutorial.tar.gz</li>
      <li>cd cairo-tutorial</li>
      <li>make</li>
      <li>cat README</li>
      <lc></lc>
      <lc align="center">IRC: irc.freenode.net #cairo</lc>
    </slide>

    <slide variant="title">
      <lc><PERSON></lc>
      <lc>Red Hat, Inc.</lc>
      <lc></lc>
      <lc>linux.conf.au</lc>
      <lc>2006-01-26</lc>
      <lc>https://cairographics.org</lc>
    </slide>

    <slide title="Ugly graphics" variant="separator">
      <lc align="left">Jaggies</lc>
      <lc align="center">Fuzzies</lc>
      <lc align="right">Fireworks</lc>
    </slide>

    <slide title="Jaggies">
      <img src="jaggies.svg"/>
    </slide>

    <slide title="Fuzzies">
      <img src="fuzzies.svg"/>
    </slide>

    <slide title="Fireworks">
    </slide>

    <slide title="What you can do about it">
      <li>Let application authors know there are options</li>
      <li>Learn to use cairo—patch the applications</li>
      <li>Write your own cairo-using applications</li>
    </slide>

    <slide title="Getting started" variant="separator">
      <lc align="center">Various shell cairo programs</lc>
    </slide>

    <slide title="Minimal cairo-xlib program" variant="code">
      <lc>#include &lt;cairo.h&gt;</lc>
      <lc>#include &lt;cairo-xlib.h&gt;</lc>
      <lc>int main (void) {</lc>
      <lc>    Display *dpy = XOpenDisplay (0);</lc>
      <lc>    Window  w = XCreateSimpleWindow (dpy,RootWindow (dpy, 0),</lc>
      <lc>                0, 0, WIDTH, HEIGHT, 0, 0, WhitePixel (dpy, 0));</lc>
      <lc>    cairo_surface_t *surface = cairo_xlib_surface_create (dpy, w,</lc>
      <lc>                        DefaultVisual (dpy, DefaultScreen (dpy)),</lc>
      <lc>                                                   WIDTH, HEIGHT);</lc>
      <lc>    XEvent  ev;</lc>
      <lc>    XSelectInput (dpy, w, ExposureMask);</lc>
      <lc>    XMapWindow (dpy, w);</lc>
      <lc>    while (XNextEvent (dpy, &amp;ev) == 0)</lc>
      <lc>        if (ev.type == Expose &amp;&amp; !ev.xexpose.count) {</lc>
      <lc>            cairo_t *cr = cairo_create (surface);</lc>
      <lc>            draw (cr);</lc>
      <lc>            cairo_destroy (cr);</lc>
      <lc>        }</lc>
      <lc>}</lc>
    </slide>

    <slide title="Minimal cairo-gtk program" variant="code">
      <lc>#include &lt;gtk/gtk.h&gt;</lc>
      <lc>static gboolean</lc>
      <lc>handle_expose (GtkWidget *widget, GdkEventExpose *event, gpointer data) {</lc>
      <lc>    cairo_t *cr = gdk_cairo_create (widget->window);</lc>
      <lc>    draw (cr);</lc>
      <lc>    cairo_destroy (cr);</lc>
      <lc>    return FALSE;</lc>
      <lc>}</lc>
      <lc>int main (int argc, char **argv) {</lc>
      <lc>    GtkWidget *window, *drawing_area;</lc>
      <lc>    gtk_init (&amp;argc, &amp;argv);</lc>
      <lc>    window = gtk_window_new (GTK_WINDOW_TOPLEVEL);</lc>
      <lc>    gtk_window_set_default_size (GTK_WINDOW (window), WIDTH, HEIGHT);</lc>
      <lc>    drawing_area = gtk_drawing_area_new ();</lc>
      <lc>    gtk_container_add (GTK_CONTAINER (window), drawing_area);</lc>
      <lc>    g_signal_connect (drawing_area, "expose-event",</lc>
      <lc>                      G_CALLBACK (handle_expose), NULL);</lc>
      <lc>    gtk_widget_show_all (window); gtk_main ();</lc>
      <lc>}</lc>
    </slide>

    <slide title="Minimal cairo-png program" variant="code">
      <lc>#include &lt;cairo.h&gt;</lc>
      <lc></lc>
      <lc>int main (void)</lc>
      <lc>{</lc>
      <lc>    cairo_surface_t *surface;</lc>
      <lc>    cairo_t *cr;</lc>
      <lc></lc>
      <lc>    surface = cairo_image_surface_create (CAIRO_FORMAT_ARGB32,</lc>
      <lc>                                          WIDTH, HEIGHT);</lc>
      <lc>    cr = cairo_create (surface);</lc>
      <lc>    draw (cr);</lc>
      <lc>    cairo_surface_write_to_png (surface, "foo.png");</lc>
      <lc></lc>
      <lc>    cairo_surface_destroy (surface);</lc>
      <lc>    cairo_destroy (cr);</lc>
      <lc></lc>
      <lc>    return 0;</lc>
      <lc>}</lc>
    </slide>

    <slide title="Minimal cairo-pdf program" variant="code">
      <lc>#include &lt;cairo.h&gt;</lc>
      <lc>#include &lt;cairo-pdf.h&gt;</lc>
      <lc>int main (void)</lc>
      <lc>{</lc>
      <lc>    cairo_surface_t *surface;</lc>
      <lc>    cairo_t *cr;</lc>
      <lc></lc>
      <lc>    surface = cairo_pdf_surface_create ("foo.pdf", WIDTH, HEIGHT);</lc>
      <lc>    </lc>
      <lc>    cr = cairo_create (surface);</lc>
      <lc>    draw (cr);</lc>
      <lc>    cairo_show_page (cr);</lc>
      <lc></lc>
      <lc>    cairo_surface_destroy (surface);</lc>
      <lc>    cairo_destroy (cr);</lc>
      <lc></lc>
      <lc>    return 0;</lc>
      <lc>}</lc>
    </slide>

    <slide title="Minimal pycairo-gtk shell" variant="code">
      <lc>#!/usr/bin/env python</lc>
      <lc>import gtk</lc>
      <lc>import cairo</lc>
      <lc></lc>
      <lc>def expose (widget, event):</lc>
      <lc>    cr = widget.window.cairo_create ()</lc>
      <lc>    draw (cr)</lc>
      <lc>      </lc>
      <lc>win = gtk.Window ()</lc>
      <lc>win.connect ('destroy', gtk.main_quit)</lc>
      <lc>win.set_default_size(WIDTH, HEIGHT)</lc>
      <lc>drawingarea = gtk.DrawingArea ()</lc>
      <lc>win.add (drawingarea)</lc>
      <lc>drawingarea.connect ('expose_event', expose)</lc>
      <lc>win.show_all ()</lc>
      <lc>gtk.main ()</lc>
    </slide>

    <slide title="Minimal pycairo-png shell" variant="code">
      <lc>#!/usr/bin/env python</lc>
      <lc>import cairo</lc>
      <lc></lc>
      <lc>surface = cairo.ImageSurface (cairo.FORMAT_ARGB32, WIDTH, HEIGHT)</lc>
      <lc>cr = cairo.Context (surface)</lc>
      <lc></lc>
      <lc>draw (cr)</lc>
      <lc></lc>
      <lc>surface.write_to_png ('foo.png')</lc>
    </slide>

    <slide title="Minimal nickle program" variant="code">
      <lc>autoimport Cairo;</lc>
      <lc></lc>
      <lc>cairo_t cr = new (WIDTH, HEIGHT);</lc>
      <lc>draw (cr);</lc>
    </slide>

    <slide title="Available bindings:" variant="large-content">
      <li>C++ (cairomm)</li>
      <li>Haskell (hscairo)</li>
      <li>Java (CairoJava, cairo-java)</li>
      <li>Common Lisp (cl-cairo)</li>
      <li>Nickle (cairo-5c)</li>
      <li>Objective Caml (cairo-ocaml)</li>
      <li>perl (cairo-perl)</li>
      <li>python (pycairo)</li>
      <li>ruby (rcairo)</li>
      <li>squeak (cairo-squeak)</li>
    </slide>

    <slide title="Following along" >
      <lc></lc>
      <lc align="center">https://cairographics.org/tutorial</lc>
      <lc align="center">https://cairographics.org/cairo-tutorial.tar.gz</lc>
      <li>tar xzf cairo-tutorial.tar.gz</li>
      <li>cd cairo-tutorial</li>
      <li>make</li>
      <li>cat README</li>
      <lc align="center">IRC: irc.freenode.net #cairo</lc>
    </slide>

    <slide title="Cairo's rendering model">
      <g>
	<image x="0" y="0" xlink:href="rendering-model.png" width="800" height="500" />
      </g>
    </slide>

    <slide title="Let's start drawing" variant="separator">
      <lc align="center">Here comes the fun part</lc>
    </slide>

    <slide title="API Overview" variant="large-content">
      <ul>
	<li>Paths</li>
	<ul>
	  <li>construction</li>
	  <li>filling, stroking</li>
	</ul>
	<li>Images</li>
	<ul>
	  <li>loading from disk</li>
	  <li>using as source</li>
	  <li>transforming/filtering</li>
	</ul>
	<li>Text</li>
	<ul>
	  <li>“Toy” API</li>
	  <li>“Full” API</li>
	</ul>
      </ul>
    </slide>

    <slide title="Drawing functions" variant="large-content">
      <ul>
	<li>Paths</li>
	<ul>
	  <li>cairo_stroke</li>
	  <li>cairo_fill</li>
	</ul>
	<li>Images</li>
	<ul>
	  <li>cairo_paint</li>
	  <li>cairo_mask</li>
	  <li>cairo_paint_with_alpha</li>
	</ul>
	<li>Text</li>
	<ul>
	  <li>cairo_show_text (“toy”)</li>
	  <li>cairo_show_glyphs (“full”)</li>
	</ul>
      </ul>
    </slide>

    <slide title="Basic path building">
      <ul>
	<li>Paths consist of lines and splines:</li>
	<ul>
	  <li>cairo_move_to</li>
	  <lc>  start new sub path, set current point</lc>
	  <li>cairo_line_to</li>
	  <lc>  add line segment to path</lc>
	  <li>cairo_curve_to</li>
	  <lc>  add Bézier spline to path</lc>
	  <li>cairo_close_path</li>
	  <lc>  add line segment to last move_to point</lc>
	</ul>
      </ul>
    </slide>

    <slide title="Stroking paths">
      <ul>
	<li>cairo_stroke</li>
	<ul>
	  <li>Paints the path itself as stroked by a pen</li>
	  <li>Controlled by various stroke parameters:</li>
	  <ul>
	    <li>cairo_set_line_width</li>
	    <li>cairo_set_line_cap</li>
	    <li>cairo_set_line_join</li>
	    <li>cairo_set_miter_limit</li>
	    <li>cairo_set_dash</li>
	  </ul>
	</ul>
      </ul>
    </slide>

    <slide title="cairo_stroke example">
    </slide>

    <slide title="cairo_set_line_width">
    </slide>

    <slide title="cairo_set_line_cap example">
      <ul>
	<li>CAIRO_LINE_CAP_BUTT</li>
	<li>CAIRO_LINE_CAP_ROUND</li>
	<li>CAIRO_LINE_CAP_SQUARE</li>
      </ul>
    </slide>

    <slide title="cairo_set_line_join example">
      <ul>
	<li>CAIRO_LINE_JOIN_BEVEL</li>
	<li>CAIRO_LINE_JOIN_ROUND</li>
	<li>CAIRO_LINE_JOIN_MITER</li>
      </ul>
    </slide>

    <slide title="cairo_close_path example">
      <li>Adds a line segment (if necessary) to the</li>
      <lc>start of the path</lc>
      <li>Draws a join from that line to the first</li>
      <lc>element of the path</lc>
    </slide>

    <slide title="cairo_set_dash example">
    </slide>

    <slide title="cairo_fill">
      <ul>
	<li>Paints the “inside” of the path</li>
	<li>Two different ways to determine inside-ness</li>
	<ul>
	  <li>cairo_set_fill_rule:</li>
	  <lc>  CAIRO_FILL_RULE_WINDING</lc>
	  <lc>  CAIRO_FILL_RULE_EVEN_ODD</lc>
	</ul>
      </ul>
    </slide>

    <slide title="cairo_fill example">
    </slide>

    <slide title="cairo_set_fill_rule">
      <li>To test a point for inside-ness, imagine a ray</li>
      <lc>from the point and extending to infinity</lc>
      <lc>(any direction) and examine path crossings</lc>
      <li>EVEN_ODD counts crossings and fills when odd</li>
      <li>WINDING counts up/down for left/right crossings</li>
      <lc>and fills when the final number is not zero</lc>
    </slide>

    <slide title="Fill rule example">
    </slide>

    <slide title="Something besides black?">
      <li>All drawing occurs with the source pattern</li>
      <li>Uniform-color sources can be set</li>
      <lc>with or without alpha:</lc>
      <ul>
	<li>cairo_set_source_rgb</li>
	<li>cairo_set_source_rgba</li>
      </ul>
    </slide>

    <slide title="cairo_set_source example">
    </slide>

    <slide title="Fill and stroke the same path">
      <li>cairo_stroke and cairo_fill consume the path</li>
      <li>Variants are available to preserve the path:</li>
      <ul>
	<li>cairo_stroke_preserve</li>
	<li>cairo_fill_preserve</li>
      </ul>
      <li>This is a variation from PostScript and PDF</li>
      <ul>
	<li>The cairo approach differs in different ways</li>
	<li>We think this way is better (of course)</li>
      </ul>
    </slide>

    <slide title="Fill and stroke example">
    </slide>

    <slide title="More path building">
      <ul>
	<li>cairo_rectangle</li>
	<li>cairo_arc</li>
	<li>cairo_text_path</li>
	<li>cairo_glyph_path</li>
	<li>Relative-coordinate variants:</li>
	<ul>
	  <li>cairo_rel_move_to</li>
	  <li>cairo_rel_line_to</li>
	  <li>cairo_rel_curve_to</li>
	</ul>
      </ul>
    </slide>

    <slide title="cairo_arc example">
    </slide>

    <slide title="Outline text">
    </slide>

    <slide title="Affine Transformations">
      <ul>
	<li>Single matrix combines rotation, translation,</li>
	<lc>scale and shear</lc>
	<li>Non-projective transformations</li>
	<ul>
	  <li>Pen doesn't change shape along the stroke</li>
	</ul>
	<li>Transformations are cumulative</li>
	<ul>
	  <li>translate, scale != scale, translate</li>
	</ul>
      </ul>
    </slide>

    <slide title="Affine Transform Example">
    </slide>

    <slide title="Save/Restore Graphics States">
      <ul>
	<li>cairo_save and cairo_restore</li>
	<li>Very useful for bracketing function bodies</li>
	<li>Eliminates graphics state side effects</li>
	<lc></lc>
	<li>NOTE: Path is not part of graphics state</li>
	<ul>
	  <li>Path is not affected by cairo_save/restore</li>
	  <li>Useful for path-generating functions</li>
	</ul>
      </ul>
    </slide>

    <slide title="Clipping">
      <li>cairo_clip</li>
      <li>Intersects path with current clip to further</li>
      <lc>restrict drawing</lc>
      <li>Pixel-aligned clipping is much faster</li>
      <li>cairo_clip_preserve is available as well</li>
    </slide>

    <slide title="Other source patterns">
      <ul>
	<li>Surface patterns</li>
	<ul>
	  <li>Image from disk</li>
	  <li>Results of cairo drawing</li>
	</ul>
	<li>Gradient patterns</li>
	<ul>
	  <li>linear gradients</li>
	  <li>radial gradients</li>
	</ul>
      </ul>
    </slide>

    <slide title="Loading an image file">
    </slide>

    <slide title="Painting an image">
    </slide>

    <slide title="Pattern transformation">
    </slide>

    <slide title="Pattern filtering">
    </slide>

    <slide title="Linear gradient example">
    </slide>

    <slide title="Radial gradient example">
    </slide>

    <slide title="Text">
      <ul>
	<li>“Toy” API:</li>
	<ul>
	  <li>Useful for demos like we are writing today</li>
	  <li>No real layout—not useful for the majority</li>
	  <lc>of writing systems in the world</lc>
	</ul>
	<li>“Full” API:</li>
	<ul>
	  <li>User must do OS-dependent font selection</li>
	  <li>User must access font glyph IDs directly</li>
	  <li>User must do all text layout</li>
	</ul>
      </ul>
    </slide>

    <slide title="“Toy” text API"/>
    <ul>
      <li>Simple font selection</li>
      <ul>
	<li>family, weight, slant</li>
	<li>OS independent</li>
	<li>No font listing support</li>
      </ul>
      <li>UTF-8 text drawing and extents functions</li>
      <li>Still supports full font transformations</li>
    </ul>

    <slide title="“Toy” text example">
    </slide>

    <slide title="“Real” text example">
      <li>Just use pango</li>
    </slide>

    <slide title="Error handling in C">
      <li>C has no exceptions</li>
      <li>Checking each return is tedious</li>
      <li>C programmers rarely bother</li>
      <li>Lots of broken programs result</li>
    </slide>
      
      <slide title="Cairo error handling">
	<li>Cairo stores status value when error occurs</li>
	<li>API “shuts down” when an error occurs</li>
	<li>All cairo functions are benign</li>
	<lc>(and well defined) after any error.</lc>
	<li>cairo_status can be called when convenient</li>
	<li>Error status values propagate across objects</li>
      </slide>

      <slide title="Cairo error example">
      </slide>

  </slides>
</svgslides>
