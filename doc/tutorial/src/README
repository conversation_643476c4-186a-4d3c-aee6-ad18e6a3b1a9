Welcome to the cairo tutorial:

+--------------------------------+
| How to Recognize Ugly Graphics |
|(and what you can do about them)|
+--------------------------------+

This directory is your personal playground for following along with
the examples. In order for you to make use of these files you will
need to have cairo and its header files installed. You can find
instructions for doing this at:

	https://cairographics.org/tutorial

Notice that there are a few .c files in this directory.

You should start out by just typing "make" which will turn each .c
file into several different programs. Go ahead and run each of the
programs and see what they do. Some of them will open up new X windows
while others will simply write their output to files (such .png or
.pdf).

After you play with those a bit, go ahead and take a look at the
contents of the .c files. You'll see that each file contains a draw()
function that does all of the drawing.

You might be surprised to notice that there is no main() function in
any of the files. Instead, main is hidden away by means of
cairo-tutorial.h. This rather non-conventional style is used to allow
you to focus on the actual drawing code involved in using cairo, while
not having to worry about the setup semantics. We don't recommend that
you follow this style for real projects.

As you follow along during the tutorial and get some ideas for things
to draw, you'll want to start making your own .c files. You can copy
an existing file or make your own by following this simple minimal
template:

	#include "cairo-tutorial.h"

	static void
	draw (cairo_t *cr, int width, int height)
	{
	    /* Put your drawing code here. */
	}

Any new file you create will automatically get picked up by the
Makefile so that "make" will compile your file into several different
programs, just like the existing examples.

If you'd like to control the initial size of the output, you may
define WIDTH and HEIGHT before including cairo-tutorial.h like so:

	#define WIDTH  100
	#define HEIGHT 100

	#include "cairo-tutorial.h"

If you would like to change the set of cairo-backend target programs
that are compiled, you may edit the "all" target in the Makefile.

Have fun!

-Carl Worth

<EMAIL>
