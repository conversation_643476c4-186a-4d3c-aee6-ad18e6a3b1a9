# -*- indent-tabs-mode: nil -*-
include:
  - template: 'Workflows/Branch-Pipelines.gitlab-ci.yml'
  - project: 'freedesktop/ci-templates'
    ref: '98b1218f146a1ec96d65e3ce0041f9a6ec5cb5e6'
    file: '/templates/fedora.yml'

# From https://gitlab.freedesktop.org/freedesktop/ci-templates/-/blob/master/src/gitlab-ci.tmpl
workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS'
      when: never
    - if: $CI_COMMIT_BRANCH

variables:
  FDO_UPSTREAM_REPO: 'cairo/cairo'
  FDO_DISTRIBUTION_VERSION: '40'
  FDO_DISTRIBUTION_TAG: '2024-07-26-coverage.0'

  # TODO: should probably get its own image at some point instead of reusing the GStreamer one
  # See https://gitlab.freedesktop.org/gstreamer/gstreamer/-/blob/main/.gitlab-image-tags.yml for latest
  # (This one is from the lastest stable branch since it's likely to stay around unchanged for longer)
  WINDOWS_IMAGE: "registry.freedesktop.org/gstreamer/gstreamer/amd64/windows:2024-03-05.0-1.24"

  DEFAULT_MESON_ARGS: >
    --default-library=both

  RUST_STABLE: "1.80.0"
  RUSTUP_VERSION: "1.27.1"

stages:
  - prep
  - build
  - test
  - analysis
  - deploy

# Global CI policy: This can be used to configure global behaviour our our jobs
default:
  retry:
    max: 2
    when:
      - 'runner_system_failure'
      - 'stuck_or_timeout_failure'
      - 'scheduler_failure'
      - 'api_failure'
  interruptible: true

.ccache_setup:
  variables:
    CCACHE_BASEDIR: "$CI_PROJECT_DIR"
    CCACHE_DIR: "$CI_PROJECT_DIR/ccache"
    CC: "ccache gcc"
    CXX: "ccache g++"
  before_script:
    - mkdir -p ccache
    - ccache --show-stats
  cache:
    # Each job will have it's own cache
    key: "$CI_JOB_NAME"
    paths:
      - ccache/

fedora image:
  extends:
   - '.fdo.container-build@fedora'
  stage: 'prep'
  variables:
    FDO_DISTRIBUTION_PACKAGES: >
      meson
      ninja-build
      ccache
      gcc
      g++
      zlib-devel
      expat-devel
      libpng-devel
      fontconfig-devel
      freetype-devel
      libX11-devel
      libXrender-devel
      glib2-devel
      librsvg2-devel
      gtk2-devel
      poppler-glib-devel
      libspectre-devel
      ghostscript
      pixman-devel
      systemd-devel
      systemd-udev
      expat-devel
      which
      diffutils
      xorg-x11-server-Xvfb
      dejavu-sans-fonts
      dejavu-sans-mono-fonts
      dejavu-serif-fonts
      google-noto-emoji-color-fonts
      google-noto-sans-vf-fonts
      fonttools
      util-linux
      poppler-utils
      clang
      clang-analyzer
      clang-tools-extra
      compiler-rt
      libasan
      libubsan
      llvm
      wget
    FDO_DISTRIBUTION_EXEC: >-
      bash .gitlab-ci/install-rust.sh --rustup-version ${RUSTUP_VERSION} \
                              --stable ${RUST_STABLE} \
                              --arch x86_64-unknown-linux-gnu &&
      bash .gitlab-ci/install-rust-tools.sh &&
      bash .gitlab-ci/install-grcov.sh &&
      rm -rf /root/.cargo /root/.cache    # cleanup compilation dirs; binaries are installed now

.build fedora:
  extends:
    - '.fdo.distribution-image@fedora'
    - '.ccache_setup'
  stage: 'build'
  variables:
    MESON_ARGS: >
      ${DEFAULT_MESON_ARGS}
      ${EXTRA_MESON_ARGS}
      --werror
    CFLAGS: '-Wno-error=deprecated-declarations'
  script:
    - meson setup ${MESON_ARGS} builddir
    - meson compile -C builddir

    # Run test scripts
    - mkdir builddir/src/.libs
    - touch builddir/src/.libs/libfoo.so
    # Run all the tests, except for the big test executable which
    # gets run separately
    - meson test -C builddir --no-suite=slow --print-errorlogs

    - meson install -C builddir
  artifacts:
    expire_in: "7 days"
    when: "always"
    paths:
      - "*"

fedora build shared:
  extends:
    - '.build fedora'
  variables:
    EXTRA_MESON_ARGS: '--default-library=shared'

fedora build static:
  extends:
    - '.build fedora'
  variables:
    EXTRA_MESON_ARGS: '--default-library=static'

fedora build clang:
  extends:
    - '.build fedora'
  variables:
    EXTRA_MESON_ARGS: '--default-library=shared'
  script:
    - export CC=clang
    - meson setup ${MESON_ARGS} builddir
    - meson compile -C builddir

.test fedora:
  dependencies:
    - 'fedora build shared'
  needs:
    - 'fedora build shared'
  extends:
    - '.fdo.distribution-image@fedora'
    - '.ccache_setup'
  stage: 'test'
  artifacts:
    when: 'always'
    expire_in: "7 days"
    paths:
      - builddir/meson-logs/*
      - builddir/test/*.log
      - builddir/test/pdiff/*.log
      - builddir/test/output
    exclude:
      - "builddir/test/**/*.cs"
      - "builddir/test/**/*.trace"

fedora build check-refs.sh:
  extends: '.test fedora'
  script:
    # Check for duplicate reference images
    - ./test/check-refs.sh "$(pwd)/builddir/test/pdiff/perceptualdiff"

test fedora pdf:
  extends: '.test fedora'
  script:
    - export CAIRO_TEST_IGNORE_pdf_argb32=$(tr '\n' ',' < .gitlab-ci/ignore-pdf-argb32.txt)
    - export CAIRO_TEST_IGNORE_pdf_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-pdf-rgb24.txt)
    - export CAIRO_TEST_TARGET=pdf
    - export srcdir=../../test
    - (cd builddir/test && xvfb-run ./cairo-test-suite)

test fedora ps2:
  extends: '.test fedora'
  script:
    - export CAIRO_TEST_IGNORE_ps2_argb32=$(tr '\n' ',' < .gitlab-ci/ignore-ps2-argb32.txt)
    - export CAIRO_TEST_IGNORE_ps2_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-ps2-rgb24.txt)
    - export CAIRO_TEST_TARGET=ps2
    - export CAIRO_TEST_UGLY_HACK_TO_IGNORE_PS_FAILURES=1
    - export srcdir=../../test
    - (cd builddir/test && xvfb-run ./cairo-test-suite)

test fedora ps3:
  extends: '.test fedora'
  script:
    - export CAIRO_TEST_IGNORE_ps3_argb32=$(tr '\n' ',' < .gitlab-ci/ignore-ps3-argb32.txt)
    - export CAIRO_TEST_IGNORE_ps3_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-ps3-rgb24.txt)
    - export CAIRO_TEST_TARGET=ps3
    - export CAIRO_TEST_UGLY_HACK_TO_IGNORE_PS_FAILURES=1
    - export srcdir=../../test
    - (cd builddir/test && xvfb-run ./cairo-test-suite)

test fedora script:
  extends: '.test fedora'
  script:
    - export CAIRO_TEST_UGLY_HACK_TO_SOMETIMES_IGNORE_SCRIPT_XCB_HUGE_IMAGE_SHM=1
    - export CAIRO_TEST_IGNORE_script_argb32=$(tr '\n' ',' < .gitlab-ci/********************.txt)
    - export CAIRO_TEST_TARGET=script
    - export srcdir=../../test
    - (cd builddir/test && xvfb-run ./cairo-test-suite)

test fedora image:
  extends: '.test fedora'
  script:
    - export CAIRO_TEST_IGNORE_image_argb32=$(tr '\n' ',' < .gitlab-ci/ignore-image-argb32.txt)
    - export CAIRO_TEST_IGNORE_image_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-image-rgb24.txt)
    - export CAIRO_TEST_IGNORE_image16_rgb24=$(tr '\n' ',' < .gitlab-ci/********************.txt)
    - export CAIRO_TEST_TARGET=image,image16
    - export srcdir=../../test
    - (cd builddir/test && xvfb-run ./cairo-test-suite)

test fedora recording:
  extends: '.test fedora'
  script:
    - export CAIRO_TEST_IGNORE_recording_argb32=$(tr '\n' ',' < .gitlab-ci/ignore-recording-argb32.txt)
    - export CAIRO_TEST_IGNORE_recording_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-recording-rgb24.txt)
    - export CAIRO_TEST_TARGET=recording
    - export srcdir=../../test
    - (cd builddir/test && xvfb-run ./cairo-test-suite)

test fedora svg:
  extends: '.test fedora'
  script:
    - export CAIRO_TEST_IGNORE_svg11_argb32=$(tr '\n' ',' < .gitlab-ci/ignore-svg11-argb32.txt)
    - export CAIRO_TEST_IGNORE_svg11_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-svg11-rgb24.txt)
    - export CAIRO_TEST_TARGET=svg11
    - export CAIRO_BOILERPLATE_OPEN_NO_DAEMON=1
    - export CAIRO_BOILERPLATE_DO_NOT_CRASH_ON_ANY2PPM_ERROR=1
    - export ANY2PPM="timeout 3m ./any2ppm"
    - ulimit -S -s 131072
    - export srcdir=../../test
    - (cd builddir/test && xvfb-run ./cairo-test-suite)

test fedora xcb:
  extends: '.test fedora'
  script:
    - export CAIRO_TEST_IGNORE_xcb_argb32=$(tr '\n' ',' < .gitlab-ci/ignore-xcb-argb32.txt)
    - export CAIRO_TEST_IGNORE_xcb_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-xcb-rgb24.txt)
    - export CAIRO_TEST_IGNORE_xcb_window_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-xcb-window-rgb24.txt)
    - export CAIRO_TEST_IGNORE_xcb_window__rgb24=$(tr '\n' ',' < '.gitlab-ci/ignore-xcb-window&-rgb24.txt')
    - export CAIRO_TEST_IGNORE_xcb_render_0_0_argb32=$(tr '\n' ',' < .gitlab-ci/ignore-xcb-render-0-0-argb32.txt)
    - export CAIRO_TEST_IGNORE_xcb_render_0_0_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-xcb-render-0-0-rgb24.txt)
    - export CAIRO_TEST_IGNORE_xcb_fallback_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-xcb-fallback-rgb24.txt)
    - export CAIRO_TEST_TARGET="xcb,xcb-window,xcb-window&,xcb-render-0_0,xcb-fallback"
    - export srcdir=../../test
    - (cd builddir/test && xvfb-run ./cairo-test-suite)

test fedora xlib:
  extends: '.test fedora'
  script:
    - export CAIRO_TEST_IGNORE_xlib_argb32=$(tr '\n' ',' < .gitlab-ci/ignore-xlib-argb32.txt)
    - export CAIRO_TEST_IGNORE_xlib_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-xlib-rgb24.txt)
    - export CAIRO_TEST_IGNORE_xlib_window_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-xlib-window-rgb24.txt)
    - export CAIRO_TEST_IGNORE_xlib_render_0_0_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-xlib-render-0-0-rgb24.txt)
    - export CAIRO_TEST_IGNORE_xlib_fallback_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-xlib-fallback-rgb24.txt)
    - export CAIRO_TEST_TARGET="xlib,xlib-window,xlib-render-0_0,xlib-fallback"
    - export srcdir=../../test
    - (cd builddir/test && xvfb-run ./cairo-test-suite)

mingw-32 build:
  extends:
    - '.fdo.distribution-image@fedora'
  stage: 'build'
  before_script:
    - dnf install -y 'dnf-command(builddep)'
    - dnf builddep -y mingw32-cairo
    - dnf install -y mingw32-gcc-c++
  script:
    - mkdir builddir
    - cd builddir
    # Test building with FreeType enabled and Fontconfig disabled
    - mingw32-meson --default-library=both -Dfontconfig=disabled
    - ninja install
  artifacts:
    expire_in: "7 days"
    when: "always"
    paths:
      - 'builddir/meson-logs/'

mingw-64 build:
  extends:
    - '.fdo.distribution-image@fedora'
  stage: 'build'
  before_script:
    - dnf install -y 'dnf-command(builddep)'
    - dnf builddep -y mingw64-cairo
    - dnf install -y mingw64-gcc-c++
  script:
    - mkdir builddir
    - cd builddir
    - mingw64-meson --default-library=both
    - ninja install
  artifacts:
    expire_in: "7 days"
    when: "always"
    paths:
      - 'builddir/meson-logs/'

# Based on https://gitlab.freedesktop.org/gstreamer/gstreamer/-/blob/main/.gitlab-ci.yml (.build windows)
.build windows:
  image: $WINDOWS_IMAGE
  stage: 'build'
  tags:
    - 'docker'
    - 'windows'
    - '2022'
  timeout: '30min'
  variables:
    MESON_ARGS: >
      ${DEFAULT_MESON_ARGS}
      -Dfontconfig=enabled
      -Dfreetype=enabled
      -Dglib=enabled
      -Dzlib=enabled
      ${EXTRA_MESON_ARGS}
  before_script:
    # Make sure meson is up to date, latest fontconfig requires a newer Meson than what's on the image
    - pip3 install -U meson
  script:
    # Make sure powershell exists on errors
    # https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_preference_variables?view=powershell-6
    - $ErrorActionPreference = "Stop"

    # Copy GLib from existing subproject cache to avoid downloading it
    # FIXME: no longer cached in GStreamer image, using tarball for now
    #- cd $env:CI_PROJECT_DIR
    #- cp -r C:/subprojects/glib subprojects/

    # For some reason, options are separated by newline instead of space, so we
    # have to replace them first.
    - $env:MESON_ARGS = $env:MESON_ARGS.replace("`n"," ")
    
    # Gitlab executes PowerShell in docker, but VsDevCmd.bat is a batch script.
    # Environment variables substitutions is done by PowerShell before calling
    # cmd.exe, that's why we use $env:FOO instead of %FOO%
    - cmd.exe /C "C:\BuildTools\Common7\Tools\VsDevCmd.bat -host_arch=amd64 -arch=$env:ARCH &&
        meson subprojects update --reset &&
        meson build $env:MESON_ARGS &&
        ninja -C build"
  artifacts:
    expire_in: "7 days"
    when: "always"
    paths:
      - 'build/meson-logs/'

vs2019 shared amd64:
  extends: '.build windows'
  variables:
    ARCH: 'amd64'
    EXTRA_MESON_ARGS: '--default-library=shared'

vs2019 static amd64:
  extends: '.build windows'
  variables:
    ARCH: 'amd64'
    EXTRA_MESON_ARGS: '--default-library=static'

vs2019 shared x86:
  extends: '.build windows'
  variables:
    ARCH: 'x86'
    EXTRA_MESON_ARGS: '--default-library=shared'

android arm64 fedora:
  # TODO: should probably build our own image here some day
  # See https://gitlab.freedesktop.org/gstreamer/gstreamer/container_registry/7689 for current images
  image: 'registry.freedesktop.org/gstreamer/gstreamer/amd64/android-fedora:2020-10-22.0-master'
  stage: 'build'
  artifacts:
    name: "${CI_JOB_NAME}_${CI_COMMIT_SHA}"
    expire_in: '5 days'
    when: 'always'
    paths:
      - "build/meson-logs/*.txt"
  allow_failure: true
  before_script:
    - dnf install -y python3-pip gcc ninja-build gperf
    - pip3 install --user meson
  script:
    - export PATH="$HOME/.local/bin:$PATH"
    - |
      cat > android-cross-file.txt <<EOF
      [constants]
      ndk_path    = '/android/ndk'
      toolchain   = ndk_path + '/toolchains/llvm/prebuilt/linux-x86_64/bin/aarch64-linux-android'
      api         = '28'

      [host_machine]
      system      = 'android'
      cpu_family  = 'aarch64'
      cpu         = 'aarch64'
      endian      = 'little'

      [properties]
      sys_root        = ndk_path + '/sysroot'
      c_link_args     = ['-fuse-ld=gold']
      cpp_link_args   = ['-fuse-ld=gold']

      [binaries]
      c           = toolchain + api + '-clang'
      cpp         = toolchain + api + '-clang++'
      ar          = toolchain + '-ar'
      strip       = toolchain + '-strip'
      EOF
    - meson setup --cross-file android-cross-file.txt -Dpixman:a64-neon=disabled build
    - meson compile --verbose -C build

macOS x86 host:
  tags:
    - gst-macos-12.3
  stage: 'build'
  artifacts:
    when: 'always'
    expire_in: "7 days"
    paths:
      - build/meson-logs/*.txt
      - build/meson-private/*.pc
      - build/test/*.log
      - build/test/pdiff/*.log
      - build/test/output
    exclude:
      - "build/test/**/*.cs"
      - "build/test/**/*.trace"
  before_script:
    # Install the DejaVu fonts
    - curl -LO https://downloads.sourceforge.net/dejavu/dejavu-fonts-ttf-2.37.zip
    - unzip dejavu-fonts-ttf-2.37.zip
    - cp dejavu-fonts-ttf-2.37/ttf/*.ttf /Library/Fonts
    # Update pip
    - pip3 install --upgrade pip
    # Make sure meson is up to date
    - pip3 install -U meson
    # Need to install certificates for Python
    - pip3 install --upgrade certifi
    # Anther way to install certificates
    - open /Applications/Python\ 3.8/Install\ Certificates.command
    # Get ninja
    - pip3 install -U ninja
  script:
    - CERT_PATH=$(python3 -m certifi) && export SSL_CERT_FILE=${CERT_PATH} && export REQUESTS_CA_BUNDLE=${CERT_PATH}
    # pixman region-test fails to link on macOS
    - meson setup -Dpixman:tests=disabled build
    - meson compile --verbose -C build
    # Test cairo-quartz. Other backends should be covered by other jobs
    - export CAIRO_TEST_IGNORE_quartz_argb32=$(tr '\n' ',' < .gitlab-ci/********************.txt)
    - export CAIRO_TEST_IGNORE_quartz_rgb24=$(tr '\n' ',' < .gitlab-ci/ignore-quartz-rgb24.txt)
    - export CAIRO_TEST_TARGET=quartz
    - (cd build/test && srcdir=../../test ./cairo-test-suite)

# Run static analysis.
static-scan:
  stage: 'analysis'
  extends:
    - '.fdo.distribution-image@fedora'
  variables:
    MESON_ARGS: >
      --buildtype=debug
  script:
    - meson setup ${MESON_ARGS} _scan_build .
    - ninja -C _scan_build scan-build
  artifacts:
    name: "cairo-${CI_JOB_NAME}-${CI_COMMIT_REF_NAME}"
    when: always
    paths:
      - "_scan_build/meson-logs/scanbuild"

coverage:
  stage: 'analysis'
  extends:
    - '.fdo.distribution-image@fedora'
  needs:
    - job: 'fedora image'
      artifacts: false
  script:
    - source ./.gitlab-ci/env.sh
    - bash -x ./.gitlab-ci/build-with-coverage.sh
    - bash -x ./.gitlab-ci/gen-coverage.sh
  coverage: '/Coverage: \d+\.\d+/'
  artifacts:
    name: "cairo-${CI_JOB_NAME}-${CI_COMMIT_REF_NAME}"
    expire_in: 2 days
    when: always
    reports:
      coverage_report:
        coverage_format: cobertura
        path: public/coverage.xml
    paths:
      - "_build/meson-logs"
      - public

pages:
  stage: 'deploy'
  needs:
    - job: coverage
  script:
    - mkdir -p public
    - cp .gitlab-ci/pages-index.html public/index.html
  artifacts:
    paths:
      - public
    expire_in: 1 day
  rules:
    # Restrict to the main branch so not every branch tries to deploy the web site
    - if: ($CI_DEFAULT_BRANCH == $CI_COMMIT_BRANCH)
