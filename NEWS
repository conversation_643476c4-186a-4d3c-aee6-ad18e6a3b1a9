Release 1.18.4 (2025-03-08 <PERSON><PERSON> <<EMAIL>>)
==============================================================

A new stable release.

The dependency on LZO has been made optional through a build time
configuration toggle. [!580]

You can build Cairo against a Freetype installation that does not have the
FT_Color type. [#792]

Cairo tests now build on Solaris 11.4 with GCC 14. [!599]

The DirectWrite backend now builds on MINGW 11. [!600]

Thanks to <PERSON>, the DirectWrite backend now supports font
variations and proper glyph coverage. [#877, !602]

Support for Windows 98 has been removed. The minimum requirement for
Windows is now Vista.

Release 1.18.2 (2024-09-01 <PERSON><PERSON> <<EMAIL>>)
==============================================================

A new stable release.

The malloc-stats code has been removed from the tests directory [#640]; the
canonical location for it is: https://github.com/behdad/malloc-stats

Cairo now requires a version of pixman equal to, or newer than, 0.40. [!522]

There have been multiple build fixes for newer versions of GCC [!525, !542];
for MSVC [#808]; for Solaris [!540]; and on macOS 10.7 [#810].

PNG errors caused by loading malformed data are correctly propagated to
callers, so they can handle the case. [!524]

Both stroke and fill colors are now set when showing glyphs on a PDF
surface. [#813]

All the font options are copied when creating a fallback font object. [#819]

When drawing text on macOS, Cairo now tries harder to select the appropriate
font name. [#811]

Cairo now prefers the COLRv1 table inside a font, if one is available. [!537]

Cairo requires a C11 toolchain when building. [!479]

Release 1.18.0 (2023-09-20 Emmanuele Bassi <<EMAIL>>)
==============================================================

The first stable cairo release in five years should be cause for celebration.

All the API added in the 1.17 development cycle is now considered stable, and
will not change.

Many thanks to all the contributors for this release.

The cairo-sphinx tool has been removed; we could not find any instruction on
how to use it, and no user answered our call for help. If you were using
cairo-sphinx, please reach out to the cairo maintainers.

Cairo now implements Type 3 color fonts for PDF. Thanks to Adrian Johnson for
his work on this feature.

Khaled Hosny contributed multiple documentation fixes, to ensure that the
cairo API reference is up to date. Khaled also fixed multiple compiler
warnings generated when building cairo.

The XML surface has been removed; it was disabled by default when building
cairo, and we could not find any downstream distributor that would enable
it.

The Tee surface is now automatically enabled. Downstream distributors of
cairo have been enabling for years it in order to build Firefox.

Fujii Hironori and Adrian Johnson fixed multiple issues with the DWrite
font backend.

John Ralls improved the Quartz surface; mainly, Quartz surfaces now use
the main display ColorSpace, speeding up rendering operations.

Cairo now hides all private symbols by default on every platform; the old
"slim" symbols hack to alias internally used symbols has been dropped, in
favor of using `-Bsymbolic-functions` with toolchains that support it.

Uli Schlachter fixed multiple memory leaks in the code base and test suite,
and helped with many reviews and general maintenance.

Marc Jeanmougin added new API to expose the Pixman dithering filter to cairo
patterns; this is currently implemented only for image surfaces.

Release 1.17.8 (2023-01-30 Emmanuele Bassi <<EMAIL>>)
==============================================================

A new cairo snapshot! And it only took less than one year, this time!

Many thanks to everyone who contributed to cairo, and especially
to (in no particular order):

- Adrian Johnson
- Khaled Hosny
- Behdad Esfahbod
- Matthias Clasen
- Uli Schlachter
- Manuel Stoeckl
- Fujii Hironori
- Tim-Philipp Müller
- Luca Bacci
- Caolán McNamara
- John Ralls

In a continuing effort to reduce the amount of legacy code, and increase
the long-term maintainability of cairo, the following backends have been
removed:

- GL and GLES drawing

Additionally, cairo's Autotools build system has been removed; from now on,
cairo will only support the Meson build system. While the end result should
be identical, further testing is appreciated.

In this snapshot, cairo gained support for rendering COLRv1 fonts, and
rendering SVG and COLRv1 fonts with custom palettes.

Support for macOS and Windows has been improved, with lots of build and bug
fixes.

Lots of safety issues have been fixed, with array bounds checking and
plugging memory leaks, as well as fixes for bugs identified via fuzzying.

This is going to be the last snapshot of the 1.17 development cycle; we only
expect minor bug fixing and improvements until the 1.18.0 release.

Release 1.17.6 (2022-03-18 Emmanuele Bassi <<EMAIL>>)
==============================================================

I spy with my little eye… a cairo snapshot!

First of all, many, many thanks to everyone who contributed to cairo
during this development cycle. A special thank you goes to:

- Adrian Johnson
- Uli Schlachter

for their tireless efforts in ensuring that the lights are still on
in the cairo project.

This snapshot sees the removal of the following backends and platform
support:

- Qt4
- BeOS
- OS/2
- DirectFB
- DRM
- Cogl
- OpenVG

Thanks to all past contributors for their work on them. If you were using
any of these backends then you will need to stick to cairo 1.16.

To offset the removal of the backends above, Adrian Johnson landed the
DWrite font rendering backend on Windows.

There have been multiple improvements in the Quartz backend, courtesy of
John Ralls.

Tim-Philipp Müller has kept the Meson build in top shape.

This snapshot is going to be the **last** release of cairo with the
Autotools build system. The Meson build has seen many improvements and
it is considerably easier to maintain and faster to build.

Release 1.17.4 (2020-11-27 Bryce Harrington <<EMAIL>>)
========================================================================

Thank you to the many people who have contributed the large number of
bug fixes and refinements since 1.17.2.

A particularly noteworthy improvement in this release is the addition of
the meson build system as an alternative to autotools.  Autotools is
still used for producing the releases, so will be the default in the
tarball and presumably will still be preferred by distro packagers of
Cairo.  It should be possible to build the release tarball using meson,
but as this is new functionality consider it still a work in progress.
The meson configuration has striven to track the autotools
implementation but be aware there may still be some differences between
the two.

Continuous Integration configurations have been added that enable
testing on a variety of platforms including Fedora, Windows MSVC, etc.
This work has helped in identifying updates and fixes including
adjusting to changes in API calls in dependencies like rsvg and
fontconfig, and to fix platform-specific build issues.

The cogl Cairo backend underwent significant development this cycle.
Cogl provides GPU accelerated drawing support.  The development work
includes implementation of core functionality, performance
optimizations, and stabilization.

Subpixel positioning support allows improved glyph outlines with the
FreeType font backend.

For a complete log of changes, please see

    https://cairographics.org/releases/ChangeLog.1.17.4

[On a personal note, this will be my last release for Cairo.  My Cairo
time availability has been non-existent (particularly this crazy past
year).  The release process is well documented and hopefully will help
whomever picks up the baton from here.]


Release 1.17.2 (2019-01-31 Bryce Harrington <<EMAIL>>)
========================================================================
This snapshot provides the new support for writing floating point
formats as 16 bpc PNGs, with support for RGBA128F and RGB96F formats.
This new feature increases Cairo's pixman version requirement to 0.36.0.

Beyond this are a range of bugfixes and some work on establishing CI for
Cairo.

For a complete log of changes, please see

    https://cairographics.org/releases/ChangeLog.1.17.2

API Changes
-----------
None

Dependency Changes
------------------
pixman 0.36.0


Release 1.16.0 (2018-10-19 Bryce Harrington <<EMAIL>>)
========================================================================
This new stable release incorporates a number of improvements made in
the four years since 1.14.0.

Of particular note is a wealth of work by Adrian Johnson to enhance PDF
functionality, including restoring support for MacOSX 10.4, metadata,
hyperlinks, and more.

Much attention also went into fonts, including new colored emoji glyph
support, variable fonts, and fixes for various font idiosyncrasies.

Other noteworthy changes include GLESv3 support for the cairo_gl
backend, tracking of SVG units in generated SVG documents, and cleanups
for numerous test failures and related issues in the PDF and Postscript
backends.

For a complete log of changes, please see

    https://cairographics.org/releases/ChangeLog.1.16.0

Features and Enhancements
-------------------------
* Add support for OpenGL ES 3.0 to the gl backend.
* The PDF backend has gained support for a range of widely used
  features, including thumbnails, page labels, metadata, document
  outlines, structured text, hyperlinks, and tags.  Tags permit adding
  logical info such as headings, tables, figures, etc. that facilitates
  indexing, accessibility, text reflow, searching, and extraction of the
  tagged items to other software.  For details on this new PDF
  functionality, see:
    https://lists.cairographics.org/archives/cairo/2016-June/027427.html
* Variable font support.  Variable fonts are single font files with
  various typography characteristics, such as weight or slant, that users
  of the font can adjust between two points.  Effectively this enables a
  single font to behave as multiple fonts.
* Restore MacOSX 10.4 support.  Cairo had dropped 10.4 support when
  moving to the CoreText API.  Now we automatically detect which API to
  use via dynamic linking, so can resume supporting this older version
  of MacOSX.
* Support colored emoji glyphs, stored as PNG images in OpenType fonts.
* Skia backend is removed
* Use Reusable streams for forms in Level 3 Postscript.
* Add CAIRO_MIME_TYPE_EPS mime type for embedding EPS files.
* Add CCITT_FAX mime type for PDF and PS surfaces
* svg: add a new function to specify the SVG document unit
  (Bug #90166)
* Use UTF-8 filenames on Windows


API Changes
-----------
Several new APIs were added.  No existing APIs were altered.

New PDF functionality:

  * cairo_pdf_surface_add_outline
  * cairo_pdf_surface_set_metadata
  * cairo_pdf_surface_set_page_label
  * cairo_pdf_surface_set_thumbnail_size
  * cairo_tag_begin
  * cairo_tag_end
  * CAIRO_STATUS_TAG_ERROR

New error status items for problems relating to PDF tagging:

  * CAIRO_STATUS_WIN32_GDI_ERROR
  * CAIRO_STATUS_FREETYPE_ERROR
  * CAIRO_STATUS_PNG_ERROR

    New error status items for handling of GDI, libfreetype, and libpng
    errors, respectively.


Setting up Win32 surfaces for HDC with alpha channels:

  * cairo_win32_surface_create_with_format

    New API for added PDF functionality (see above), and new error
    status item for problems relating to PDF tagging.

Variable fonts:

  * cairo_font_options_get_variations
  * cairo_font_options_set_variations

Tracking units in SVG documents:

  * cairo_svg_surface_set_document_unit
  * cairo_svg_surface_get_document_unit



Dependency Changes
------------------
None


Performance Optimizations
-------------------------
None


Notable Bug Fixes
-----------------
* Fix thin lines that don't show up when printing in Inkscape due to
  overly aggressive culling.
  (Bug #77298)
* Fix playback of recording surfaces into PDF surfaces, where objects
  with negative coordinates were not getting drawn.  To address this,
  the coordinate systems for PDF and PS have been changed to match
  cairo's coordinate system.  This allows recording surfaces to be
  emitted in cairo coordinates, and results in the same origin being
  used for all operations when using the recording surface XObject.
  Test cases for PDF and PS have also been updated accordingly.
  (Bug #89232)
* Fix "invalidfont" error on some printers when printing PDFs with
  embedded fonts that have glyphs (such as spaces) with
  num_contours == 0.  (Bug #79897)
* Fix missing glyphs such as thin dashes, which get scaled to 0 in
  userspace and thus have their drawing operations culled.  (Bug #94615)
* Fix other oddities caused by variously idiosyncratic fonts.
* Fix a data race in freed_pool discovered by Firefox's cairo usage.
  The patch adads atomic int load and store functions, with relaxed
  memory ordering.  (Bug #90318)
* Handle SOURCE and CLEAR operators when painting color glyphs.
  (Bug #102661)
* Fix falling back to system font with PDFs using certain embedded
  fonts, due to truncated font names.
  (Bug #103249)
* Prevent curved strokes in small ctms from being culled from vector
  surfaces
  (Bug #103071)
* Fix assertion hit with PDFs using Type 4 fonts rendered with user
  fonts, due to error when destroying glyph page.
  (Bug #103335)
* Prevent invalid ptr access for > 4GB images.
  (Bug #98165)
* pdf: Fix internal links pointing to other pages, by pre-calculating
  page heights so that link positions can be calculated more accurately.
* Fix error reporting in the xcb backend if fallback fails.  Instead of
  returning NULL when the X11 server can't do some operation, return a
  surface in an error state.
* Clarify documentation regarding device scale inheritance and the units
  used in cairo_surface_create_similar_image.
  (Bug #99094)
* Call XSync in the xlib backend before setting the error handler to
  ignore errors for certain requests, to make sure all pending errors
  are handled first.
* Fix regression with text containing space character on Win32.
  (Bug: https://gitlab.freedesktop.org/cairo/cairo/issues/339)

For a more comprehensive listing of fixed bugs, see the release notes for the
individual 1.15.x releases.


Release 1.15.14    (2018-09-19 Bryce Harrington <<EMAIL>>)
============================================================================
We're nearly ready to finalize the 1.16.0 release, so this snapshot
can be considered a beta for 1.16.

The most notable change this release is a performance optimization for
windows, discussed below.  Other than that, much of the development
focus was on final polish and stability as we prepare for 1.16.

Some attention went into getting the testsuite passing at least for the
image backend.  The Cairo testsuite depends on external software like
Pixman, and changes in the rendering behavior of these dependencies
change test behavior, leading to false positives.

Results from the Coverity static testing tool were also reviewed.  Most
of the issues flagged were false positives, but there were several
legitimate problems found and fixed.

For a complete log of changes, please see

    https://cairographics.org/releases/ChangeLog.1.15.14

Features and Enhancements
-------------------------
* Add more FreeeType font color conversions to support COLR/CPAL
* Update test reference images against current pixman

API Changes
-----------
None

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
Vasily Galkin introduced a Win32 performance optimization for
CAIRO_OPERATOR_SOURCE when copying data from a backbuffer to an argb32
surface corresponding to a Win32 DC.  With this, argb32 drawing should
perform as fast as typical dibsection-buffered GDI drawing.  See the
Cairo mailing list for April 2018 for data and discussion of the
performance improvements.


Bug Fixes
---------
* Fix crash when rendering Microsoft's Segoe UI Emoji Regular font.
* Fix build breakage with glesv3 enabled due to non-existant glesv3.pc.
* Fix memory leaks found by Coverity
* Fix incorrect null ptr handling found by Coverity
* Fix test compilation when font-config is disabled
* Use _cairo_malloc instead of malloc (Bug #101547) (CVE-2017-9814)
* Fix assertion failure in the freetype backend (Bug #105746)


Release 1.15.12    (2018-04-04 Bryce Harrington <<EMAIL>>)
========================================================================
The main focus for this release is the addition of Variable Font
support.  Variable fonts are single font files with various typography
characteristics, such as weight or slant, that users of the font can
adjust between two points.  Effectively this enables a single font to
behave as multiple fonts.

The Skia backend is disabled in this release, due to severe bitrot, and
will be removed in future releases.  Contact the cairo team if you have
a need of this backend.

For a complete log of changes, please see

    https://cairographics.org/releases/ChangeLog.1.15.12

Features and Enhancements
-------------------------
* Variable font support
* Skia backend is disabled

API Changes
-----------
* cairo_font_options_get_variations() and
  cairo_font_options_set_variations() are added.

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
None

Bug Fixes
---------
* Fix errors in csi-trace --help and --version options
* Fix a 'memory leak' in the image compositor, with
  pixman_glyph_cache_t.
* Fix access of uninitialized memory found by valgrind
  (Bug #91271)
* Fix improper initialization of memory in
  _cairo_ft_font_face_create_for_pattern()
  (Bug #105084)
* Fix multi-monitor virtual desktop with negative coords on Win32
  (Bug #100793)
* Fix issues occurring with older FreeType versions.


Release 1.15.10    (2017-12-07 Bryce Harrington <<EMAIL>>)
========================================================================
This release adds GLESv3 support to the cairo_gl backend, adds
tracking of SVG units in generated svg documents, and cleans up numerous
test failures and related issues in the PDF and Postscript backends.

For a complete log of changes, please see

    https://cairographics.org/releases/ChangeLog.1.15.10

Features and Enhancements
-------------------------
* Add support for OpenGL ES 3.0 to the gl backend.
* Use Reusable streams for forms in Level 3 Postscript.
* Add CAIRO_MIME_TYPE_EPS mime type for embedding EPS files.
* Add CCITT_FAX mime type for PDF and PS surfaces
* svg: add a new function to specify the SVG document unit
  (Bug #90166)
* Use UTF-8 filenames on Windows

API Changes
-----------
* cairo_svg_surface_set_document_unit() and
  cairo_svg_surface_get_document_unit()

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
None

Bug Fixes
---------
* Fix regression in gles version detection
* Fix undefined-behavior with integer math.
* Handle SOURCE and CLEAR operators when painting color glyphs.
  (Bug #102661)
* Convert images to rgba or a8 formats when uploading with GLESv2
* Use _WIN32 instead of windows.h to check for windows build.
* Fix sigabrt printing documents with fonts lacking the mandatory .nodef
  glyph.
  (Bug #102922)
* Prevent curved strokes in small ctms from being culled from vector
  surfaces
  (Bug #103071)
* Fix painting an unbounded recording surface with the SVG backend.
* Fix falling back to system font with PDFs using certain embedded
  fonts, due to truncated font names.
  (Bug #103249)
* Fix handling of truetype fonts with excessively long font names
  (Bug #103249)
* Fix race conditions with cairo_mask_compositor_t
  (Bug #103037)
* Fix build error with util/font-view
* Fix assertion hit with PDFs using Type 4 fonts rendered with user
  fonts, due to error when destroying glyph page.
  (Bug #103335)
* Set default creation date for PDFs
* Prevent invalid ptr access for > 4GB images.
  (Bug #98165)
* Prevent self-copy infinite loop in Postscript surface.
* Fix padded image crash in Postscript surface.
* Fix annotation bugs in PDFs and related memory leaks
* Fix test failures and other assorted issues in ps and pdf code.
* Fix code generation when using GCC legacy atomic operations
  (Bug #103559)
* Fix various compilation warnings and errors.
* Fix various distcheck errors with private symbols, doxygen formatting,
  etc.

Release 1.15.8     (2017-08-29 Bryce Harrington <<EMAIL>>)
========================================================================
This small snapshot provides new colored emoji glyph support, and a
handful of minor fixes.

For a complete log of changes, please see

    https://cairographics.org/releases/ChangeLog.1.15.8

Features and Enhancements
-------------------------
* Support colored emoji glyphs, stored as PNG images in OpenType fonts.


API Changes
-----------
None

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
None

Bug Fixes
---------

* pdf: Fix internal links pointing to other pages, by pre-calculating
  page heights so that link positions can be calculated more accurately.

* image: Fix crash on negative lengths

* win32: Fix initialization of mutexes for static builds

* pdf: Don't emit /PageLabel dict when no labels defined

* font: Fix color font loading on big-endian systems

* font:	Fix color font support infinite-loop with empty glyphs

* Fix off by one check in cairo-image-info.c



Release 1.15.6     (2017-06-13 Bryce Harrington <<EMAIL>>)
========================================================================
This new snapshot incorporates changes over the past half-year since the
1.15.4 snapshot, including all the fixes from the 1.14 release series.

The PDF code continues to be enhanced, and we're restored MacOSX 10.4
support.  Font-related fixes and improved error handling for X round out
the release.

For a complete log of changes, please see

    https://cairographics.org/releases/ChangeLog.1.15.6


Features and Enhancements
-------------------------
* Detect if variable fonts have	synthesized bold/italic	or non-default
  variants, and	     use a fallback font where needed.

* Restore MacOSX 10.4 support.	    Cairo had dropped 10.4 support when
  moving to the CoreText API.  Now we automatically detect which API to
  use via dynamic linking, so can resume supporting this older version
  of MacOSX.


API Changes
-----------
None

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
None

Bug Fixes
---------
* Fix error reporting in the xcb backend if fallback fails.  Instead of
  returning NULL when the X11 server can't do some operation, return a
  surface in an error state.

* Call XSync in the xlib backend before setting the error handler to
  ignore errors for certain requests, to make sure all pending errors
  are handled first.

* Fix text-glyph-range for quartz-font.	 Use 0xFFFF instead of 0 for
  invalid index	       tracking.

* Fix handling of Supplementary Multilingual Plane (SMP) Unicode
  characters in quartz-font.

* Fix various issues in the drm backend	including updating API usage and
  general code cleanup.

* Clarify documentation	regarding device scale inheritance and the units
  used in cairo_surface_create_similar_image.
  Bug #99094.


Release 1.15.4     (2016-12-9 Bryce Harrington <<EMAIL>>)
=======================================================================
This new snapshot incorporates changes over the past year since the
1.15.2 snapshot, including all the fixes from the 1.14 release series.

Of particular note in this snapshot is a wealth of work by Adrian
Johnson to enhance PDF support, as well as numerous bug fixes provided
by him and other contributors.

For a complete log of changes since the last release, please see:

    https://cairographics.org/releases/ChangeLog.1.15.4

Features
--------
* The PDF backend has gained support for a range of widely used
  features, including thumbnails, page labels, metadata, document
  outlines, structured text, hyperlinks, and tags.  Tags permit adding
  logical info such as headings, tables, figures, etc. that facilitates
  indexing, accessibility, text reflow, searching, and extraction of the
  tagged items to other software.  For details on this new PDF
  functionality, see:

    https://lists.cairographics.org/archives/cairo/2016-June/027427.html


API Changes
-----------

  cairo_win32_surface_create_with_format

    Added a cairo API to set up Win32 surfaces for HDC with alpha channels.

  cairo_pdf_surface_add_outline
  cairo_pdf_surface_set_metadata
  cairo_pdf_surface_set_page_label
  cairo_pdf_surface_set_thumbnail_size
  cairo_tag_begin
  cairo_tag_end
  CAIRO_STATUS_TAG_ERROR

    New API for added PDF functionality (see above), and new error
    status item for problems relating to PDF tagging.

  CAIRO_STATUS_WIN32_GDI_ERROR
  CAIRO_STATUS_FREETYPE_ERROR
  CAIRO_STATUS_PNG_ERROR

    New error status items for handling of GDI, libfreetype, and libpng
    errors, respectively.

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
None

Bug Fixes
---------
* Bug fixes from 1.15.2 (see the 1.15.2 NEWS for details)

* Fix playback of recording surfaces into PDF surfaces, where objects
  with negative coordinates were not getting drawn.  To address this,
  the coordinate systems for PDF and PS have been changed to match
  cairo's coordinate system.  This allows recording surfaces to be
  emitted in cairo coordinates, and results in the same origin being
  used for all operations when using the recording surface XObject.
  Test cases for PDF and PS have also been updated accordingly.
  (Bug #89232)

* Fix "invalidfont" error on some printers when printing PDFs with
  embedded fonts that have glyphs (such as spaces) with
  num_contours == 0.  (Bug #79897)

* Fix missing glyphs such as thin dashes, which get scaled to 0 in
  userspace and thus have their drawing operations culled.  (Bug #94615)

* Fix other oddities caused by variously idiosyncratic fonts.

* Fix deadlock when destruction of a scaled font indirectly triggers
  destruction of a second scaled font, causing the global cache to be
  locked twice.  (Bug #93891)

* Fix X errors reported to applications when shmdt() is called before
  the Attach request is processed, due to missing xcb and xlib calls.

* Fix random failure in record-paint-alpha-clip-mast test case, caused
  by an incorrect assumption that a deferred clear can be skipped.
  (Bug #84330)

* Fix crash when dealing with an XShmGetImage() failure, caused by a
  double free in _get_image_surface().  (Bug #91967)

* Fix invalid execution of ASCII85 data by the PS interpreter that the
  image operator didn't use, by flushing the extraneous data after
  drawing the image.  (Bug #84811)

* Fix decoding of Adobe Photoshop's inverted CMYK JPEG files in PDF
  export.

* Fix unbounded surface assertion in win32-print code.

* Fix a data race in freed_pool discovered by Firefox's cairo usage.
  The patch adads atomic int load and store functions, with relaxed
  memory ordering.  (Bug #90318)

* Cleanup debugging text sent to stdout instead of log.  (Bug #95227)

* Fix build issue when using non-GNU strings utility.  (Bug #88639)

* Fix build of cairo modules as regular modules, not as versioned shared
  libraries.  (Bug #29319)

* Fix build on win32 using gcc 5.4.

* Fix build of script backend to require zlib.

* Update test suite reference images using Debian Jessie 64-bit and
  poppler current as of June, 2016.

* Various improvements to documentation and tests, compiler warning
  fixes, and an assortment of code refactoring and cleanup.


Release 1.15.2     (2015-12-10 Bryce Harrington <<EMAIL>>)
========================================================================
This release is largely a rollup to include a variety of fixes that
didn't make the cut for the stable 1.14.2 and 1.14.4 releases, as well
as all the fixes from those releases.  Notably this includes a highly
requested new API for Win32 surfaces.

For a complete log of changes since the last release, please see:

    https://cairographics.org/releases/ChangeLog.1.15.2

Features
--------
None

API Changes
-----------

  cairo_win32_surface_create_with_format

    Added a cairo API to set up Win32 surfaces for HDC with alpha channels.

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
None

Bug Fixes
---------
* All the bug fixes from 1.14.2, 1.14.4, and 1.14.6

* Fix xcb/xlib compilation and calls.  Make image boxes behave when SHM
  is not available.

* Fix various issues with printing and transparent images on Win32.

* Fix thin lines that don't show up when printing in Inkscape due to
  overly aggressive culling.
  (Bug #77298)

* Fix broken printing via pdf when glyph 0 is used for rendering,
  resulting in missing spaces and letters.
  (Bug #89082)

* Fix crash for certain glyphs in opentype fonts.
  (Bug #91902)

* Fix incorrect rendering of SVG paths with more than one subpath.  If
  more than one trap is passed in then it's guaranteed that the returned
  traps will have their left edge to the left of their right edge, but
  if only one trap is passed in then the function always returns without
  doing anything.
  (Bug #90984)

* Improve rendering with Quartz to better match pixman's blending and
  filtering behavior.


Release 1.14.6    (2015-12-09  Bryce Harrington <<EMAIL>>)
========================================================================
Simple bugfix release to fix one Windows issue.

For a complete log of changes since 1.14.4, please see:

    https://cairographics.org/releases/ChangeLog.1.14.6

Features
--------
None

API Changes
-----------
None

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
None

Bug Fixes
---------
* Fix failure on Windows due to reference of the function
  cairo_win32_surface_create_with_format(), which isn't included in the
  1.14.4 release. (Bug #92771)


Release 1.14.4    (2015-10-28  Bryce Harrington <<EMAIL>>)
========================================================================
Just in time for Halloween we see another bug-fix release for Cairo.
This brings a few dozen straightforward bug fixes with no API changes.

In addition, this includes a typical assortment of fixes to tests,
cleanup of warnings and memory leaks, correction of misspellings,
updates to documentation, etc.

For a complete log of changes since 1.14.2, please see:
 
    https://cairographics.org/releases/ChangeLog.cairo-1.14.4

Features
--------
None

API Changes
-----------
None

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
None

Bug Fixes
---------
* Avoid appending empty slots to user data arrays.  Fixes a memory
  consumption regression since commit 9341c254a.

* Return a better error (file-not-found) when setting up pango on
  devices where the font files don't have read permissions.

* Fix regression in the font size of canvas text in Inkscape when
  compiled with the Quartz backend.  (Bug #84324)

* Fix _cairo_gl_shader_bind_matrix() to maintain compatibility with
  OpenGL ES 2.0.  Manually transpose the matrix.

* Fix incorrect font descriptor conversion when the font matrix yy is
  negative. (Bug #90538)

* Fix crash when using a complex path for clip and stroke due to
  discarding the intersection exactly at the top edge.
  (Bug #74779)

* Fix cairo_get_locale_decimal_point() on Android

* Fix compilation problem on AIX due to conflicting usage of symbol
  'jmpbuf'.  (Bug #89339)

* Fix broken rendering with XCB due to snapshotting of uploaded part of
  surfaces.  (Bug #67505)

* Fix loss of alpha when copying a mask for a cairo recording surface,
  resulting in a double copy.  (Bugs #73038, #73901)

* Fix incorrect recording of certain paths with script surfaces.
  (Bug #91054)

* Fix typo in definition of MAYBE_WARN in configure script.
  (Bug #89750)

* Fix use of filename variable after it's been freed.
  (Bug #91206)

* Fix out of bounds access when printing pattern.
  (Bug #91266)

* Fix incorrect size calculation in glyph cache unlocking for Cairo GL
  compositor.
  (Bug #91321)

* Fix memory leak in _cairo_gl_pattern_texture_setup()
  (Bug #91537)

* Fix transparent images in win32-print.
  (Bug #91835)

* Fix _put_shm_image_boxes and _put_image_boxes when no SHM available
  with XCB.


Release 1.14.2 (2015-03-09 Bryce Harrington <<EMAIL>>)
====================================================================
This release provides collected bug fixes, along with one feature
enhancement for the xcb backend, and a small performance improvement for
fonts.

The running theme of the bug fixes is platform-specific issues, both
build and run-time.  Platforms with fixes include Sparc, AIX, Windows
(mingw), and Windows (MSVC8).  Memory leaks, valgrind issues, and PDF
issues round out our list.

It's come to light that changes in cairo 1.14 resulted in breakage on
MacOS X 10.4.  We've not yet determined whether to fix up the support,
or excise the 10.4-specific code and support only OS X 10.5 or newer.
Meantime, we'll only advertise cairo as working on OS X 10.5.

Features
--------
 * Improve xcb's handling of per-screen subpixel ordering.  If no
   Xft.rgba property is specified, default to the screen's subpixel
   order.

API Changes
-----------
None

Dependency Changes
------------------
None

Performance Optimizations
-------------------------
 * Improve performance of cpu_to_be32 and be32_to_cpu, making truetype
   subsetting of large fonts run about 15% faster.

Bug Fixes
---------
 * Fix unaligned access on sparc with the compact font format (CFF).
   Unlike truetype, all data in CFF is not aligned.
   (Debian bug #712836)
 * Fix unaligned access on sparc with tor-scan-converter's memory pool.
 * Fix crash when loading a PDF with a transformed image.
   (fdo bug #85151)
 * Fix regression on mingw for bigendian test due to removal of file
   extension for executables.
   (fdo bug #85120)
 * Fix handling of backslash in PDF interpreter
   (fdo bug #85662)
 * Fix crash in xlib and xcb renderers when swapping a 0-sized glyph
 * Fix bug with RTL text in PDF operators
   (fdo bug #86461)
 * Fix compilation 'cairo-path-stroke-traps.c' with MSVC8
   (fdo bug #84908)
 * Fix crash in _fill_xrgb32_lerp_opaque_spans when a span length is
   negative.
 * Fix valgrind error by releasing pattern created by
   cairo_pattern_create_rgb().
 * Fix valgrind errors when running cairo-test-suite.
 * Fix memory leak in recording surface replays
   (fdo bug #87898)
 * Fix destruction of fonts in api-special-cases test.
   (fdo bug #87567)
 * Fix duplicated surface push on similar-image, preventing trivial GTK3
   program traces from being replayable, with an error message about
   invalid values for the size of the input.
   (fdo bug #73580)
 * Fix crash when win32 surface's image size does not cover the entire
   surface.
   (fdo bug #53121)
 * Fix crash due to obsolete CGFontGetGlyphPath call
   (fdo bug #84324)
 * Fix several build issues on AIX
   (fdo bugs #89338, #89340, #89356, #89354)
 * Fix various documentation warnings and errors


Release 1.14.0 (2014-10-13 Bryce Harrington <<EMAIL>>)
====================================================================
Hard to believe it's been over a year since our last release, but it's
not for lack of activity.  This release includes contributions of a wide
assortment of bug fixes, build system improvements, warnings cleanups,
codebase refactoring, test suite repairs, and static analysis work.

This release is lighter on features (compared with 1.12.10) but includes
a highly demanded rehaul of our image downscaling functionality, which
solves a serious problem experienced by Inkscape users when shrinking
embedded bitmaps in SVG files.  The new scaling algorithms are used by
the image backend and by other backends as needed for fallbacks.


Features
--------

  Filtering improvements for the image backend, in particular
  down-scaling of images produces filtered images that depend on all the
  pixels of the source. When using the image backend you get the
  following settings:

  CAIRO_FILTER_GOOD: uses a box filter for scales less than .75 in
    either direction. For scales larger than this, the same filter as
    CAIRO_FILTER_BILINEAR is used.

  CAIRO_FILTER_BEST: uses a Catmull-Rom filter always. When upscaling
    more than 2x this will produce anti-aliased square pixels, similar
    to OS/X.

  CAIRO_FILTER_GAUSSIAN: uses PIXMAN_FILTER_BEST, which in current
    pixman is the same as BILINEAR. (This is subject to change in the
    future).

  xlib and xcb also use the image fallback for GOOD/BEST filters, but
  note that other backends do not implement these filtering fixes yet,
  however other actions may cause them to use an image fallback which
  will cause these filters to be used.

  Improve handling of device transformation and scaling, allowing Cairo
  to now support scaling at a device level, permitting easier, more
  transparent HiDPI support.

  Support JBIG2 mime data in PDF.  This allows embedding of more
  compressed JPEG formats within PDF, rather than including the full
  uncompressed image.  Also, reduce the number of transparency groups
  used by PDF to keep the file size small and viewing/printing of the
  PDF fast.

  Expand the embedding section to include stencil mask support.

  Reorder font declarations to be in natural order.

  Update the Skia backend to build against current Skia (as of June
  2014).

  Drop Link-Time Optimization (LTO) support from build system.  This
  seems to have caused much trouble for unclear benefit, and most
  distros are reverting or disabling it anyway.

  Optimize VBO size on GL to 1M and to 16k for EGL.  This improves
  (theoretical) performance for desktop GLX use cases while avoiding
  hitting VBO memory size limitations on embedded devices.

API Changes
-----------

  cairo_surface_set_device_scale, cairo_surface_get_device_scale:

    Sets a scale that is multiplied to the device coordinates
    determined by the CTM when drawing to @surface. One common use for
    this is to render to very high resolution display devices at a scale
    factor, so that code that assumes 1 pixel will be a certain size
    will still work.

  cairo_egl_device_get_display, cairo_egl_device_get_context:

    Support get/set of EGLContext and EGLDisplay for egl-based cairo
    devices, similar to GLX.

Dependency Changes
------------------

  Cairo now requires glib 2.14 for its gobject helper functions,
  and pixman 0.30 for downscaling.


Bug fixes
---------

  Don't embed CMYK Jpeg images in svg.

  Fix tests to place output in proper location.

  Fix determination of alpha for all surfaces when recording.

  Extend oversize check to cairo_gl_surface_create_for_texture, so an
  error surface is returned if the texture is too large to render to.

  Fix embedding of mime data in PDF and PS files.

  Remove useless error handling in *_reply() functions in XCB.

  Fix a double-free exposed by multithreaded apps creating and
  destroying the same font concurrently.
  https://bugs.freedesktop.org/show_bug.cgi?id=69470

  Fix corrupt stacks produced by bugs in operand emission for trace.

  Fix out of bounds array access in format cache for xlib

  Don't rename glyphs used by seac operator.  This can cause certain
  combined characters to use their decorations (e.g. umlauts on ö) to be
  lost during printing of PDFs using evince.
  https://bugs.freedesktop.org/show_bug.cgi?id=70364

  Fix crash on calling cairo_create with a finished surface

  Fix SSIZE_T definition problem when making with MSYS on Windows7

  Fix one off issue in gl context cleanup

  Fix usage of CAIRO_STACK_ARRAY_LENGTH

  Fix rectangle stroke with non rectilinear pen

  Fix imagemask with pattern source failure on some printers.  This bug
  could cause files converted using pdftops to fail for example on Ricoh
  printers, or opening in Adobe Distiller on Windows.
  https://bugs.freedesktop.org/show_bug.cgi?id=69485

  Fix whitespace in font names

  Fix page size in generated PDFs.  When printing using pdftocairo on
  larger page sizes, such as 11x17, the image would be cropped to letter
  size.
  https://bugs.freedesktop.org/show_bug.cgi?id=73452

  Fix path-currentpoint test by preserving current-point in
  copy_path()/append_path() sequence

  Fix generation of HTML in code docs for
  cairo-format-stride-for-width.  Raw HTML code was being passed
  to the browser, instead of displaying normally.
  https://bugs.freedesktop.org/show_bug.cgi?id=63257

  Fix spelling of "tessellator" throughout code.  We're using the
  American rather than British spelling of this word.
  https://bugs.freedesktop.org/show_bug.cgi?id=50411

  Fix crash in pixman_image_composite32

  Fix crash when trying to modify a (const) all-clipped cairo_clip_t
  https://bugs.freedesktop.org/show_bug.cgi?id=75819

  Add check_composite method to all compositors, to fix crashes in the
  test suite.

  Fix crash in Firefox when scrolling on certain pages.

  Fix memory leaks found by static analysis.

  Fix build of any2ppm if fork is not available.

  Fix broken build for Qt backend, due to missing libstdc++.

  Fix typo in two cairo_uint128 functions.  Fixes potential build issues
  on systems without a uint128 type.

  Fix build when --enable-pdf=no

  Fix cache_frozen assertions for Win32 print.

  Correctly check for xcb image surface for inplace upload

  Fix webkit-based web browser crashes due to empty boxes by skipping
  over them when tesselating.

  Make pixman, libpng, and zlib paths commandline configurable for win32
  builds.

  Fix image scale on Win32 when GDI scale is not identity.

  Fix float endian configure test when using clang -O4

  Fix compilation with Android bionic libc

  Don't try to build util/sphinx on Windows

  Fix loss of precision when emitting joins.  This was caused by
  discrepancies in line gradients when passing trapezoids around.

  Fix loss of precision and associated rendering issues in
  cairo-tor-scan-converter from projection onto sample grid.

  Fix pixman oversampling of neighbouring edges within a cell by
  eliminating self-intersections for the pixman traps compositor.

  Fix multi-line string splitting in PDFs

  Various cleanups and fixes to warnings, documentation, tests, and
  build system.  Improve error handling and return value checks.
  Cleanup XFAIL tests and reference images.  Cover recently added
  functionality.


Release 1.12.16 (2013-08-21 Chris Wilson <<EMAIL>>)
===================================================================
Thanks to everybody who reported a bug and helped us develop a fix,
we have amassed quite a few bug fixes. There are still more outstanding
bugs that seek attention and a little bit of TLC, but this release has
been delayed long enough...

Bug fixes
---------

  Set the correct orientation for simple boxes with a negative scale
  factor.

  Fix the creation of the shading dictionary in PDF.

  Fix a crash in PDF when incorporating an image with CAIRO_EXTEND_PAD.
  https://bugs.freedesktop.org/show_bug.cgi?id=61451

  Avoid upscaling bitmap fonts if possible.

  Fix an assertion failure within the mempool allocator for shared memory.

  Fix allocation size for CFF subsets.

  Export cairo_matrix_t for GObject bindings.

  Fix a double free in the Quartz backend.
  https://bugs.freedesktop.org/show_bug.cgi?id=62885

  Fix origin of GDI StretchBlits for the Windows backend
  https://bugs.freedesktop.org/show_bug.cgi?id=61876

  Fix error propagation for requests to create a similar surface with
  negative size.
  https://bugs.freedesktop.org/show_bug.cgi?id=63196

  Fix complex clipping of trapezoids with regions
  https://bugzilla.gnome.org/show_bug.cgi?id=697357

  Stop leaking the image data when loading PNGs

  Fix unbounded operations with a clip mask through the span compositor
  https://bugs.freedesktop.org/show_bug.cgi?id=61592

  Add missing checks before rendering to a finished surface - so we return
  an error rather than hit an assert.
  https://bugs.freedesktop.org/show_bug.cgi?id=68014

  Prevent an assertion failure when creating similar GL surfaces larger
  than supported by hardware.

  Prevent a double free of a similar image under Windows.
  https://bugs.freedesktop.org/show_bug.cgi?id=63787


Release 1.12.14 (2013-02-10 Chris Wilson <<EMAIL>>)
===================================================================
In the last week we had a few more bugs reported and promptly resolved.
As these are a combination of regressions and stability issues, it is
time for a prompt update and release. Many thanks to everyone for
testing and reporting issues, and helping to make Cairo better.

Bug fixes
---------

  Prevent user callbacks accessing user-data during destroy to prevent
  use-after-free bugs.
  https://bugzilla.mozilla.org/show_bug.cgi?id=722975

  Use standard names for glyphs in subset fonts (PDF).
  https://bugs.freedesktop.org/show_bug.cgi?id=60248

  Fix detection of Win98. The logic for detecting Win98 (and its broken
  AlphaBlend()) was inverted, disabling AlphaBlend() for everyone.

  Prevent numeric overflow from extrapolating polygon edges to the clip
  boundary and causing severe render artifacts.
  https://bugs.freedesktop.org/show_bug.cgi?id=60489

  Fix computation of glyph string coordinates when breaking up runs
  for xlib.

  Fix an assertion in the win32 backend for failing to clear its
  similar-images.
  https://bugs.freedesktop.org/show_bug.cgi?id=60519


Release 1.12.12 (2013-01-31 Chris Wilson <<EMAIL>>)
===================================================================
The goal of this release is to fix the synchronisation problems that
were exhibited in the SHM transport for cairo-xlib. This cropped up
any place that tried to rapidly push fresh pixel data to the X server
through an ordinary image surface, such as gimp-2.9 and evince.

Bug fixes
---------

   Avoid replacing the entire image when uploading subimages
   https://bugs.freedesktop.org/show_bug.cgi?id=59635

   Force synchronisation for scratch SHM image buffers, so that we do
   not overwrite data as it is being read by X.
   https://bugs.freedesktop.org/show_bug.cgi?id=59635 (also)

   Fix typos in detecting multisampling for the GL (MSAA) backend.

   Fix a memory leak in the GL (MSAA) backend.

   Fix a reference counting bug when mapping a GL surface to an image.


Release 1.12.10 (2013-01-16 Chris Wilson <<EMAIL>>)
===================================================================
A heap of bug fixes everywhere, and the gradual completion of the MSAA
backend for cairo-gl. Perhaps the most noteworthy set of the bugfixes
was the crusage lead by Behdad Eshfabod to make font handling by
pango/cairo/fontconfig fully threadsafe. This testing revealed a couple
of races that needed fixing in Cairo's scaled-font and glyph cache.

Bug fixes
---------

  Append coincident elements to the recording's surface bbtree so that
  the list is not corrupted and the overlapping elements lost.

  Fix cairo-trace to correctly record map-to-image/unmap-image and then
  replay them.

  Ignore MappingNotifies when running the XCB testsuite as they are sent
  to all clients when the keyboard changes. The testsuite would detect
  the unexpected event and complain.

  Handle very large images in the XCB backend.

  Fix a memory leak in the xlib/shm layer, and prevent use of the SHM
  surfaces after the display is closed.
  https://bugs.freedesktop.org/show_bug.cgi?id=58253

  Handle resizing of bitmap fonts, in preparation for a fix to
  fontconfig to correctly pass on the user request for scaling.

  Always include subroutine 4 (hint replacement idion) when subsetting
  type 1 fonts in order to prevent a crash in cgpdftops on Mac OS/X

  Fix a couple of typos in the cairo-gobject.h header files for
  introspection.

  Prevent a mutex deadlock when freeing a scaled-glyph containing a
  recording-surface that itself references another scaled-glyph.
  https://bugs.freedesktop.org/show_bug.cgi?id=54950

  Make scaled-font cache actually thread-safe and prevent
  use-after-frees.

  Restore support for older versions of XRender. A couple of typos and a
  few forgotten chunks prevented the xlib compositor from running
  correctly with XRender < 0.10. Note that there are still a few
  regressions remaining.


Release 1.12.8 (2012-11-24 Chris Wilson <<EMAIL>>)
===================================================================
Another couple of weeks and a few more bugs have been found and fixed,
it is time to push the next point release. Many thanks to everyone who
reported their issues and helped us track down the bugs and helped
testing the fixes.

Bug fixes
---------

  Expand the sanity checking for broken combinations of XSendEvent and
  ShmCompletionEvent.

  Notice that "The X.Org Foundation" sometimes also identifies itself
  as "The Xorg Foundation".

  Handle various ages of libXext and its Shm headers.

  Fix the invalid clipping of the source drawable when using SHM
  transport to upload images.
  https://bugs.freedesktop.org/show_bug.cgi?id=56547

  Handle all Type1 postscript operators for better font compatibility.
  https://bugs.freedesktop.org/show_bug.cgi?id=56265

  Fix a couple of memory leaks in Type1 font subsetting
  https://bugs.freedesktop.org/show_bug.cgi?id=56566

  Tighten the evaluation of the start/stop pen vertices, and catch a few
  instances where we would use a fan instead of a bevel.
  https://bugs.freedesktop.org/show_bug.cgi?id=56432

  Fix assumption that geometric clipping always succeeds with the
  span-compositor.
  https://bugs.freedesktop.org/show_bug.cgi?id=56574

  Fix call to spline intersection when evaluating whether a stoke is
  visible.

  Remember to copy inferior sources when using SHM to readback the
  surface for use as a source.

Release 1.12.6 (2012-10-22 Chris Wilson <<EMAIL>>)
===================================================================
Thanks to everyone who download cairo-1.12.4 and gave us their feedback.
It truly was invaluable and has helped us to fix many portability issues
that crept in with some of the new features. This release aims to fix
those stability issues and run on a wider range of systems.

Bug fixes
---------

  Fix the recording surface to actually snapshot the source and so fix
  PDF drawing.

  Calling XSendEvent with an XShmCompletionEvent is incompatabile with
  older Xorg servers.

  Reorder CloseDisplay chain so that XShm is not reinstantiated after
  shutdown, causing a potential crash if the Display was immediately
  recreated using the same memory address.

  Make sure that the Xserver has attached to the SHM segment before
  deleting it from the global namespace on systems that do not support
  deferred deletion.

  Type1 subsetting support for PDF (and PS) was once again improved to
  work with a larger number of PDF readers.

  GLESv2 build fixes and improved support for embedded GPUs.

  Tweak the invisible pen detection for applications that are currently
  using too large values for geometric tolerance.

  A build fix for older freetype libraries.


Release 1.12.4 (2012-10-05 Chris Wilson <<EMAIL>>)
===================================================================
More bugs, and more importantly, more fixes. On the cairo-gl side, we
have refinements to the MSAA compositor which enables hardware
acceleration of comparatively low-quality antialiasing - which is useful
in animations and on very high density screens. For cairo-xlib, we have
finally enabled SHM transport for image transfers to and from the X
server. A long standing required feature, SHM transport offers a notable
reduction in rendering latency by reducing the number of copies
required to upload image data - given hardware and driver support,
cairo-xlib can now perform zero copy uploads onto the GPU. And as usual
Adrian Johnson has been very busy fixing many different corner cases in
cairo-pdf, improving opacity groups and font subsetting. Last, but not
least, for cairo-image Søren Sandmann Pedersen added support for
rendering glyphs to pixman and using that from within cairo. The new
glyph rendering facility reduces the overhead for setting up the
compositing operation, improving glyph thoughput for the image backend
by a factor of about 4. And before he did so, he also fixed up a few
bugs in the existing glyph rendering code. So many thanks to Andrea
Canciani, Adrian Johnson, Chuanbo Weng, Dongyeon Kim, Henry Song, Martin
Robinson, Søren Sandmann Pedersen and Uli Schlachter for their
contributions, finding and fixing bugs.

Bug fixes
---------

 Interior boxes were being dropped when amalgamating regions during
 tessellation.
 https://bugs.freedesktop.org/show_bug.cgi?id=49446

 Allow building without gtk-doc installed

 Invalid edge generation whilst reducing complex polygons.
 https://bugs.freedesktop.org/show_bug.cgi?id=50852

 Stroking around tight cusps

 Use locale correct formats for reading font subsetting and valid
 buffers.
 https://bugs.freedesktop.org/show_bug.cgi?id=51443

 Ensure that the type1 subset includes all the glyph encodings
 https://bugs.freedesktop.org/show_bug.cgi?id=53040

 Upload the whole source for a repeating pattern.
 https://bugs.freedesktop.org/show_bug.cgi?id=51910

 Fix damage tracking to handle continuation chunks correctly and so
 prevent crashes on win32.
 https://bugs.freedesktop.org/show_bug.cgi?id=53384

 Avoid emitting miter joins for degenerate line segments
 https://bugzilla.mozilla.org/show_bug.cgi?id=407107

 Convert the relative path semgents into the backend coordinates
 and then back again to user coordinates (cairo_copy_path,
 cairo_append_path)
 https://bugs.freedesktop.org/show_bug.cgi?id=54732

 Fix extents computations for a degenerate path consisting only of a
 move-to
 https://bugs.freedesktop.org/show_bug.cgi?id=54549

 Prevent crashing on a degenerate project edge after polygon
 intersection
 https://bugs.freedesktop.org/show_bug.cgi?id=54822



Release 1.12.2 (2012-04-29 Chris Wilson <<EMAIL>>)
===================================================================
After such a long gestation period for the release of Cairo 1.12, we
inevitably accumulated a few bugs that were flushed out by broadening the
test base. Thanks to everybody who tried the release, apologies to any one
unfortunate enough to encounter a bug and many thanks for reporting it. As
a result Adrian Johnson, Alexandros Frantzis, Andrea Canciani, Kalev
Lember, Maarten Bosman, Marcus Meissner, Nis Martensen and Uli Schlachter
have squashed many more bugs and improved the documentation. I would
strongly recommend everyone to upgrade to cairo-1.12.2.
-Chris

Bug fixes
---------

 Allow applications to create 0x0 xlib surfaces, such as used by LibreOffice.
 https://bugs.freedesktop.org/show_bug.cgi?id=49118

 Trim composite extents for SOURCE/CLEAR operators to the mask.

 Use fallback fonts in PDF for unhandled computed glyph widths
 https://bugs.freedesktop.org/show_bug.cgi?id=48349

 Handle snapshots of recording surfaces for analysing pattern extents.
 Fixes a regression of reporting the PDF bounding box as being the page size.

 Fix allocation size for PDF pattern ids.
 Bugzilla: https://bugs.freedesktop.org/show_bug.cgi?id=49089

 Fix emission of rectilinear dashed segments, with and without scaling, and
 application of degenerate line joins.

 Clamp unbounded fixup polygons to the clip extents.

 Prevent infinite loop due to rounding errors whilst incrementing along dashes.

 Prevent overflow for inline a8 span filling.

 Miscellaneous build fixes for Cygwin on Windows and Solaris.

Release 1.12.0 (2012-03-23 Chris Wilson <<EMAIL>>)
===================================================================
It's taken over 18 months, but the wait is finally over. A new cairo release!
We are pleased to announce a new stable release of Cairo that brings many
new features and performance improvements, all whilst maintaining
compatibility with cairo-1.0 and all releases since. We recommend anyone
using a previous release of Cairo to upgrade to 1.12.0.

The major feature of this release is the introduction of a new procedural
pattern; the mesh gradient. This, albeit complex, gradient is constructed
from a set of cubic Bezier patches and is a superset of all other gradient
surfaces which allows for the construction of incredibily detailed patterns.
In PDF parlance, the mesh gradient corresponds with type 7 patterns. Many
thanks to Andrea Canciani for bringing this to Cairo, and for his work on
making gradient handling robust.

Not content with just adding another procedural pattern, Cairo 1.12 also
adds new API to create a callback pattern,
cairo_pattern_create_raster_source, that allows the application to
provide the pixel data for the region of interest at the time of
rendering. This can be used for instance, by an application to decode
compressed images on demand and to keep a cache of those decompressed
images, independently of Cairo. When combined with the recording
surface, it should form a useful basis for a deferred renderer.

With the release of cairo-1.12, we also introduce a new supported
backend for interoperating with X using XCB. Uli Schlachter, also
maintainer of awesome and contributor to libxcb, has volunteered to
maintain cairo-xcb for us. Thanks Uli!

For cairo-1.12, we have also added some common API to address any
surface as an image and so allow direct modification of the raster data.
Previously, only the Quartz and Win32 backends supported a very narrow
interface to allow for efficient pixel upload. Now with
cairo_surface_create_similar_image, cairo_surface_map_to_image, and
cairo_surface_unmap_image, Cairo exports a consistent method for
treating those surfaces as an image and so allow modification inplace.
These are the same routines used internally, and should support
efficient transfer or direct mapping of the target surfaces as
applicable.

Another focus over the past year has been to address many performance
issues, without sacrificing the composition model. To accomplish the
goal, once again the rasterisation pipeline was overhauled and made
explicit, giving the backends the freedom to implement their own
specific pipeline whilst also providing a library of common routines
from which to build the pipeline. For instance, this allows the image
backend and the gl backend to composite scan line primitives inplace,
and to then implement custom fallbacks to catch the corner cases that do
not map onto their fastest paths. Similarly, this allows for the Xlib
backend to implement trapezoidation without compromising the other
backends, yet still allow for the pipeline to be used elsewhere for
testing and fallbacks. Clipping was once again overhauled, so that the
common cases for the raster pipelines could be captured and processed
with fast paths with the emphasis on performing geometric clipping to
reduce the frequency of using multi-pass clipmasks. Stroking was made
faster, both by providing specialised fast-paths for simple, yet frequent,
cases (such as stroking around a rectangle) and by reducing the number
of edges generated by the general stroker.

As part of the focus on performance, Cairo 1.12 introduces some
antialias hints (NONE, FAST, GOOD, BEST) that are interpolated by the
rasterisers to fine tune their performance versus quality. Cairo 1.12
also introduces a new observation architecture,
cairo_surface_observer_t, which can be used to analyse the amount of
time consumed by drawing commands and help identify inefficiencies in
both Cairo and the application.

Last, but by no means least, the OpenGL backend has seen significant
work including the port to GLESv2 and the exploitation of advanced
hardware features. Interesting times.

As always, I would like to thank everyone who contributed to Cairo,
not only through writing code, but also submitting documentation, bug
reports, suggestions and generally having fun with Cairo! In particular
though this release could not have happened without the efforts of
Adrian Johnson, Alexandros Frantiz, Andrea Canicani, Martin Robinson,
Nis Martensen, and Uli Schlachter. Thanks.
-Chris

Snapshot 1.11.4 (2012-13-12)
============================
The cairo community is pleased to finally announce the long aniticpated
release candidate for 1.12, 1.11.4, of the cairo graphics library. This
is the first major update to cairo in over a year and brings a large
number of new features; undoubtably a few bugs as well.

While many people have contributed and have helped to test the release,
providing feedback on 1.10 and suggesting improvements, this release
is the result of a few persevering souls who deserve recognition for their
outstanding contributions: Andrea Canciani (all round bug fixing,
performance tuning and master of the gradients), Adrian Johnson (PDF
supremo) and Uli Schlachter (who stepped forward as maintainer for the
XCB backend).

Major additions since 1.11.2:

 * cairo_surface_map_to_image API for pixel level access to any surface

 * New antialias hints to control the trade-off between speed and quality

 * A callback pattern, cairo_pattern_create_raster_source, for lazy
   decoding of image data.

 * cairo_surface_observer_t, a new type of surface to gather performance
   statistics

 * XCB as a supported backend

 * A rewritten compositor pipeline for performance improvements for, but not
   limited to, the xlib and image backends.
   From ION and PineView through to SandyBridge, every machine I have shows
   across the board performance improvement on the cairo-traces:

   i5-2520m	gnome-system-monitor:	5.97x speedup
   pnv		gnome-system-monitor:	4.86x speedup
   i5-2520m	firefox-asteroids:	4.66x speedup
   pnv		firefox-asteroids:	4.43x speedup
   image	firefox-canvas:		3.82x speedup
   i5-2520m	firefox-canvas-alpha:	3.49x speedup
   image	firefox-asteroids:	2.87x speedup
   pnv		firefox-talos-svg:	2.83x speedup
   ion		grads-heat-map:		2.75x speedup
   pnv		firefox-canvas-alpha:	2.66x speedup
   image	gnome-system-monitor:	2.66x speedup
   image	swfdec-giant-steps:	2.46x speedup
   image	firefox-canvas-alpha:	2.14x speedup
   i5-2520m	firefox-talos-svg:	2.03x speedup
   image	grads-heat-map:		2.02x speedup
   ion		gnome-system-monitor:	2.00x speedup
   pnv		firefox-particles:	1.99x speedup
   i5-2520m	grads-heat-map:		1.96x speedup
   pnv		firefox-canvas:		1.92x speedup
   ion		firefox-particles:	1.80x speedup
   image	poppler-reseau:		1.77x speedup
   pnv		xfce4-terminal-a1:	1.72x speedup
   image	firefox-talos-svg:	1.65x speedup
   pnv		grads-heat-map:		1.63x speedup
   i5-2520m	firefox-canvas:		1.63x speedup
   pnv		swfdec-youtube:		1.62x speedup
   image	ocitysmap:		1.59x speedup
   i5-2520m	firefox-fishbowl:	1.56x speedup
   i5-2520m	poppler-reseau:		1.50x speedup
   i5-2520m	evolution:		1.50x speedup
   i5-2520m	midori-zoomed:		1.43x speedup
   pnv		firefox-planet-gnome:	1.42x speedup
   i5-2520m	firefox-talos-gfx:	1.41x speedup
   i5-2520m	gvim:			1.41x speedup
   pnv		ocitysmap:		1.37x speedup
   image	poppler:		1.31x speedup
   ion		firefox-canvas-alpha:	1.35x speedup
   ion          firefox-talos-svg:	1.34x speedup
   i5-2520m	ocitysmap:		1.32x speedup
   pnv		poppler-reseau:		1.31x speedup
   i5-2520m	firefox-planet-gnome:	1.31x speedup
   pnv		firefox-fishbowl:	1.30x speedup
   pnv		evolution:		1.28x speedup
   image	gvim:			1.27x speedup
   i5-2520m	swfdec-youtube:		1.25x speedup
   pnv		gnome-terminal-vim:	1.27x speedup
   pnv		gvim:			1.25x speedup
   image	firefox-planet-gnome:	1.25x speedup
   image	swfdec-youtube:		1.25x speedup
   ...

And a plethora of minor improvements everywhere!
-Chris

Snapshot 1.11.2 (2011-01-23)
===========================

In this first snapshot along the way to cairo-1.12.0, we are very excited
to announce the introduction of Bezier surface gradients, known as type
6/7 gradients in PS/PDF parlance. This is the culmination of much work by
the dynamic duo: Adrian Johnson and Andrea Canciani. Thanks guys!

Also, I want to warmly welcome Uli Schlachter who recently joined the
Cairo community on a mission. That mission is to make cairo-xcb a
supported backend for 1.12. And for this snapshot he has made great
strides in fixing all the bugs I had left behind. Thanks Uli!

And we have also seen a new contributor, Alexandros Frantzis, who has
begun bringing up cairo-gl for GLESv2 devices. Thanks Alex!

And lastly, I must also thank Adrian and Andrea for the vast numbers of
bugs that they have tackled between them, fixing all those little corner
cases that lie hidden until too late.

API additions:

The ability to construct piece-wise Bezier surface gradients:

	cairo_pattern_create_mesh

	constructs a pattern of type CAIRO_PATTERN_TYPE_MESH using

	cairo_pattern_mesh_begin_patch
	cairo_pattern_mesh_end_patch
	cairo_pattern_mesh_curve_to
	cairo_pattern_mesh_line_to
	cairo_pattern_mesh_move_to
	cairo_pattern_mesh_set_control_point
	cairo_pattern_mesh_set_corner_color_rgb
	cairo_pattern_mesh_set_corner_color_rgba
	cairo_pattern_mesh_get_patch_count
	cairo_pattern_mesh_get_path
	cairo_pattern_mesh_get_corner_color_rgba
	cairo_pattern_mesh_get_control_point

The introduction of a unique ID accessible via the mime data type:
	CAIRO_MIME_TYPE_UNIQUE_ID





Release 1.10.2 (2010-12-25 Chris Wilson <<EMAIL>>)
===================================================================
The cairo community is pleased to announce the 1.10.2 release of the
cairo graphics library. This is the first update to cairo's stable 1.10
series and contains a large number of bug fixes.

While many people have contributed and have help to test the release,
2 people deserve special recognition for their efforts in tracking down
and fixing bugs, Andrea Canciani and Adrian Johnson. Thanks to their
tremendous efforts, and of all cairo contributors, it is much
appreciated.

We recommend everyone upgrade to cairo 1.10.2 and hope that everyone
will continue to have lots of fun with cairo!

-Chris

Bug fixes
---------

  Fix embedding of grayscale jpegs in PS.
  https://bugs.freedesktop.org/show_bug.cgi?id=31632

  Fix the reported path of extents containing a curve.

  Fix the compositing of unaligned boxes.

  Reset the clipper in PDF upon finish.

  Fix degenerates arcs to become a degenerate line.

  Build support for autoconf 2.67

  Fix painting of transformed patterns in PS

  Fix the EPS bounding box for PS
  https://bugs.freedesktop.org/show_bug.cgi?id=24688

  Fix the missing content for EPS
  https://bugs.freedesktop.org/show_bug.cgi?id=24688

  Fix regression upon changing page size in PS/PDF
  https://bugs.freedesktop.org/show_bug.cgi?id=24691

  Only use ActualText with PDF-1.5 documents

  Fix the bbox for type1 fallbacks.

  Reset the color after ending the context in PDF
  https://bugs.freedesktop.org/show_bug.cgi?id=31140

  Fix the advance of subsetted type1 fonts
  https://bugs.freedesktop.org/show_bug.cgi?id=31062

  Fix handling of EXTEND_NONE gradients for PDF

  Restrict in-place optimisation for a8 image masks with SOURCE


Release 1.10.0 (2010-09-06 Chris Wilson <<EMAIL>>)
===================================================================
The cairo community is astounded (and flabbergast) to finally announce
the 1.10.0 release of the cairo graphics library. This is a major update
to cairo, with new features and enhanced functionality which maintains
compatibility for applications written using any previous major cairo
release, (1.8, 1.6, 1.4, 1.2, or 1.0). We recommend that anybody using
a previous version of cairo upgrade to cairo 1.10.0.

One of the more interesting departures for cairo for this release is the
inclusion of a tracing utility, cairo-trace. cairo-trace generates a
human-readable, replayable, compact representation of the sequences of
drawing commands made by an application. This can be used to inspecting
applications to understand issues and as a means for profiling
real-world usage of cairo.

The traces generated by cairo-trace have been collected in

  git://git.cairographics.org/git/cairo-traces

and have driven the performance tuning of cairo over the last couple of
years. In particular, the image backend is much faster with a new
polygon rasterisation and a complete overhaul of the tessellator. Not
only is this faster, but also eliminates visual artifacts from
self-intersecting strokes. Not only has cairo-trace been driving
performance improvements within cairo, but as a repeatable means of
driving complex graphics it has been used to tune OpenGL, DDX, and
pixman.

Cairo's API has been extended to better support printing, notably
through the ability to include a single compressed representation of an
image for patterns used throughout a document, leading to dramatic file
size reductions. Also the meta-surface used to record the vector
commands compromising a drawing sequence is now exposed as a
CAIRO_SURFACE_TYPE_RECORDING, along with a new surface that is a child of a
larger surface, CAIRO_SURFACE_TYPE_SUBSURFACE. One typical usage of a
subsurface would be as a source glyph in a texture atlas, or as a
restricted subwindow within a canvas.

Cairo's API has also resurrected the RGB16 format from the past as
the prevalence of 16-bit framebuffers has not diminished and is a
fore-taste of the extended format support we anticipate in the future.
Increasing cairo's utility, we introduce the cairo_region_t for handling
sets of pixel aligned rectangles commonly used in graphics applications.
This is a merger of the GdkRegion and the pixman_region_t, hopefully
providing the utility of the former with the speed of the latter.

Furthermore cairo has been reworked to interoperate more closely with
various acceleration architectures, gaining the ability to share
those hardware resources through the new cairo_device_t. For instance,
with the new OpenGL backend that supersedes the Glitz backend, hardware
and rendering operations can be shared between a classic OpenGL
application mixing libVA for the hardware assisted video decode with
cairo for high quality overlays all within the same OpenGL canvas.

Many thanks for the hard work of Adrian Johnson, Andrea Canciani, Behdad
Esfahbod, Benjamin Otte, Carl Worth, Carlos Garcia Campos, Chris Wilson,
Eric Anholt, Jeff Muizelaar, Karl Tomlinson, M Joonas Pihlaja, Søren
Sandmann Pedersen and many others that have contributed over the last
couple of years to cairo. Thank you all!

Snapshot 1.9.14 (2010-07-26)
============================

  A quiet couple of weeks, hopefully Cairo is seeing widescale deployment and
  we are being to see the results of the stabilisation effort. Clipping bugs
  seems to have been the order of the last couple of weeks, with a couple
  reported and duly fixed. Thank you Igor Nikitin and Karl Tomlinsion for
  finding those regressions. At this point all that seems to remain to do is
  to fix the outstanding regressions in the PDF backend...

Bugs fixes
----------

    Clip doesn't work for text on the image backend
    https://bugs.freedesktop.org/show_bug.cgi?id=29008

    Add explicit dependency for cxx
    https://bugs.freedesktop.org/show_bug.cgi?id=29114

    Fix regressions in reporting clip extents
    https://bugs.freedesktop.org/show_bug.cgi?id=29120
    https://bugs.freedesktop.org/show_bug.cgi?id=29121
    https://bugs.freedesktop.org/show_bug.cgi?id=29122
    https://bugs.freedesktop.org/show_bug.cgi?id=29124
    https://bugs.freedesktop.org/show_bug.cgi?id=29125


Snapshot 1.9.12 (2010-07-12)
============================

  A couple of weeks spent fixing those annoying bugs and cleaning up the build
  system; the list of outstanding tasks to complete for the stable release is
  finally shrinking. The chief bug fixer has been Benjamin Otte who not only
  made sure that the public API is consistent and being tested for its
  consistency, but also ensured that the documentation was up-to-date and
  spent time clarifying cases where even the Cairo developers have come
  unstuck in the past. Many thanks, Benjamin. However, he was not alone,
  as Andrea Canciani continued his fine work in isolating broken corner cases
  and proceeding to fix them, and tidying up the quartz backend. And last, but
  definitely not least, M Joonas Pihlaja tried building Cairo across a
  perverse range of systems and fixed up all the loose bits of code that came
  unravelled. Thanks everybody!

API Changes
-----------

  cairo_surface_set_mime_data, cairo_surface_get_mime_data:

    The length parameter is now an unsigned long (as opposed to an unsigned
    int). The parameter is intended to be an equivalent to a size_t without
    requiring POSIX types and be large enough to store the size of the
    largest possible allocation.

  cairo_gl_surface_create_for_texture:

    This a new surface constructor for cairo-gl that explicitly enables
    render-to-texture for foreign, i.e. application, textures.

  cairo_region_xor, cairo_region_xor_rectangle

    A couple of utility routines add to the region handling interface for
    the purpose of replacing existing GdkRegion functionality.

Bugs fixes
----------

  https://bugs.launchpad.net/ubuntu/+source/cairo/+bug/600622

    Inkscape was caught in the act of attempting to modify a finished surface.
    Unfortunately, we had the ordering of our guards and assertions wrong and
    so an ordinary application error was triggering an assert in Cairo. This
    lead Benjamin to add a test case to ensure that the entire public API
    could handle erroneous input and then proceeded to fix a whole slew of
    uncovered bugs.


  https://bugs.freedesktop.org/show_bug.cgi?id=28888

    A regression introduced by the special casing of uploading images to an
    xlib surface in-place which was ignoring the translation applied to the
    image.


Snapshot 1.9.10 (2010-06-26)
============================

   The first "quick" snapshot in the run up to the stable release.  The
   last snapshot was picked up by the bleeding edge distributions and so the
   bug reports have to started to roll in.  The most frequent of these are the
   introduction of rendering errors by applications that modify a surface
   without subsequently calling cairo_surface_mark_dirty(). Make sure the
   application developers are aware of increased reliance on strict use of the
   Cairo API before 1.10 is released!

   The usual slew of bugs reported and we would like to thank Zoxc for
   contributing the WGL interface for cairo-gl, and finding more build
   failures on win32.  And it just wouldn't be a 1.9 snapshot unless
   Benjamin Otte improved the error handling within cairo-gl, as well as
   isolating and fixing some more errors in the test suite. The biggest bug of
   the snapshot turned out to be a major sign extension issue that had lain
   hidden for many years and was suddenly exposed by incorrectly rounding
   rectangles when performing non-antialiased rendering.  Also to the relief
   of many we have included the downstream patch to honour the user's LCD
   filtering preferences for subpixel rendering of fonts.  The interface
   remains private for the time being, whilst the proposed public API is
   finalized.

API changes
-----------
   None.

Snapshot 1.9.8 (2010-06-12)
===========================

   One major API changes since the last snapshot, and a whole slew of bugs
   fixed and inconsistencies eliminated. Far too many bugs fixed to
   individually identify. We need to thank Benjamin Otte for his fantastic
   work on the cairo-gl backend making it faster and more robust, Andrea
   Canciani for finding so many bugs and developing test cases for them, as
   well fixing them. And last but not least we must all thank Adrian Johnson for
   continuing to eliminate bugs and improving the PostScript and PDF backends.

   This snapshot represents almost 4 months of bug fixing, bringing Cairo to
   a point where we consider it almost ready to be a candidate for release.
   There are a few known bugs left to be fixed, being tracked in
   https://bugs.freedesktop.org/show_bug.cgi?id=24384, so please give Cairo a
   whirl and report any regressions. The plan is to release a new snapshot
   every other week leading to a 1.10 release with a target date of
   2010-08-16.

API additions
-------------
  CAIRO_FORMAT_RGB16_565

    16 bit devices still remain popular, and so with great demand,
    CAIRO_FORMAT_RGB16_565 has been restored enabling applications to create
    and use 16 bit images as sources and render targets.

  cairo_surface_create_for_rectangle()

    It is common practice to cut an image up into many smaller pieces and use
    each of those as a source - a technique called texture atlasing.
    cairo_surface_create_for_rectangle() extends Cairo to directly support use
    of these subregions of another cairo_surface_t both as a source and as a
    render target.

  cairo_region_create()
  cairo_region_create_rectangle()
  cairo_region_create_rectangles()
  cairo_region_copy()
  cairo_region_reference()
  cairo_region_destroy()
  cairo_region_equal()
  cairo_region_status()
  cairo_region_get_extents()
  cairo_region_num_rectangles()
  cairo_region_get_rectangle()
  cairo_region_is_empty()
  cairo_region_contains_rectangle()
  cairo_region_contains_point()
  cairo_region_translate()
  cairo_region_subtract()
  cairo_region_subtract_rectangle()
  cairo_region_intersect()
  cairo_region_intersect_rectangle()
  cairo_region_union()
  cairo_region_union_rectangle()

    The Cairo region API was actually added a couple of snapshots ago, but we
    forgot to mention it at the time. A simple API for the handling of
    rectangular pixel-aligned regions by Soeren Sandmann.


Backend-specific improvements
-----------------------------
cairo-gl

  Benjamin Otte made more than 200 commits in which he refactored the cairo-gl
  backend, reducing a lot of code duplication and enabled him to begin working
  on improving performance by reducing state changes and associated overhead.

cairo-xlib

  Access to the underlying connection to the Display is now thread-safe
  enabling cairo-xlib to be used in a multi-threaded application without fear
  of random corruption. Thanks Benjamin Otte!

  cairo-xlib will now attempt to use PolyModeImprecise when compositing
  trapezoids (i.e. a fill or a stroke operation with a non-trivial path) which
  should allow hardware drivers more scope for accelerating the operation at
  the cost of potentially incurring minute rendering errors. The mode can be
  forced back to PolyModePrecise by setting the antialias parameter to
  CAIRO_ANTIALIAS_SUBPIXEL.

cairo-svg

  A notable improvement was contributed by Alexander Shulgin to enable SVG to
  reference external image through the use an extended MIME data type.

Snapshot 1.9.6 (2010-02-19)
===========================
API additions
-------------
    Add cairo_device_t

    The device is a generic method for accessing the underlying interface
    with the native graphics subsystem, typically the X connection or
    perhaps the GL context. By exposing a cairo_device_t on a surface and
    its various methods we enable finer control over interoperability with
    external interactions of the device by applications. The use case in
    mind is, for example, a multi-threaded gstreamer which needs to serialise
    its own direct access to the device along with Cairo's across many
    threads.

    Secondly, the cairo_device_t is a unifying API for the mismash of
    backend specific methods for controlling creation of surfaces with
    explicit devices and a convenient hook for debugging and introspection.

    The principal components of the API are the memory management of:

      cairo_device_reference(),
      cairo_device_finish() and
      cairo_device_destroy();

    along with a pair of routines for serialising interaction:

      cairo_device_acquire() and
      cairo_device_release()

    and a method to flush any outstanding accesses:

      cairo_device_flush().

    The device for a particular surface may be retrieved using:

      cairo_surface_get_device().

    The device returned is owned by the surface.

API changes (to API new in the cairo 1.9.x series)
--------------------------------------------------
  cairo_recording_surface_create()
  cairo_recording_surface_ink_extents()

    These are the replacement names for the functions previously named
    cairo_meta_surface_create and cairo_meta_surface_ink_extents.

  cairo_surface_set_mime_data

    This interface is now changed such that the MIME data will be
    detached if the surface is modified at all. This guarantees that
    the MIME data will not become out of synch due to surface
    modifications, and also means that for the MIME data to be useful,
    it must be set after all modifications to the surface are
    complete.

API removal (of experiment API)
-------------------------------
  The cairo-glitz backend is removed entirely, (in favor of the new
  cairo-gl backend). See below for more on cairo-gl.

Generic fixes
-------------

  Many improvements for drawing of dashed strokes

	Fix incorrect handling of negative offset
	Faster computation of first dash (avoids near-infinite looping)
	Approximate extremely fine dash patterns with appropriate alpha value

  Optimize spans-based renderers for repeated rows, (such as in a rounded rectangle)

Backend-specific improvements
-----------------------------
cairo-drm

  This is a new, direct-rendering backend that supports Intel graphics
  chipsets in the i915 and i965 families. It's still experimental and
  will likely remain that way for a while. It's already got extremely
  good performance on the hardware it supports, so if nothing else
  provides a working proof and performance target for the cairo-gl
  work for Intel graphics.

cairo-gl

  Start using GLSL to accelerate many operations. Many thanks to Eric
  Anholt and T. Zachary Laine for this work. For the first time, we
  have what looks like what will be a very compelling OpenGL-based
  backend for cairo (in terms of both quality and performance).

  See this writeup from Eric for more details on recent progress of
  cairo-gl (which he presented at FOSDEM 2010):

	http://anholt.livejournal.com/42146.html

cairo-image

  The image backend is made dramatically faster (3-5 times faster for
  benchmarks consisting primarily of glyph rendering).

cairo-quartz fixes:

  Many fixes from Robert O'Callahan and Andrea Canciani including:

	Fixed gradient pattern painting
	Improved A8 image handling
	Fixes for "unbounded" and other compositing operators

cairo-pdf fixes:

  Improvements to embedding of JPEG and JPEG2000 data.

cairo-ps fixes:

  Fix printing of rotated user fonts.

Snapshot 1.9.4 (2009-10-15)
===========================
API additions:

  cairo_meta_surface_create()
  cairo_meta_surface_ink_extents()

    Finally exporting the internal meta-surface so that applications
    have a method to record and replay a sequence of drawing commands.

  cairo_in_clip()

    Determines whether a given point is inside the current clip.
    ??? Should this be called cairo_in_paint() instead? in-clip is the test
    that is performed, but in-paint would be similar to in-fill and in-stroke.

New utilities:

  cairo-test-trace

    A companion to cairo-perf-trace, this utility replays a trace against
    multiple targets in parallel and looks for differences in the output,
    and then records any drawing commands that cause a failure.
    Future plans:
      Further minimisation of the fail trace using "delta debugging".
      More control over test/reference targets.

Backend improvements:

  xlib

     Server-side gradients. The theory is that we can offload computation
     of gradients to the GPU and avoid pushing large images over the
     connection. Even if the driver has to fallback and use pixman to render
     a temporary source, it should be able to do so in a more efficient manner
     than Cairo itself. However, cairo-perf suggests otherwise:

     On tiny, Celeron/i915:

      before: firefox-20090601 211.585
       after: firefox-20090601 270.939

     and on tiger, CoreDuo/nvidia:

      before: firefox-20090601 70.143
       after: firefox-20090601 87.326

     In particular, looking at tiny:

     xlib-rgba paint-with-alpha_linear-rgba_over-512   47.11 (47.16 0.05%) -> 123.42 (123.72 0.13%):  2.62x slowdown
     █▋
     xlib-rgba paint-with-alpha_linear3-rgba_over-512   47.27 (47.32 0.04%) -> 123.78 (124.04 0.13%):  2.62x slowdown
     █▋


New experimental backends:

   QT

   OpenVG - The initial work was done by Øyvind Kolås, and made ready for
	    inclusion by Pierre Tardy.

   OpenGL - An advanced OpenGL compositor. The aim is to write a integrate
	    directed rendering using OpenGL at a high-level into Cairo. In
	    contrast to the previous attempt using Glitz which tried to
	    implement the RENDER protocol on top of OpenGL, using the
	    high-level interface should permit greater flexibility and
	    more offloading onto the GPU.
	    The initial work on the backend was performed by Eric Anholt.

Long standing bugs fixed:

  Self-intersecting strokes.

    A long standing bug where the coverage from overlapping semi-opaque
    strokes (including neighbouring edges) was simply summed in lieu of
    a costly global calculation has been fixed (by performing the costly
    global calculation!) In order to mitigate the extra cost, the
    tessellator has been overhauled and tune, which handles the fallback
    for when we are unable to use the new span rasteriser on the stroke
    (e.g. when using the current RENDER protocol). The large number of
    pixel artifacts that implementing self-intersection elimination
    removes is ample justification for the potential performance
    regression. If you unfortunately do suffer a substantial performance
    regression in your application, please consider obtaining a
    cairo-trace and submitting it to us for analysis and inclusion into
    our performance suite.

Special thanks:

   To the AuroraUX team for providing access to one of their OpenSolaris
   machines for cairo and pixman development.  http://www.auroraux.org/

Snapshot 1.9.2 (2009-06-12)
===========================
API additions:

  cairo_surface_set_mime_data()
  cairo_surface_get_mime_data()

    Should this take unsigned int, unsigned long or size_t for the length
    parameter? (Some datasets may be >4GiB in size.)

    Associate an alternate, compressed, representation for a surface.
    Currently:
     "image/jp2" (JPEG2000) is understood by PDF >= 1.5
     "image/jpeg" is understood by PDF,PS,SVG,win32-printing.
     "image/png" is understood by SVG.

  cairo_pdf_version_t
  cairo_pdf_surface_restrict_to_version()
  cairo_pdf_get_versions()
  cairo_pdf_version_to_string()

    Similar to restrict to version and level found in SVG and PS,
    these limit the features used in the output to comply with the PDF
    specification for that version.

  CAIRO_STATUS_INVALID_SIZE
    Indicates that the request surface size is not supported by the
    backend.  This generally indicates that the request is too large.

  CAIRO_STATUS_USER_FONT_NOT_IMPLEMENTED
    Indicates that a required callback for a user-font was not implemented.

  CAIRO_STATUS_LAST_STATUS
    This is a special value to indicate the number of status values enumerated
    at compile time. (This may differ to the number known at run-time.)

  The built-in twin font is now called "@cairo:" and supports a limited set
  of options like "@cairo:mono". Where are these specified?

  cairo_in_fill() now uses HTML Canvas semantics, all edges are inside.

New experimental backends:

   CairoScript

New utility:

  cairo-trace and cairo-perf-trace

    cairo-trace generates a human-readable, replayable, compact(-ish!)
    representation of the sequences of drawing commands made by an
    application.

    Under the util/cairo-script directory is a library to replay traces.

    perf/cairo-perf-trace replays traces against multiple backends
    and makes useful benchmark reports. This is integrated with
    'make perf'. You may collect your own traces or take advantage
    of traces collected by the community:

      git://git.cairographics.org/git/cairo-traces

    (Put this into perf/cairo-traces to run these as part of "make perf".)

    There is additional WIP in building a debugging tool for cairo applications
    based on CairoScript (currently very preliminary, mostly serves to show
    that GtkSourceView is too slow) :

      people.freedesktop.org:~ickle/sphinx

Test suite overhaul:

  The test suite is undergoing an overhaul, primarily to improve its speed
  and utility. (Expect more changes in the near future to improve XFAIL
  handling.)

Optimisations:
  polygon rasterisation! Joonas implemented the Tor polygon scan converter,
  on typical geometry is about 30% faster for the image backend.

  Bovine Polaroids! For those not in on the joke, this is the long
  awaited "copy-on-write snapshot" or "COW snapshot" support. The
  user-visible feature is that including the same image multiple times
  into a PDF file should result in only a single instance of that
  image in the final output. This is unlike previous versions of cairo
  which would generate very large PDF files with multiple copies of
  the same image. Adrian says that the PDF is not quite working as
  well as it should yet, so we hope for further improvements before
  cairo 1.10.

Bug fixes:

  EXTEND_PAD.

  Better handling of large scale-factors on image patterns.

  Emit /Interpolate for PS,PDF images.

  Global glyph cache - cap on the total number of inactive glyphs,
  should prove fairer for fonts with larger glyph sets.

  Compilation without fontconfig

  Improved handling of low-bitdepth sources (e.g. copying the contents
  of 16-bit xserver windows)

Regressions:

  cairo_traps_extract_region >10x slower. Fix pending.

Still to come:

  Region tracking API (ssp) for damage tracking, hit testing etc
  mime-surface

  An expiremental OpenGL backend?

  Tweaks to tessellator, allocations of patterns, delayed
  initialisation of the xlib backend (reduce the cairo overhead of
  render_bench by ~80%).


Release 1.8.8 (2009-06-16 Chris Wilson <<EMAIL>>)
==================================================================
The cairo community is pleased to announce the 1.8.8 release of the
cairo graphics library. This is the fourth update to cairo's stable
1.8 series and contains a small number of bug fixes (in particular a
few corrections to the documentation and a few fixes in the FreeType font
backend). This is being released just over six months after cairo 1.8.6.

We recommend that everyone using cairo upgrade to 1.8.8.

-Chris

Build fixes
-----------
There were reports of incompatibilities with the autotools bundled in with
the 1.8.6 tarball.  This release has been built with automake-1.10.2 and
autoconf-2.63.

The configure check for FreeType has been improved:

   typo in check for version of freetype in configure script
   https://bugs.freedesktop.org/show_bug.cgi?id=19283

Compilation on 64-bit MacOS/X fixes:

  Cannot build cairo_quartz_font_face_create_for_atsu_font_id on 64-bit Mac OS X
  https://bugs.freedesktop.org/show_bug.cgi?id=15702

Bug fixes
---------
Uninitialised status return within _cairo_clip_intersect_mask(). This caused
random crashes and general mayhem as an error could be generated causing all
rendering to the context to stop.

Avoid transforming nearly-degenerate matrices into degenerate matrices:

   Painting stops in this case, using -moz-transform: scale, rotate and video
   https://bugzilla.mozilla.org/show_bug.cgi?id=467423

A few FreeType font handling bugs were fixed:

   Rendering with PANGO_GRAVITY_EAST leads to different results with image and pdf
   https://bugs.freedesktop.org/show_bug.cgi?id=21985

   Don't call FT_Done_Face() on faces we did not create

   zombie ft_font_face / ft_unscaled_font mutual referencing problems
   https://bugs.freedesktop.org/show_bug.cgi?id=21706

Ensure win32 font backend sets the return value to -1 (indicating the absent
glyph) if the font index lookup for the unicode character fails. And
similarly fix a bug where a fatal error was raised for an invalid glyph.

   cairo_scaled_font_glyph_extents breaks with invalid glyph id
   https://bugs.freedesktop.org/show_bug.cgi?id=20255

Various improvements to the documentation, reported by Truc Troung:

   https://bugs.freedesktop.org/show_bug.cgi?id=20095
   https://bugs.freedesktop.org/show_bug.cgi?id=20154
   https://bugs.freedesktop.org/show_bug.cgi?id=20180
   https://bugs.freedesktop.org/show_bug.cgi?id=20183
   https://bugs.freedesktop.org/show_bug.cgi?id=20182
   https://bugs.freedesktop.org/show_bug.cgi?id=20441


Release 1.8.6 (2008-12-13 Chris Wilson <<EMAIL>>)
==================================================================
The cairo community is pleased to announce the 1.8.6 release of the
cairo graphics library. This is the third update to cairo's stable
1.8 series and contains a small number of bug fixes (in particular a
few fixes for failures of cairo 1.8.4 on Quartz and PDF, and build fixes for
a couple of backends). This is being released just under a month after
cairo 1.8.4.

We recommend that everyone using cairo upgrade to 1.8.6.

-Chris

Build fixes
-----------
Fix build of DirectFB backend with debugging enabled:

   Bug in _cairo_directfb_surface_release_source_image function
   https://bugs.freedesktop.org/show_bug.cgi?id=18322

Fix build on OS/2.

Bug fixes
---------
Workaround a mis-compilation of cairo_matrix_invert() that generated invalid
matrices and triggered assertion failures later. The issue was reported by
Peter Hercek.

Invalid computation of the modulus:

   https://bugzilla.mozilla.org/show_bug.cgi?id=466258

Invalid referencing of patterns in the Quartz backend:

   Failed assertion `CAIRO_REFERENCE_COUNT_HAS_REFERENCE
   (&pattern->ref_count)' when using cairo quartz backend
   https://bugs.freedesktop.org/show_bug.cgi?id=18632

Invalid references to glyphs after early culling, causing segmentation faults
in the PDF backend:

   https://lists.cairographics.org/archives/cairo/2008-December/015976.html

Check for XRender in the XCB backend, or else we may attempt an invalid memory
access:

    XCB backend fails with missing render.
    https://bugs.freedesktop.org/show_bug.cgi?id=18588

Release 1.8.4 (2008-11-14 Carl Worth <<EMAIL>>)
=========================================================
The cairo community is pleased to announce the 1.8.4 release of the
cairo graphics library. This is the second update to cairo's stable
1.8 series and contains a small number of bug fixes, (in particular a
few fixes for build failures of cairo 1.8.2 on various systems). This
is being released just over two weeks after cairo 1.8.2.

We recommend that everyone using cairo upgrade to 1.8.4.

-Carl

Build fixes
-----------
Fix build with older XRender that doesn't define RepeatNone:

   Build of xlib backend fails against old XRender (RepeatNone undeclared)
   https://bugs.freedesktop.org/show_bug.cgi?id=18385

Fix build with bash version <= 3.0:

   doltlibtool broken on linux with bash 3.00.0
   https://bugs.freedesktop.org/show_bug.cgi?id=18363

Bug fixes
---------
Avoid triggering a bug in X.org server 6.9 resulting in a hung machine
requiring a reboot:

    https://bugs.freedesktop.org/show_bug.cgi?id=15628#c2

Fix display of user fonts as exercised by proposed support for type3
fonts in poppler (unsigned promotion fixes):

    Use cairo user-font for Type 3 fonts
    https://lists.freedesktop.org/archives/poppler/2008-October/004181.html

Avoid miscomputing size of fallback images required when rendering
with CLEAR, IN, or SOURCE operator to vector surfaces, (PS, PDF, SVG,
etc.).

Be more tolerant of broken fonts when subsetting type1 fonts:

   Error handling in cairo_type1_font_subset_get_glyph_names_and_widths
   https://lists.cairographics.org/archives/cairo/2008-October/015569.html

Fix cairo_fill_extents, cairo_stroke_extents, cairo_path_extents, to
correctly allow NULL parameters as documented.

Fix potential crash on emitting a type3 glyph after having drawn text
paths from the same font, (for example with cairo_text_path).

Release 1.8.2 (2008-10-29 Carl Worth <<EMAIL>>)
=========================================================
The cairo community is pleased to announce the 1.8.2 release of the
cairo graphics library. This is the first update to cairo's stable 1.8
series and contains a large number of bug fixes. It is being released
just over one month since cairo 1.8.0.

This release consists primarily of bug fixes, but there is one notable
new feature, (the ability to build cairo without an external font
backend), and there are a few optimizations as well. See below for
details on these changes and the most important bug fixes.

While many people have contributed to this release, Chris Wilson
deserves particular mention. He has contributed well over twice as
many changes to cairo since 1.8.0 than everyone else combined. We
greatly appreciate the tremendous efforts of Chris and all cairo
contributors.

We recommend everyone upgrade to cairo 1.8.2 and hope that everyone
will have lots of fun with cairo!

-Carl

New feature
-----------
It is now possible to build cairo without any font backend, (such as
freetype, win32 or quartz). This is most useful when the application
provides custom font rendering through the user-font API. But in the
case where no external font backend is available, and no user-font is
provided, cairo will render with a failsafe font, (a stroked font
covering visible ASCII character). (Behdad Esfahbod)

Optimizations
-------------
Dramatically speed up compilation with dolt (removes much of the
libtool overhead) (Behdad Esfahbod with thanks to Josh Triplett).

Several minor optimizations to tessellator (special-cased comparisons,
faster insert for skiplist, etc.) (Chris Wilson).

Optimize away fractional translation component when doing
EXTEND_NEAREST filtering, (for better performance).

General bug fixes
-----------------
Allow cloning sub-regions of similar surfaces to fix this bug
(Chris Wilson):

	Crafted gif file will crash firefox
	[XError: 'BadAlloc (insufficient resources for operation)']
	https://bugzilla.mozilla.org/show_bug.cgi?id=424333

Fix some matrix confusion to fix this regression (Chris Wilson):

	Translucent star exports in a wrong way to PDF
	https://bugs.launchpad.net/inkscape/+bug/234546

Fix some long-standing bugs with respect to properly computing the
extents of transformed, filtered surfaces (Owen Taylor, Carl Worth,
and Chris Wilson):

	Bad clipping with EXTEND_NONE
	https://bugs.freedesktop.org/show_bug.cgi?id=15349

	Improve filtering handling in cairo-pattern.c
	https://bugs.freedesktop.org/show_bug.cgi?id=15367

	Many thanks to Chris Wilson for digging out and cleaning up
	these fixes.

Fix compilation on Solaris 10 (Chris Wilson):

	Cairo requires -DREENTRANT (along with	-D_POSIX_THREAD_SEMANTICS)
	to compile on Solaris 10 with pthreads
	https://bugs.freedesktop.org/show_bug.cgi?id=18010

Fix very old bug causing dashes to be rendered at the wrong length in
fallback images (Adrian Johnson)

	Dashed strokes too long in fallback images
	https://bugs.freedesktop.org/show_bug.cgi?id=9189

Fix broken dashing when a dashed path starts outside the clip region
(Chris Wilson).

Avoid range overflow when computing large patterns (Benjamin Otte and
Chris Wilson).

Avoid crashing due to an invalid font with an incorrect entry in its
CMAP table (Adrian Johnson).

Fix bugs in computing maximum size of text requests that can be sent
with the Render extension, (avoiding potential crashes when rendering
large amounts of text) (Behdad Esfahbod and Chris Wilson).

Fix rendering of operators unbounded by the mask (Chris Wilson).

Fix compilation on systems without compiler support for a native
64-bit type (Chris Wilson).

Fix several cases of missing error-status propagation. (Chris Wilson,
doing the work he seems to never tire of).

Fix several locking issues found with the lockdep valgrind skin (Chris
Wilson).

Backend-specific bug fixes
--------------------------
xlib: Avoid crash due to attempting XRender calls on pixmaps with
formats not supported by the Render extension (Chris Wilson):

	XRender crashes due to NULL pointer from Cairo on SGI O2
	https://bugs.freedesktop.org/show_bug.cgi?id=11734

xlib: Add support for XImages with depth of 4, 20, 24, or 28 bits
(Chris Wilson):

	cairo doesn't support 24 bits per pixel mode on X11
	https://bugs.freedesktop.org/show_bug.cgi?id=9102

xlib: Avoid mistakenly considering two surfaces as similar just
because their depths match (while their Render formats do not) (Karl
Tomlinson).

ps: Fix slight mis-scaling of bitmapped fonts (Adrian Johnson)

svg: Correctly emit comp-op for paint, mask, and show_glyphs
operations (Emmanuel Pacaud).

svg: Use finer-grained fallbacks for SVG 1.2 (as PS and PDF backends
have been doing since 1.6.0) (Chris Wilson).

win32: Fallback to DIB if DDB create fails for
cairo_surface_create_similar (Vladimir Vukicevic).

win32: Fix compatibility with Windows Mobile (Vladimir Vukicevic).

win32: Fix static builds to not do __declspec(dllimport) on public
functions. This requires the user to set a CAIRO_WIN32_STATIC_BUILD
environment variable when compiling (Behdad Esfahbod).

Release 1.8.0 (2008-09-25 Carl Worth <<EMAIL>>)
=========================================================
The cairo community is happy (and relieved) to announce the 1.8.0
release of the cairo graphics library. This is a major update to
cairo, with new features and enhanced functionality which maintains
compatibility for applications written using any previous major cairo
release, (1.6, 1.4, 1.2, or 1.0). We recommend that anybody using a
previous version of cairo upgrade to cairo 1.8.0.

The dominant theme of this release is improvements to cairo's ability
to handle text. The highlights include a new "user fonts" feature as
well as a new cairo_show_text_glyphs API which allows glyphs to be
embedded in PDF output along with their original text, (for searching,
selection, and copy-and-paste). Another major feature is a revamp of
cairo's build system making it much easier to build cairo on various
platforms.

See below for more details.

User fonts
----------
This new API allows the user of cairo API to provide drawings for
glyphs in a font. A common use for this is implementing fonts in
non-standard formats, like SVG fonts and Flash fonts. This API can
also be used by applications to provide custom glyph shapes for fonts
while still getting access to cairo's glyph caches. See
test/user-font.c and test/user-font-proxy.c for usage examples. This
is based on early work by Kristian Høgsberg. Thanks Kristian!

This new API consists of the following functions (and corresponding
_get functions):

	cairo_user_font_face_create

	cairo_user_font_face_set_init_func
	cairo_user_font_face_set_render_glyph_func
	cairo_user_font_face_set_text_to_glyphs_func
	cairo_user_font_face_set_unicode_to_glyph_func

An additional, new API is

	cairo_scaled_font_text_to_glyphs

We were previously reluctant to provide this function as
text-to-glyphs support in cairo was limited to "toy" font
functionality, not really interesting for real-world text
processing. However, with user fonts landing, this API is needed to
expose full access to how user fonts convert text to glyphs. This is
expected to be used by text toolkits like Pango, as well as "proxy"
user-font implementations.

cairo_show_text_glyphs
----------------------
This new API allows the caller of cairo to provide text data
corresponding to glyphs being drawn. The PDF backend implements this
new API so that complex text can be copied out of cairo's PDF output
correctly and reliably, (assuming the user of cairo calls
cairo_show_text_glyphs). The cairo_show_text_glyphs API is definitely
the most daunting API to debut in cairo. It is anticipated that pango
(and similar high-level text libraries) will be the primary users of
this API. In fact, pango 1.22 already uses cairo_show_text_glyphs.
Behdad was the architect and implementor of this effort. Thanks,
Behdad!

The cairo_show_text_glyphs API includes the following new functions:

	cairo_show_text_glyphs

	cairo_glyph_allocate
	cairo_glyph_free

	cairo_text_cluster_allocate
	cairo_text_cluster_free

	cairo_surface_has_show_text_glyphs

Build system revamp
-------------------
The primary goal of the revamp is to make the build system less
fragile, (particularly for non-Linux platforms). For example, now
people building on win32 will no longer need to maintain a
platform-specific list of files to be built. See the new README.win32
for details. Also, the .so file will now be installed with a different
naming scheme, (for example, 1.7.6 will install with a .10800
suffix). Many thanks to Behdad and his small army of helpers!

Assorted API additions
----------------------
For API completeness, several missing "getter" functions were added:

	cairo_scaled_font_get_scale_matrix

	cairo_surface_get_fallback_resolution

	cairo_toy_font_face_create
	cairo_toy_font_face_get_family
	cairo_toy_font_face_get_slant
	cairo_toy_font_face_get_weight

The new cairo_toy_font_face functions provide access to functionality
and settings provided by cairo_select_font_face(). Thanks Behdad!

cairo-ps/cairo-pdf: More efficient output
-----------------------------------------
Adrian Johnson has been busy fixing all kinds of bugs in PS and PDF
backends, as well making them generate much more compact output by
avoiding things like re-emitting the color or linestyle on every
drawing operation. Thanks Adrian!

cairo-xlib: dithering
---------------------
Dithering: Cairo now does simple dithering when rendering to legacy X
servers. This is most visible with 8-bit visuals. Thanks Behdad!

cairo-xlib: Avoid rendering glyphs out of surface bounds
--------------------------------------------------------
This seemingly harmless optimization exposed a bug in OpenOffice.org 3
versions where OO.o was passing bogus surface extents to cairo,
resulting in no text rendered in OO.o. Please contact your
distribution's OO.o maintainers if you see this bug and point them to
the following URL:

 https://bugs.freedesktop.org/show_bug.cgi?id=16209

cairo-xlib: Improved performance with X server without Render
-------------------------------------------------------------
Cairo now performs better on remote X servers that lack the Render
extension by being smarter about using X core protocol facilities
instead of falling back to doing all rendering on the client side.

cairo-ft: respecting FC_FT_FACE
-------------------------------
Previously it was impossible to instruct cairo to do emboldening on a
font face object created from an FT_Face. Cairo now respects and uses
the FC_FT_FACE fontconfig pattern element, so emboldening can be
achieved by using cairo_ft_font_face_create_for_pattern() and a
carefully crafted pattern using FC_FT_FACE and FC_EMBOLDEN. Thanks
Behdad!

cairo-directfb: backend improvements
------------------------------------
The directfb backend, though still unsupported, has seen a good deal
of improvements. Thanks Vlad!

Bug fixing and optimizations
----------------------------
xlib: Faster bookkeeping (Karl Tomlinson)
	https://bugzilla.mozilla.org/show_bug.cgi?id=453199#c5

PS: Fix gradients with non-constant alpha (Chris Wilson)

Fix deadlock in user-font code (Richard Hughes and Behdad Esfahbod)
	https://bugs.freedesktop.org/show_bug.cgi?id=16819

Countless other bugs have been fixed and optimizations made, many of
them thanks to Chris Wilson. Thanks Chris and others!

Note also that the code that had been in cairo 1.7.x calling into
freetype's optional lcd_filter function was removed from cairo before
the 1.8.0 release. We do expect this code to come back in some form in
the future.

Snapshot 1.7.6 (2008-09-17 Carl Worth <<EMAIL>>)
==========================================================
The cairo community is happy to announce the 1.7.6 snapshot of the
cairo graphics library. This is a "release candidate" for the upcoming
1.8.0 release, so we will greatly appreciate any reports of problems
in this release, and no major changes are currently planned before
1.8.

Notable changes in 1.7.6
------------------------
The largest number of changes since 1.7.4 did not change the
implementation of cairo itself, but instead revamped cairo's build
system. The primary goal of the revamp is to make the build system
less fragile, (particularly for non-Linux platforms). For example, now
people building on win32 will no longer need to maintain a
platform-specific list of files to be built. Also, the .so file will
now be installed with a different naming scheme, (for example, 1.7.6
will install with a .10706 suffix). Much thanks, Behdad!

And, as usual, Chris Wilson has made another large round of robustness
improvements, (eliminating dead code, fixing propagation of error
status values, test suite improvements, etc. etc.). Thanks as always,
Chris!

API changes since 1.7.4
-----------------------
There have been a few changes of API that was new during the 1.7
series:

* Remove cairo_font_options_set_lcd_filter
   and cairo_font_options_get_lcd_filter

  Motivation: At the Cairo Summit, this API was determined to be too
	specific to the freetype font backend to be in the general
	API. A similar API with a cairo_ft prefix might be introduced
	in the future. Note that cairo will still respect the
	corresponding fontconfig settings for these options.

* Replace cairo_has_show_glyphs
     with cairo_surface_has_show_glyphs

  Motivation: This really is a surface-specific interface, and the
	convenience function on the cairo_t is not obviously
	necessary. An application can easily call:

	cairo_surface_has_show_glyphs (cairo_get_target (cr));

	as needed.

* Add cairo_text_cluster_flags_t
   to cairo_show_text_glyphs
      cairo_scaled_font_text_to_glyphs
      cairo_user_scaled_font_text_to_glyphs_func_t

  Motivation: This flag, (and specifically the
	CAIRO_TEXT_CLUSTER_FLAG_BACKWARD value), replaces the
	cairo_bool_t backward argument in each of the above
	interfaces. This leads to more readable user code, and also
	allows future extensibility.

As always, there are no changes to any API from any major cairo
release, (1.0.x, 1.2.x, 1.4.x, 1.6.x). Cairo maintains the same
compatibility promise it always has.

Bug fixes since 1.7.4
---------------------
xlib: Faster bookkeeping (Karl Tomlinson)
	https://bugzilla.mozilla.org/show_bug.cgi?id=453199#c5

PS: Fix gradients with non-constant alpha (Chris Wilson)

Fix deadlock in user-font code (Richard Hughes and Behdad Esfahbod)
	https://bugs.freedesktop.org/show_bug.cgi?id=16819

Several other minor fixes.

Snapshot 1.7.4 (2008-08-11 Behdad Esfahbod <<EMAIL>>)
===============================================================
The cairo community is embarrassed to announce availability of the 1.7.4
snapshot of the cairo graphics library.  This is a followup release to the
1.7.2 snapshot to ship a tarball that can actually be built.  The only
change since 1.7.4 is including the missing header file
cairo-user-font-private.h in the distribution.

Snapshot 1.7.2 (2008-08-11 Behdad Esfahbod <<EMAIL>>)
===============================================================
The cairo community is finally ready to announce availability of the 1.7.2
snapshot of the cairo graphics library.  This is embarrassingly the first
snapshot in the 1.7 unstable series of cairo, leading to the eventual release
of cairo 1.8, currently planned for late September.

This snapshot comes four months after the 1.6.4 release.  We have done a
really bad job on getting development snapshots out this cycle, but
hopefully all the API changes for 1.8 are now finished and the remaining
weeks will be spent on bug-fixing.  There is more than 400 commits worth
of changes in this snapshot, and those can use some testing.  Read on!

Text, text, and more text!
--------------------------
The dominant theme of this release, and 1.8 in general, is improvements
around cairo text API.  Here is a high-level list of changes with text
handling:

User fonts
----------
This is new API allowing the user of cairo API to provide drawings for glyphs
in a font.  This is most useful in implementing fonts in non-standard formats,
like SVG fonts and Flash fonts, but can also be used by games and other
applications to draw "funky" fonts.  See test/user-font.c and
test/user-font-proxy.c for usage examples.  This is based on early work by
Kristian Høgsberg.  Thanks Kristian!

show_text_glyphs
----------------
This new API allows the caller of cairo to mark text glyphs with their
original text.  The PDF backend implements this new API and latest Pango
master uses it.  The result is (when bugs are fixed) that complex text can be
copied out of pangocairo's PDF output correctly and reliably.  There are bugs
to fix though.  A few poppler bugs, and some more in cairo and pango.

To test show_text_glyph, just grab pango master and this cairo snapshot and
print text in gedit.  Open in acroread or evince, select all, copy, paste
in gedit and compare.  The Arabic text with diacritic marks is particularly
showing bad.  Try with pango/pango-view/HELLO.txt if you are brave
enough.  The Indic text is showing improvements, but is still coming out
buggy.

LCD subpixel filtering using FreeType
-------------------------------------
FreeType 2.3.5 added support for various LCD subpixel filtering, and
fontconfig 2.6.0 added support for configuring LCD filter on a font by font
basis.  Cairo now relies on FreeType and fontconfig for subpixel filtering.
This work is based on David Turner's original patch to cairo, maintained
and tested by Sylvain Pasche and others.  Thanks all!

Toy font face constructor and getter
------------------------------------
Mostly for API completion, but also useful for higher level (like Pango) to
hook into what the user has set using cairo_select_font_face(), making that
toy API a bit more useful.

FreeType: respecting FC_FT_FACE
-------------------------------
Previously it was impossible to instruct cairo to do emboldening on a font
face object created from an FT_Face.  Cairo now respects and uses the
FC_FT_FACE fontconfig pattern element, so emboldening can be achieved by
using cairo_ft_font_face_create_for_pattern() and a carefully crafted pattern
using FC_FT_FACE and FC_EMBOLDEN.


PS/PDF: More efficient output
-----------------------------
Adrian Johnson has been busy fixing all kinds of bugs in PS and PDF
backends, as well making them generate much more compact output by avoiding
things like re-emitting the color or linestyle on every drawing operation.
Thanks Adrian!


Xlib: Dithering
---------------
Cairo now does simple dithering when rendering to legacy X servers.  This is
mostly visible with 8-bit visuals.

Xlib: Avoid rendering glyphs out of surface bounds
--------------------------------------------------
This seemingly harmless change manifested a bug with OpenOffice.org 3 versions
where OO.o was passing bogus surface extents to cairo, resulting in no text
rendered in OO.o.  Please contact your distro's OO.o maintainers if you see
this bug and point them to the following URL:

  https://bugs.freedesktop.org/show_bug.cgi?id=16209

Xlib: Improved performance with Xrender-less X servers
------------------------------------------------------
Cairo now performs better on remote, Xrender-less X servers by being smarter
about using X core protocol facilities instead of falling back to doing all
rendering on the client side.


Directfb: backend improvements
------------------------------
The directfb backend, though still unsupported, has seen a good deal of
improvements.  Thanks Vlad!


Bug fixing and optimizations
----------------------------
Countless bugs have been fixed and optimizations made, many of them thanks to
Chris Wilson.  Thanks Chris!


API additions
-------------

cairo_show_text_glyphs

  This is a new text rendering API.  Being a more advanced version of
  cairo_show_glyphs(), it is aimed for use by higher-level text toolkits like
  Pango, and enables better text extraction from output generated by backends
  like PDF and SVG.  The PDF backend already implements it, and the upcoming
  Pango release will use it.

  To make that API work, a bunch of other additions were made:

cairo_glyph_allocate
cairo_glyph_free
cairo_text_cluster_t
cairo_text_cluster_allocate
cairo_text_cluster_free
cairo_surface_has_show_text_glyphs


cairo_user_font_face_create

  This is the "user" font face constructor, accompanied by a variety of method
  signatures, getters, and setters for a callback-based font backend:

CAIRO_FONT_TYPE_USER
cairo_user_scaled_font_init_func_t
cairo_user_scaled_font_render_glyph_func_t
cairo_user_scaled_font_text_to_glyphs_func_t
cairo_user_scaled_font_unicode_to_glyph_func_t
cairo_user_font_face_set_init_func
cairo_user_font_face_set_render_glyph_func
cairo_user_font_face_set_text_to_glyphs_func
cairo_user_font_face_set_unicode_to_glyph_func
cairo_user_font_face_get_init_func
cairo_user_font_face_get_render_glyph_func
cairo_user_font_face_get_text_to_glyphs_func
cairo_user_font_face_get_unicode_to_glyph_func


cairo_scaled_font_text_to_glyphs

  We were previously reluctant to provide this function as text-to-glyphs
  support in cairo was limited to "toy" font functionality, not really
  interesting for real-world text processing.  However, with user-fonts
  landing, this API is needed to expose full access to how user-fonts
  convert text to glyphs.  This is expected to be used by text toolkits like
  Pango, as well as "proxy" user-font implementations.


cairo_lcd_filter_t
cairo_font_options_set_lcd_filter
cairo_font_options_get_lcd_filter

  These add the possibility to choose between various available LCD subpixel
  filters.  The available filter values are modelled after what FreeType
  provides.


cairo_toy_font_face_create
cairo_toy_font_face_get_family
cairo_toy_font_face_get_slant
cairo_toy_font_face_get_weight

  These provide access to functionality and settings provided by
  cairo_select_font_face().


cairo_scaled_font_get_scale_matrix
cairo_surface_get_fallback_resolution

  For API completeness.


Various new values for cairo_status_t enum


Known issues:

- Type3 fonts generated by cairo's PDF backend may show up in poppler/Evince
  in a different color than expected.  This is fixed in poppler master branch.
  This mostly affects cairo user fonts.  The test case test/user-font.c
  demonstrates this.

- User fonts using other fonts in their rendering are currently embedded in
  PDF as fallback bitmap glyphs.  This will be (hopefully) fixed before 1.8.
  The test case test/user-font-proxy.c demonstrates this.


Release 1.6.4 (2008-04-11 Carl Worth <<EMAIL>>)
=========================================================
The cairo community is wildly embarrassed to announce the 1.6.4
release of the cairo graphics library. This release reverts the xlib
locking change introduced in 1.6.4, (and the application crashes that
it caused).  The community would be glad to sack its current release
manager and is accepting applications for someone who could do the job
with more discipline.

Revert 'add missing locking in cairo-xlib'
------------------------------------------
This change was introduced in cairo 1.6.2, but also introduced a bug
which causes many cairo-xlib applications to crash, (with a
segmentation fault inside of XSetClipMask). Instead of attempting
another fix for the broken fix, the change in 1.6.2 has been
reverted. The original bug which the change was addressing has been
present since at least cairo 1.4, so it is not expected that leaving
this bug unfixed will cause any new problems for applications moving
from cairo 1.4 to cairo 1.6.

At this point, the code of cairo 1.6.4 differs from cairo 1.6.0 only
in the fix for the PostScript-printer crashes.

Tweak build to avoid linking with g++
-------------------------------------
Cairo 1.6.4 avoids a quirk in automake that was causing the cairo
library to be linked with g++ and linked against libstdc++ even when
only C source files were compiled for the library.

Release 1.6.2 (2008-04-11 Carl Worth <<EMAIL>>)
=========================================================
The cairo community is pleased (but somewhat sheepish) to announce the
1.6.2 release of the cairo graphics library. This is an update to
yesterday's 1.6.0 release with an important fix to prevent cairo's
PostScript output from crashing some printers. This release also
includes a locking fix for cairo's xlib backend to improve thread
safety. There are no changes beyond these two fixes.

Fix for PostScript printer crash
--------------------------------
Adrian Johnson discovered that cairo 1.6.0 was being a bit hard on
PostScript printers, by changing the font matrix very frequently. This
causes some PostScript interpreters to allocate new font objects every
few glyphs, eventually exhausting available resources. The fix
involves leaving translational components of the font matrix as zero,
so that the PostScript interpreter sees an identical font matrix
repeatedly, and can more easily share internal font object resources.

This fix has been tested to resolve the bugs posted here, (for both
Xerox and Dell printers):

	Printing some PDFs from evince is crashing our Xerox printer
	https://bugs.freedesktop.org/show_bug.cgi?id=15348

	Cairo-generated postscript blocks Dell 5100cn
	https://bugs.freedesktop.org/show_bug.cgi?id=15445

Add missing locking in cairo-xlib
---------------------------------
Chris Wilson noticed that cairo 1.6.0 was manipulating an internal
cache of GC object within cairo's Xlib backend without proper
locking. The missing locking could cause failures for multi-threaded
applications. He fixed this in 1.6.2 by adding the missing locks.

Release 1.6.0 (2008-04-10 Carl Worth <<EMAIL>>)
=========================================================
The cairo community is quite pleased to announce the 1.6.0 release of
the cairo graphics library. This is a major update to cairo, with new
features and enhanced functionality which maintains compatibility for
applications written using cairo 1.4, 1.2, or 1.0. We recommend that
anybody using a previous version of cairo upgrade to cairo 1.6.0.

The most significant new features in this release are dramatically
improved PDF and PostScript[*] output, support for arbitrary X server
visuals (including PseudoColor), a new Quartz backend, and and a new
"win32 printing" backend. See below for more details on these and
other new features.

New dependency on external pixman library (Thanks, Søren!)
----------------------------------------------------------
As of cairo 1.6, cairo now depends on the pixman library, for which
the latest release can be obtained alongside cairo:

	https://cairographics.org/releases/pixman-0.10.0.tar.gz

This library provides all software rendering for cairo, (the
implementation of the image backend as well as any image fallbacks
required for other backends). This is the same code that was
previously included as part of cairo itself, but is now an external
library so that it can be shared by both cairo and by the X server,
(which is where the code originated).

Improved PDF, PostScript, and SVG output (Thanks, Adrian!)
----------------------------------------------------------
Users of the cairo-pdf, cairo-ps, and cairo-svg should see a dramatic
improvement from cairo 1.2/1.4 to 1.6. With this release there are now
almost no operations that will result in unnecessary rasterization in
the PDF and PostScript. Rasterized "image fallbacks" are restricted
only to minimal portions of the document where something is being
drawn with cairo that is beyond the native capabilities of the
document, (this is rare for PDF or SVG, but occurs when blending
translucent objects for PostScript).

This means that the final output will be of higher quality, and will
also be much smaller, and therefore will print more quickly. The
machinery for doing analysis and minimal fallbacks also benefits the
win32-printing surface described below.

In addition to doing less rasterization, the PostScript and PDF output
also has several other improvements to make the output more efficient
and more compatible with specifications.

[*] Note: Just before this release, a bug has been reported that the
PostScript output from cairo can crash some printers, (so far the
following models have been reported as problematic Xerox Workcentre
7228 or 7328 and Dell 5100cn). We will implement a workaround as soon
as we can learn exactly what in cairo's output these printers object
to, (and we could use help from users that have access to misbehaving
printers). This bug is being tracked here:

	Printing some PDFs from evince is crashing our Xerox printer
	https://bugs.freedesktop.org/show_bug.cgi?id=15348

New support for arbitrary X server visuals (Thanks, Keith and Behdad!)
----------------------------------------------------------------------
As of cairo 1.6, cairo should now work with an arbitrary TrueColor or
8-bit PseudoColor X server visual. Previous versions of cairo did not
support these X servers and refused to draw anything. We're pleased to
announce that this limitation has been lifted and people stuck with
ancient display systems need no longer be stuck with ancient software
just because of cairo.

New, supported Quartz backend for Mac OS X (Thanks, Brian and Vladimir!)
------------------------------------------------------------------------
As of cairo 1.6, the cairo-quartz backend is now marked as "supported"
rather than "experimental" as in previous cairo releases. Its API now
has guarantees of API stability into future cairo releases, and its
output quality is comparable to other backends. There have been
significant improvements to cairo-quartz since 1.4. It now uses many
fewer image fallbacks, (meaning better performance), and has greatly
improved text rendering.

New, "win32 printing" backend (Thanks, Adrian and Vladimir!)
------------------------------------------------------------
A new win32-printing surface has been added with an interface very
similar to the original win32 surface, (both accept an HDC
parameter). But this new surface should only be called with a printing
DC, and will result in all drawing commands being stored into a
meta-surface and emitted after each page is complete. This allows
cairo to analyze the contents, (as it does with PDF, PostScript, and
SVG backends), and to do minimal image-based fallbacks as
necessary. The analysis keeps things as efficient as possible, while
the presence of fallbacks, (when necessary), ensure the consistent,
high-quality output expected from cairo.

Robustness fixes (Thanks, Chris!)
---------------------------------
There has been a tremendous number of improvements to cairo's
robustness. Areas that have been improved include:

	* Proper reporting of errors

	* Responding correctly to invalid input

	* Avoiding integer overflows

	* Avoiding memory leaks on error-recovery paths

	* Making reference counting thread safe

	* Exhaustive testing of memory allocation points

Other fixes (Thanks, everybody!)
--------------------------------
Cairo's internal fixed-point representation has been changed from
16.16 to 24.8. This has a direct impact on applications as it allows
much larger objects to be drawn before internal limits in cairo make
the drawing not work.

The CAIRO_EXTEND_PAD mode is now fully supported by surface
patterns. This mode allows applications to use cairo_rectangle and
cairo_fill to draw scaled images with high-quality bilinear filtering
for the internal of the image, but without any objectionably blurry
edges, (as would happen with the default EXTEND_NONE and cairo_paint).

Rendering with CAIRO_ANTIALIAS_NONE has been fixed to be more
predictable, (previously image rendering and geometry rendering would
be slightly misaligned with respect to each other).

The reference manual at https://cairographics.org/manual now documents
100% of the functions and types in cairo's public API.

API additions
-------------
Several small features have been added to cairo with new API functions:

cairo_format_stride_for_width

    Must be called to compute a properly aligned stride value before
    calling cairo_image_surface_create_for_data.

cairo_has_current_point

    Allows querying if there is a current point defined for the
    current path.

cairo_path_extents

    Allows querying for path extents, (independent of any fill or
    stroke parameters).

cairo_surface_copy_page
cairo_surface_show_page

    Allow beginning a new document page without requiring a cairo_t
    object.

cairo_ps_surface_restrict_to_level
cairo_ps_get_levels
cairo_ps_level_to_string
cairo_ps_surface_set_eps

    Allow controlling the Post PostScript level, (2 or 3), to
    target, as well as to generate Encapsulated PostScript (EPS).

cairo_quartz_font_face_create_for_cgfont

    Create a quartz-specific cairo_font_face_t from a CGFontRef.

cairo_win32_font_face_create_for_logfontw_hfont

    Create a win32-specific cairo_font_face from a LOGFONTW and an
    HFONT together.

Thanks, Everyone!
-----------------
I've accounted for 32 distinct people with attributed code added to
cairo between 1.4.14 and 1.6.0, (their names are below). That's an
impressive number, but there are certainly dozens more that
contributed with testing, suggestions, clarifying questions, and
encouragement. I'm grateful for the friendships that have developed as
we have worked on cairo together. Thanks to everyone for making this
all so much fun!

Adrian Johnson, Alp Toker, Antoine Azar, Behdad Esfahbod,
Benjamin Otte, Bernardo Innocenti, Bertram Felgenhauer,
Boying Lu, Brian Ewins, Carl Worth, Chris Heath, Chris Wilson,
Claudio Ciccani, Emmanuel Pacaud, Jeff Muizelaar, Jeremy Huddleston,
Jim Meyering, Jinghua Luo, Jody Goldberg, Jonathan Gramain,
Keith Packard, Ken Herron, Kouhei Sutou, Kristian Høgsberg,
Larry Ewing, Martin Ejdestig, Nis Martensen, Peter Weilbacher,
Richard Hult, Shailendra Jain, Søren Sandmann Pedersen,
Vladimir Vukicevic

Snapshot 1.5.20 (2008-04-04 Carl Worth <<EMAIL>>)
===========================================================
This is the tenth snapshot in cairo's unstable 1.5 series. It comes
just two days (and only one working day) after the 1.5.18
snapshot. The quick snapshot is due to two embarrassing bugs (both
affecting cairo-xlib) that had been introduced in the 1.5.18
snapshot. The fixes for these are described below along with a few
other fixes, (which hopefully aren't introducing new bugs this time).

cairo-xlib
----------
Revert fix from 1.5.18 to allow pattern expansion based on the filter
mode. This fix seemed so boring, (the use case it addresses is almost
never used in practice), that it didn't even get mentioned in the
1.5.18 release notes. However, the "fix" happened to break rendering
that is always used resulting in corrupt image rendering in mozilla,
evolution, and probably everything else that uses cairo.

Fix to avoid BadMatch errors in cairo_surface_create_similar. These
were introduced, (inadvertently, of course), as part of the fix in
1.5.18 for creating similar surfaces without the Render
extension. Again, thanks to mozilla, (and Vladimir Vukicevic in
particular), for noticing our mistake.

general
-------
Correctly handle an in-error surface in
cairo_surface_write_to_png. Previously this function would cause an
assertion failure if you gave it a finished surface. Now it cleanly
returns a CAIRO_STATUS_SURFACE_FINISHED result instead.

Avoid potentially infinite wandering through memory inside
_cairo_hull_prev_valid. Thanks to Jonathan Watt for noticing this
problem:

	https://bugzilla.mozilla.org/show_bug.cgi?id=306649#c21

cairo-pdf
---------
Fix generation of "soft" masks made by drawing to a similar surface
and then calling cairo_mask_surface() with it.

cairo-svg
---------
Fix for code that uses cairo_mask() on an intermediate surface which
is later passed to cairo_mask_surface().

Snapshot 1.5.18 (2008-04-05 Carl Worth <<EMAIL>>)
===========================================================
This is the ninth snapshot in cairo's unstable 1.5 series. It comes
just 4 days after the 1.5.16 snapshot. We had hoped to not need
another snapshot before the final 1.6.0 release, but several critical
bugs were found and fixed in the last few days, so we thought it
important to let people test the fixes with this snapshot. See below
for details.

documentation
-------------
The README now lists necessary dependencies.

Various graphics state defaults are now documented, (source pattern is
opaque black, line width is 2.0, line join is miter, line cap is butt,
miter limit is 10.0, etc.).

general
-------
Several cleanups have been made along many error-path returns,
(carefully propagating up the original error status values, cleaning
up memory leaks during error recovery, etc.). This is yet another in
Chris "ickle" Wilson's long series of error-handling cleanups during
the 1.5 series.

Avoid undesired clipping when drawing scaled surface patterns with
bilinear filtering.

cairo-pdf
---------
Fix emission of 1-bit alpha masks in PDF output.

Fix a bug that would cause glyphs to be misplaced along the Y axis:

    http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=%23474136

    Originally, an issue about a crash, but later leading to the
    misplaced glyphs issue being discovered.

cairo-ps
--------
Fix misplaced glyphs in cairo's PostScript output.

    This issue occurs when consecutive glyphs are placed far
    apart. This case is exercised by the new ft-show-glyphs-table test
    case, which was originally inspired by the Debian bug #23474136
    mentioned above.

Fix more misplaced glyphs in cairo's PostScript output:

    The issue here showed up under very particular circumstance, (when
    converting a PDF file with a CFF font with CID Identity-H encoding
    and using glyph 0, (defined by the CFF specification as .notdef)
    as a space instead). More concretely, this problem appeared when
    converting the UbuntuDesktop.pdf file mentioned in this bug
    report:

	https://bugs.freedesktop.org/show_bug.cgi?id=15348#c3

    As usual with arcane font-encoding-specific bugs like this, many
    thanks to Adrian Johnson for his magical ability to dive into
    specifications and emerge almost instantaneously with fixes. And
    thanks to Sebastien Bacher for bringing the bug to our attention.

cairo-xlib
----------
Fix serious failure on X servers without the Render extension.

    Since the 1.5.14 snapshot (with support for PseudoColor visuals),
    any application attempting to create a "similar" xlib surface would
    fail on an X server without the Render extension. Thanks to
    Frederic Crozat for pointing out that cairo's test suite was
    entirely failing when run against Xvfb.

Avoid crashing cairo-xlib applications for too-large glyphs

    Naively sending glyphs of any size to the X server will eventually
    violate the X limit on maximum request sizes. We now properly
    detect when a glyph would be too large and use existing fallbacks
    to render the glyph rather than trying to send it to the X server.

Enable the buggy_repeat workaround for Xorg servers < 1.4

    We have determined that Xorg 1.3.0 (as packaged in Fedora 8 at
    least) has a bug that can result in an X server crash when cairo
    uses certain X Render repeat operations, (as exercised by cairo's
    extend-reflect test). We avoid this crash by using fallbacks
    whenever a repeating surface is needed for any Xorg server with a
    version less than 1.4. This is slower, but should prevent the
    crash.

    (Meanwhile, there appears to be a separate bug where some X
    servers or specific X-server drivers will use random pixmap data
    when asked to draw a repeating surface. The buggy_repeat
    workaround would also avoid those problems, but we have not yet
    characterized whether the new "version < 1.4" is a good
    characterization of those problems or not.)

cairo-quartz-font
-----------------
Implement cairo_font_extents for this backend.

The cairo-quartz-font implementation added in the 1.5.14 snapshot was
entirely missing support for the cairo_font_extents function. Thanks to
Richard Hult for pointing out this obvious shortcoming, (and obvious
lack of coverage in our test suite):

	CGFont backend returns 0 font extents
	https://bugs.freedesktop.org/show_bug.cgi?id=15319

Snapshot 1.5.16 (2008-04-01 Carl Worth <<EMAIL>>)
===========================================================
This is the eighth snapshot in cairo's unstable 1.5 series. It comes
less than two weeks after the 1.5.14 snapshot and it really is a
legitimate snapshot, (in spite of sharing this date with that of many
bogus announcements). The major change in this snapshot is that the
cairo-quartz backend is now officially "supported", including new API
to construct a font face from a CGFontRef . Also several bug fixes
have been fixed in many backends. See below for details.

general
-------
Cairo now depends on pixman 0.10.0 which was recently released. The
latest pixman release can always be found alongside cairo releases at:

   https://cairographics.org/releases

Increase the precision of color stops for gradients. This fixes a
regression in gradient rendering that had been present since the
1.5.12 snapshot.

paginated (all of ps, pdf, svg, and win32-printing)
---------------------------------------------------
Fix assertion failure when some drawing elements are outside the page
boundaries, (this bug was noticed when using Inkscape to print a
drawing with landscape orientation to a portrait-oriented piece of
paper).

cairo-ps
--------
Fix of bug causing incorrect glyph positioning.

Fix handling of CAIRO_OPERATOR_SOURCE.

cairo-pdf
---------
More reduction of unnecessary digits of precision in PDF output.

Fix handling of CAIRO_OPERATOR_SOURCE.

cairo-svg
---------
Fix bug in usage of libpng that was preventing cairo_mask from working
with the svg backend.

Fix transformation of source pattern for cairo_stroke().

cairo-win32-printing
--------------------
Fix fallback resolution, (thanks again to inkscape users/developers
for helping us find this one).

cairo-quartz
------------
Mark the cairo-quartz backend as "supported" rather than
"experimental". This means the following:

    * The backend will now be built by default (if possible).

    * We are committing that the backend-specific API (as published in
      cairo-quartz.h) are stable and will be supported in all future
      cairo 1.x releases.

    * We are committing that the output quality of this backend
      compares favorably with other cairo backends, (and that quality
      is ensured by good results from the cairo test suite).

    * We recommend that distributions build and distribute this
      backend when possible.

Note that the cairo_quartz_image API (in cairo-quartz-image.h) is
still experimental, will not build by default, (pass
--enable-quartz-image to configure to build it), and may see API
changes before it is marked as "supported" in a future release.

Put the CAIRO_FONT_TYPE_ATSUI name back into
cairo-deprecated.h. Without this, the cairo 1.5.14 snapshot broke all
builds for applications using the C++ cairomm bindings (and perhaps
others) which have the CAIRO_FONT_TYPE_ATSUI name in their header
files. This breakage happened even for applications not using
cairo-quartz at all.

    Note: Even though the CAIRO_FONT_TYPE_ATSUI name is provided to
    avoid this build breakage, we still recommend that bindings and
    applications move to the new, and more accurate,
    CAIRO_FONT_TYPE_QUARTZ name.

Replace the implementation of cairo-quartz-font to use CFFont instead
of ATSUI. The CGFont API is a better fit than ATSUI, and this new
implementation is also more correct than the old one as well.

This also adds the following new API call:

	cairo_public cairo_font_face_t *
	cairo_quartz_font_face_create_for_cgfont (CGFontRef font);

The previous cairo_quartz_font_face_create_for_atsu_font_id function
continues to exist and is part of the supported API going
forward. (However, the old name of that same function, which was
cairo_atsui_font_face_create_for_atsu_font_id is officially
deprecated. Any source code using the old name should be updated to
use the new name.)

Fix transformation of source pattern for cairo_stroke().

cairo-win32
-----------
Avoid crash in create_similar is cairo_win32_surface_create fails.

Snapshot 1.5.14 (2008-03-20 Carl Worth <<EMAIL>>)
===========================================================
This is the seventh snapshot in cairo's unstable 1.5 series. It comes
3 weeks after the 1.5.12 snapshot. This snapshot includes support for
arbitrary X server visuals, (including PseudoColor), which was the
final remaining cairo-specific item on the cairo 1.6 roadmap. It also
includes a huge number of improvements to the cairo-quartz backend. So
this is effectively a cairo 1.6 release candidate. We expect very few
changes from now until 1.6 and only for specific bug fixes.

API Change
----------
Rename ATSUI font backend to Quartz font backend. This affects the
following usage:

	--enable-atsui		-> --enable-quartz-font
	CAIRO_HAS_ATSUI_FONT	-> CAIRO_HAS_QUARTZ_FONT
	CAIRO_FONT_TYPE_ATSUI	-> CAIRO_FONT_TYPE_QUARTZ

	cairo_atsui_font_face_create_for_atsu_font_id ->
	cairo_quartz_font_font_create_for_atsu_font_id

This API change is justified by the cairo-quartz backend still be
marked as "experimental" rather than "supported", (though this is one
step toward making the change to "supported" before 1.6). Cairo will
still provide ABI compatibility with the old symbol name, however.

paginated (all of ps, pdf, svg, and win32-printing)
---------------------------------------------------
Optimize by not analyzing an image surface for transparency more than
once, (previously all images were analyzed twice).

cairo-ps and cairo-pdf
----------------------
Avoiding emitting a matrix into the stroke output when unnecessary,
(making output size more efficient).

Reduce rounding error of path shapes by factoring large scale factors
out of the path matrix, (ensuring that a fixed-number of printed
digits for path coordinates contains as much information as possible).

Reduce excess digits for text position coordinates. This makes the
output file size much smaller without making the result any less
correct.

cairo-ps
--------
Eliminate bug causing extraneous text repetition on Linux PostScript
output in some cases.

	See: Mozilla Bug 419917 – Printed page contents are reflected
	inside bordered tables (Linux-only)

	https://bugzilla.mozilla.org/show_bug.cgi?id=419917

Optimize output when EXTEND_PAD is used.

cairo-pdf
---------
Fix to not use fill-stroke operator with transparent fill, (else PDF
output doesn't match the cairo-defined correct result). See:

	https://bugs.launchpad.net/inkscape/+bug/202096

cairo-svg
---------
Fix stroke of path with a non-solid-color source pattern:

	https://bugs.freedesktop.org/show_bug.cgi?id=14556

cairo-quartz
------------
Fix text rendering with gradient or image source pattern.

Handling antialiasing correctly for cairo_stroke(), cairo_clip(), and
cairo_show_text()/cairo_show_glyphs().

Correctly handle gradients with non-identity transformations:

	Fixes https://bugs.freedesktop.org/show_bug.cgi?id=14248

Add native implementation of REPEAT and REFLECT extend modes for
gradients.

Fix implementation for the "unbounded" operators, (CAIRO_OPERATOR_OUT,
_IN, _DEST_IN, and _DEST_ATOP).

Correctly handle endiannees in multi-architecture compiles on Mac OS
X.

Avoid behavior which would cause Core Graphics to print warnings to
the console in some cases.

cairo-win32
-----------
Fix handling of miter limit.

cairo-win32-printing
--------------------
Fix to not use a 1bpp temporary surface in some cases while printing,
(so grayscale data is preserved rather than just becoming black and
white).

cairo-xlib
----------
Add support for rendering to arbitrary TrueColor X server
visuals. This fixes at least the following bugs:

	cairo doesn't support 8-bit truecolor visuals
	https://bugs.freedesktop.org/show_bug.cgi?id=7735

	cairo doesn't support 655 xlib format
	https://bugs.freedesktop.org/show_bug.cgi?id=9719

Add support for rendering to 8-bit PseudoColor X server visuals. This
fixes the following bug:

	Cairo doesn't support 8-bit pseudocolor visuals
	https://bugs.freedesktop.org/show_bug.cgi?id=4945

Snapshot 1.5.12 (2008-02-28 Carl Worth <<EMAIL>>)
===========================================================
This is the sixth snapshot in cairo's unstable 1.5 series. It comes 1
week after the 1.5.10 snapshot. This snapshot includes the
long-awaited change from 16.16 to 24.8 fixed-point values, (see below
for why you should care).  It also includes several backend-specific
bug fixes.

24.8 fixed-point format
-----------------------
Cairo has always converted path coordinates to a fixed-point
representation very early in its processing. Historically, this has
been a 32-bit representation with 16 bits of integer for the
device-pixel grid and 16 bits of sub-pixel positioning. The choice of
16 bits for the integer coordinate space was based on the 16-bit limit
for X Window drawables.

This 16-bit limit has proven problematic for many applications. It's
an especially vexing problem when targeting non-X backends that don't
have any 16-bit restriction. But even when targeting cairo-xlib, it's
often desirable to draw a large shape, (say a background rectangle),
that extends beyond the surface bounds and expect it to fill the
surface completely, (rather than overflowing and triggering random
behavior).

Meanwhile, nobody has ever really needed 16 bits of sub-pixel
precision.

With this snapshot, the fixed-point system is still in place and is
still using a 32-bit representation, (future versions of cairo might
move entirely to floating-point when targeting PDF output for
example). But the representation now provides 24 bits of pixel
addressing and only 8 bits of sub-pixel positioning. This should give
a much less stifling space to many applications.

However, the underlying pixman library still has 16-bit limitations in
many places, (it has its roots in the X server as well). Until those
are also fixed, applications targeting cairo image surfaces, or
hitting software fallbacks when targeting other surfaces will still
encounter problems with device-space values needing more than 16
integer bits.

generic fixes
-------------
Add a few tests to the test suite to increase coverage.

Cleanup a few error-handling paths, (propagate error correctly).

cairo-ft
--------
Fix handling of font sizes smaller than 1 device pixel.

cairo-pdf
---------
Fix to properly save/restore clip when analyzing meta-surface
patterns, (fixing a couple of test-suite failures).

Implement native support for CAIRO_OPERATOR_SOURCE when the source
pattern is opaque.

Emit rectangles as PDF rectangles ("re" operator) rather than as
general paths.

cairo-ps
--------
Fix to work properly with the 16.16->24.8 change.

cairo-svg
---------
Fix CAIRO_EXTEND_REFLECT by using an image fallback, (there's no
direct SVG support for reflected patterns).

Fix the use of alpha-only masks, (such as CAIRO_FORMAT_A8).

cairo-quartz
------------
Add new API for efficiently using image data as a source:

	cairo_surface_t *
	cairo_quartz_image_surface_create (cairo_surface_t *image_surface);

	cairo_surface_t *
	cairo_quartz_image_surface_get_image (cairo_surface_t *surface);

For full documentation, see:

	https://cairographics.org/manual/cairo-Quartz-Surfaces.html#cairo-quartz-image-surface-create

Several fixes for cairo_mask().

cairo-atsui
-----------
Change default from from Monaco to Helvetica to be more consistent
with other font backends.

Snapshot 1.5.10 (2008-02-20 Carl Worth <<EMAIL>>)
===========================================================
This is the fifth snapshot in cairo's unstable 1.5 series. It comes 3
weeks after the 1.5.8 snapshot. This snapshot adds one new API
function, (cairo_has_current_point), and the usual mix of
improvements, (more efficient PostScript/PDF output, optimized
stroking), and fixes (more robust error-handling, etc.). See below for
details.

New API
-------
Add a new function to query if there is a current point:

	cairo_bool_t
	cairo_has_current_point (cairo_t *cr);

There is no current point immediately after cairo_create(), nor after
cairo_new_path() or cairo_new_sub_path(). There is a current point
after any of the path-creation functions, (cairo_move_to,
cairo_line_to, cairo_curve_to, etc.).

With this new function, we also revert the change of the return type
of cairo_get_current_point from cairo 1.5.8, (it's now a void function
again).

Optimizations
-------------
Optimize stroking code to avoid repeated calculation of redundant
values, (particularly significant for very large, offscreen paths).

General fixes
-------------
Patch a few more potential buffer overruns, (due to integer
overflow).

Many fixes and improvements to cairo's error-handling, (ensure that
correct error values are returned, clean up memory leaks on
error-handling paths, etc.).

Fix a potential infinite loop when stroking a spline with a pen that
has been transformed to a line segment.

Remove treating NULL as a synonym for a valid cairo_font_options_t*
with default values, (a change that had been introduced as of cairo
1.5.8).

Remove the altered handling of tolerance and fallback-resolution that
had been introduced as of cairo 1.5.4.

cairo-xlib
----------
Pass the original Drawable, (as opposed to the root window), to
XCreatePixmap when creating a similar surface. This gives the X server
more information so that it can be clever and efficient.

cairo-pdf
---------
Fix the rendering of repeating and reflecting patterns.

Ensure miter limit is always >= 1, (smaller limits are not meaningful,
but they can cause some PDF viewers to fail to display pages).

Generate more efficient output when the same path is used for both
fill and stroke.

cairo-ps
--------
Start sharing much of the cairo-pdf code rather than implementing very
similar code in cairo-ps.

Implement native support for repeating and reflecting linear
gradients.

Implement reflected surface patterns.

Ensure miter limit is always >= 1, (smaller limits are not meaningful,
but they can cause some PostScript viewers to crash).

Generate PostScript that will perform more efficiently and use less
memory on printers, (use currentfile instead of a giant string array
for image data, and avoid using PostScript patterns for paint() and
fill() when possible).

cairo-svg
---------
Avoid unnecessary rasterization when copying a "similar" surface to
another svg surface, (allow the SOURCE operator to be implemented with
all-vector operations if there are no underlying objects).

cairo-atsui
-----------
Eliminate infinite loop when attempting to render an empty string.

Snapshot 1.5.8 (2008-01-30 Carl Worth <<EMAIL>>)
==========================================================
This is the fourth snapshot in cairo's unstable 1.5 series. It comes 2
weeks after the 1.5.6 snapshot. It adds a few new API functions. Most
notably all callers of cairo_image_surface_create_for_data should now
be calling cairo_format_stride_for_width to compute a legal stride
value. See below for more details.

New API in cairo 1.5.8
----------------------
We've added a new function that should be called to compute a legal
stride value before allocating data to be used with
cairo_image_surface_create_for_data:

	int
	cairo_format_stride_for_width (cairo_format_t	format,
				       int		width);

We've also added a new cairo_path_extents function that can be used to
compute a bounding box for geometry such as a single line segment,
(contrast with cairo_path_extents and cairo_stroke_extents):

	void
	cairo_path_extents (cairo_t *cr,
			    double *x1, double *y1,
			    double *x2, double *y2);

And finally, we've added a function to allow for querying the
XRenderPictFormat of a cairo-xlib surface:

	XRenderPictFormat *
	cairo_xlib_surface_get_xrender_format (cairo_surface_t *surface);

API changes
-----------
Fix return types of cairo_surface_show_page and
cairo_surface_copy_page. This is an API change to functions that are
new in the 1.5 series, so not an API break compared to any stable
cairo release, (1.0.x, 1.2.x, 1.4.x).

Change the return type of cairo_get_current_point() from void to
cairo_status_t. This allows the caller to receive a
CAIRO_STATUS_NO_CURRENT_POINT value to distinguish the a current point
at the origin from no current point existing.

Performance improvement
-----------------------
Improve performance of clipping by using an optimized code path
internally, (with the ADD operator instead of IN).

General bug fixes
-----------------
Fix various cairo_*_extents functions to initialize the return-value
variables even in the case of a cairo_t in error.

Treat NULL as a legitimate value for cairo_font_options_t*. [NOTE:
On discussion afterwards, we decided against this change so it has
been removed as of cairo 1.5.10.]

Fix rendering with CAIRO_ANTIALIAS_NONE to be more predictable, (that
is, to avoid seams appearing when geometry and imagery share an
identical edge). Portions of this fix are in the pixman library and
will appear in a future release of that library.

Avoid triggering an error for a font size of 0.

Miscellaneous changes
---------------------
Require pixman >= 0.9.6.

There has been a tremendous amount improvement to cairo's
documentation. We're delighted that 100% of the public API has at
least some documentation in the API reference manual. Many thanks to
Behdad Esfahbod and Nis Martensen for leading this effort.

cairo-pdf and cairo-ps
----------------------
Eliminate failure when a Type 1 font is embedded with an explicit
glyph 0.

cairo-pdf
---------
Implement a more correct and more efficient approach for patterns with
an extend mode of CAIRO_EXTEND_REFLECT.

cairo-ps
--------
Fix image masks to properly pack and pad mask bits.

cairo-quartz
------------
Take care to only use DrawTiledImage for integer-aligned images, (and
use slower paths to get the correct result in other cases).

cairo-win32
-----------
Fix for older versions of mingw.

Improve the handling of the clipping with the win32 and win32-printing
surfaces.

Fix rendering of non black/white text.

Snapshot 1.5.6 (2008-01-15 Carl Worth <<EMAIL>>)
==========================================================
This is the third snapshot in cairo's unstable 1.5 series. It comes
about 6 weeks after the 1.5.4 snapshot. The only API addition compared
to 1.5.4 is very minor, (a new value CAIRO_STATUS_TEMP_FILE_ERROR).
The remainder of the changes are the usual accumulation of bug fixes
and improvements. See below for details.

General bug fixes
-----------------
Fix handling of fonts that contain a mixture of outline and bitmapped
glyphs. There was a change in this handling in 1.5.4 that improved
some cases and also regressed other cases. Now, all cases should be
handled quite well.

Fix alignment issues that were causing SIGBUS failures on SPARC.

Fix a regression (which first appeared in 1.5.2) where stroking under
a large scale would sometimes incorrectly replace a miter join with a
bevel join. (Thanks to Keith Packard.)

Fix reporting of zero-sized extents to be {0,0} rather than
{INT_MAX,INT_MIN}. This avoids several integer overflow and
allocations of massive regions in some cases.

Fix failures of gradients with no stops, (quartz, ps, and pdf).

Fix handling of Type 1 fonts on Windows platforms.

Fix handling of Type 1 fonts with no specific family name in the font
itself, (generate a CairoFont-x-y name).

Handle NULL string values in cairo_show_text, cairo_show_glyphs, and
friends.

Many robustness improvements along error-handling paths, (thanks as
always, to Chris "ickle" Wilson).

Various other minor fixes.

Paginated backends (PDF/PostScript/win32-printing)
--------------------------------------------------
Avoid unnecessary rasterization when using a paginated surface as a
source, (such as drawing from one pdf surface to another).

Fix replaying of paginated surface with more than one level of push/pop
group.

cairo-xlib
----------
Fix xlib backend to not consider recent X server release as having a
buggy repeat implementation in the Render extension.

cairo-pdf
---------
Fix PDF output to avoid triggering very slow rendering in PDF viewers,
(avoid starting and stopping the content stream for each pattern
emission).

Support CAIRO_OPERATOR_SOURCE in cases where there is nothing below
the object being drawn.

Fix to avoid seams appearing between multiple fallback regions.

cairo-ps (PostScript)
---------------------
Use correct bounding box in Type 3 fonts.

Fix several bugs in cairo's PostScript output. These include making
the PostScript output more compatible with recent versions of
ghostscript that are more strict about Type 3 fonts, for
example.

Fix for win32 to not attempt to create temporary files in the root
directory, (where the user may not have write permission).

Avoid generating Level 3 PostScript if Level 2 is sufficient. Also,
add code in output documents to alert the user if Level 3 PostScript
is handed to a device that cannot handle PostScript beyond Level
2.

cairo-directfb
--------------
Various performance optimizations.

Fixed support for small surfaces (less than 8x8).

Provide support for environment variables CAIRO_DIRECTFB_NO_ACCEL to
disable acceleration and CAIRO_DIRECTFB_ARGB_FONT to enable ARGB fonts
instead of A8.

cairo-os2
---------
Allow OS/2 APIs instead of C library allocation functions.

Snapshot 1.5.4 (2007-12-05 Carl Worth <<EMAIL>>)
==========================================================
This is the second snapshot in cairo's unstable 1.5 series. It comes
just over 1 month after the 1.5.2 snapshot. There are no API changes
or additions in 1.5.4 compared to 1.5.2, but there are several bug
fixes, and some optimizations. Most of these apply to particular
backends. See below for details.

General improvements
--------------------
Use less memory for spline approximation calculations.

Change how the tolerance value is interpreted with regard to
fallback-resolution. [Note: On further discussion, we decided against
this change for now. It is removed as of cairo 1.5.10.]

Fix precision of floating-point values in vector-output backends to
avoid rounding errors with very small numbers.

Xlib improvements
-----------------
Fix bug in glyph rendering with xlib, (due to everything being clipped
out). This was a regression in the 1.5.2 snapshot that was visible in
the GIMP, for example. See:

	cairo 1.5.2 causes font problems in GIMP 2.4 status bar and evolution 2.12.1
	https://bugs.freedesktop.org/show_bug.cgi?id=13084

PostScript improvements
-----------------------
Fix bug leading to invalid PostScript files when rendering
text, (need "0 0 xyshow" instead of "0 xyshow").

Fix many issues with Type 3 fonts, including making the resulting text
extractable.

Quartz improvements
-------------------
Fix font metrics height value for ATSUI, (helps webkit on GTK+ OS X
layout nicely).

Fix gradients.

Fix EXTEND_NONE mode for patterns.

Fix cairo_quartz_surface_create to properly clear the new surface
in cairo_quartz_surface_create.

Fix to correctly handle 0x0 sized surfaces.

Optimize drawing of EXTEND_REPEAT patterns for OS X 10.5.

Snapshot 1.5.2 (2007-10-30 Carl Worth <<EMAIL>>)
==========================================================
This is the first snapshot in cairo's unstable 1.5 series. It comes 4
months after the 1.4.10 release. This snapshot includes significant
improvements to PDF and PostScript output, which is one of the things
in which we're most interested in getting feedback. There are a couple
of minor API additions, and several optimizations, (primarily in the
"print/vector" backends). And there are dozens of bug fixes and
robustness improvements.

New dependency on external pixman library
-----------------------------------------
A significant change in this snapshot compared to all previous cairo
releases is that cairo now depends on an external "pixman" library for
its software rendering. Previously this same code was compiled
internally as part of cairo, but now the code is separate so that both
cairo and the X server can now share common code, (thanks very much to
Søren Sandmann for his work on separating pixman and maintaining it).

So users will need to acquire and build pixman before being able to
build cairo. The current release is 0.9.6 and can be obtained from
here:

	https://cairographics.org/releases/pixman-0.9.6.tar.gz

 which can be verified with:

	https://cairographics.org/releases/pixman-0.9.6.tar.gz.sha1
	66f01a682c64403a3d7a855ba5aa609ed93bcb9e  pixman-0.9.6.tar.gz

	https://cairographics.org/releases/pixman-0.9.6.tar.gz.sha1.asc
	(signed by Carl Worth)

Major PDF/PostScript improvements
---------------------------------
Adrian Johnson has done some long-awaited work to make cairo's PDF and
PostScript output more interesting than ever before. First, many
operations that previously triggered image fallbacks will now be
rendered as native vectors. These operations include:

	PDF: cairo_push_group, cairo_surface_create_similar,
	cairo_mask, A8/A1 surface sources, repeating/reflecting linear
	gradients.

	PostScript: cairo_push_group, cairo_surface_create_similar,
	gradients, bilevel alpha masks, (for example, all values either 0 or
	255 for an A8 mask).

Not only that, but when an image fallback is required, it will now be
limited to only the necessary region. For example, a tiny translucent
image overlaying a small portion of text would previously caused an
entire PostScript page to be rendered as a giant image. Now, the
majority of that page will be nice text, and there will only be a tiny
image in the output.

Additionally, the PostScript output now carefully encodes text so that
if it is subsequently converted to PDF, the text will be
selectable.

This is very exciting progress, and we're hoping to hear from users
during the 1.5 series about how things have improved, (for example,
inkscape users doing cairo-based PDF export: please let us know how
things look). And feel free to pass your thanks along to Adrian for his excellent work.

NOTE: This much improved PDF output makes more sophisticated use of
functionality in the PDF specification. This means that cairo's output
will sometimes expose bugs in some free software PDF viewers, (evince,
poppler, and xpdf, for example), that are not yet ready for such PDF
files. We're working with the poppler maintainers to get these bugs
fixed as quickly as possible. In the meantime, please double-check
with other PDF viewers if cairo-generated PDF files are not being
rendered correctly. It may be due to a bug in the viewer rather than
in the PDF file that cairo has created.

Robustness improvements
-----------------------
Chris Wilson has made the largest contribution by far to cairo 1.5.2,
(in number of commits). His more than 150 commits include a huge
number of fixes to increase cairo's robustness. These fixes make cairo
more robust against invalid and degenerate input, (NaN, empty path,
etc.), against size-0 malloc calls, against memory leaks on
error-recovery paths, and against other failures during error
handling. He also implemented atomic operations to cairo, and used
them to fix cairo's previously non-thread-safe reference counting,
again improving robustness.

Chris has put a tremendous amount of time and effort into writing
analysis tools for this work, and in running those tools and fixing
the problems they report. We're very grateful for this work, and hope
that all cairo users appreciate the more robust implementation that
results from it.

This work is largely thankless, so it might make sense to notice
sometime that cairo has been running quite smoothly for you, and when
you do, send a quick "thank you" off to Chris Wilson, since it
is all definitely running smoother thanks to his work.

New API
-------
There are no major additions to cairo's core API. The only new,
generic functions are:

	void
	cairo_surface_copy_page (cairo_surface_t *surface);

	void
	cairo_surface_show_page (cairo_surface_t *surface);

which can now be used much more conveniently than the existing
cairo_copy_page and cairo_show_page functions in some
situations. These functions act identically, but require only a
cairo_surface_t* and not a cairo_t*.

All other API additions are specific to particular backends.

New cairo-win32 API (new font face function and "win32 printing" surface)
-------------------------------------------------------------------------
There is a new function for creating a win32 font face for both a
logfontw and an hfont together. This complements the existing
functions for creating a font face from one or the other:

	cairo_font_face_t *
	cairo_win32_font_face_create_for_logfontw_hfont (LOGFONTW *logfont,
							 HFONT font);

There is also a new "win32 printing" surface:

	cairo_surface_t *
	cairo_win32_printing_surface_create (HDC hdc);

This interface looks identical to the original
cairo_win32_surface_create, (both accept and HDC), but the behavior of
this new surface is very different. It should only be called with a
printing DC, and will result in all drawing commands being stored into
a meta-surface and emitted after each page is complete, with analysis
to do as minimal image-based fallbacks as necessary. The behavior and
implementation shares much with the PDF and PostScript backends.

New cairo-ps API (EPS and PostScript level control)
---------------------------------------------------
An often requested feature has been the ability to generate
Encapsulated PostScript (EPS) with cairo. We have that now with the
following very simple API. Just do cairo_ps_surface_create as usual
then call this function with a true value:

	void
	cairo_ps_surface_set_eps (cairo_surface_t       *surface,
				  cairo_bool_t           eps);

[NOTE: As always with snapshots, it's possible---though not very
likely---that the API could still be modified before a final
release. For example, this is the first public cairo function that
accepts a Boolean parameter. I'm generally opposed to Boolean
parameters, but this is probably the one case where I'm willing to
accept one, (namely a "set" function that accepts a single Boolean).]

Also, it is now possible to control what PostScript level to target,
(either level 2 or level 3), with the following new API:

	typedef enum _cairo_ps_level {
	    CAIRO_PS_LEVEL_2,
	    CAIRO_PS_LEVEL_3
	} cairo_ps_level_t;

	void
	cairo_ps_surface_restrict_to_level (cairo_surface_t    *surface,
					    cairo_ps_level_t    level);

	void
	cairo_ps_get_levels (cairo_ps_level_t const  **levels,
			     int                      *num_levels);

	const char *
	cairo_ps_level_to_string (cairo_ps_level_t level);

Improvement for cairo-quartz
----------------------------
Brian Ewins had contributed several improvements to cairo-quartz. These
include an implementation of EXTEND_NONE for linear and radial
gradients, (so this extend mode will no longer trigger image fallbacks
for these gradients), as well as native surface-mask clipping, (only
on OS X 10.4+ where the CGContextClipToMask function is available).

He also fixed a semantic mismatch between cairo and quartz for dashing
with an odd number of entries in the dash array.

We're grateful for Brian since not many quartz-specific improvements
to cairo would be happening without him.

Optimizations
-------------
Optimize SVG output for when the same path is both filled and stroked,
and avoid unnecessary identity matrix in SVG output. (Emmanuel Pacaud).

Optimize PS output to take less space (Ken Herron).

Make PS output more compliant with DSC recommendations (avoid initclip
and copy_page) (Adrian Johnson).

Make PDF output more compact (Adrian Johnson).

Release glyph surfaces after uploading them to the X server, (should
save some memory for many xlib-using cairo application). (Behdad
Esfahbod).

Optimize cairo-win32 to use fewer GDI objects (Vladimir Vukicevic).

win32-printing: Avoid falling back to images when alpha == 255
everywhere. (Adrian Johnson).

win32-printing: Avoid falling back for cairo_push_group and
cairo_surface_create_similar. (Adrian Johnson)

Bug fixes
---------
Avoid potential integer overflows when allocating large buffers
(Vladimir Vukicevic).

Preparations to allow the 16.16 fixed-point format to change to
24.8 (Vladimir Vukicevic).

Fix bugs for unsupported X server visuals (rgb565, rgb555, bgr888, and
abgr8888). (Carl Worth and Vladimir Vukicevic)

Fix bugs in PDF gradients (Adrian Johnson).

Fix cairo-xlib to build without requiring Xrender header
files (Behdad Esfahbod).

Make cairo more resilient in the case of glyphs not being available in
the current font. (Behdad Esfahbod)

Prevent crashes when both atsui and ft font backends are compiled in
(Brian Ewins).

Make font subsetting code more robust against fonts that don't include
optional tables (Adrian Johnson).

Fix CFF subsetting bug, (which manifested by generating PDF files that
Apple's Preview viewer could not read) (Adrian Johnson).

Fixed error handling for quartz and ATSUI backends (Brian Ewins).

Avoid rounding problems by pre-transforming to avoid integer-only
restrictions on transformation in GDI (Adrian Johnson).

Fixed an obscure bug (#7245) computing extents for some stroked
paths (Carl Worth).

Fix crashes due to extreme transformation of the pen, (seems to show
up in many .swf files for some reason) (Carl Worth).

Release 1.4.10 (2007-06-27 Carl Worth <<EMAIL>>)
==========================================================
This is the fifth update in cairo's stable 1.4 series. It comes
roughly three weeks after the 1.4.8 release. The most significant
change in this release is a fix to avoid an X error in certain cases,
(that were causing OpenOffice.org to crash in Fedora). There is also a
semantic change to include child window contents when using an xlib
surface as a source, an optimization when drawing many rectangles, and
several minor fixes.

Eliminate X errors that were killing OO.o (Chris Wilson)
--------------------------------------------------------
Cairo is fixed to avoid the X errors propagated when cleaning up
Render Pictures after the application had already destroyed the
Drawable they reference. (It would be nice if the X server wouldn't
complain that some cleanup work is already done, but there you have
it.) This fixes the bug causing OpenOffice.org to crash as described
here:

	XError on right click menus in OOo.
	https://bugzilla.redhat.com/bugzilla/show_bug.cgi?id=243811

Use IncludeInferiors when using xlib surface as a source (Ryan Lortie)
----------------------------------------------------------------------
When an xlib surface is used as the source of a draw operation the
contents of child windows are now included in the source data. The
semantics of drawing to xlib surfaces are unchanged (ie: draws are
still clipped by child windows overlapping the destination window).

Optimize drawing of many rectangles (Vladimir Vukicevic)
--------------------------------------------------------
Avoid O(N*N) loop when filling many axis-aligned rectangles, (either
many rectangles as separate sub-paths or due to dashing).

Miscellaneous fixes
-------------------
Fix cairo-perf on Solaris by linking to librt. (Behdad Esfahbod)

Fix make check for systems that require executable files to have a
particular extension. (Behdad Esfahbod)

Eliminate some warnings in cairo-quartz. (Brian Ewins)

Fix build-breaking typo for cairo-directfb. (Chris Wilson)

Release 1.4.8 (2007-06-07 Carl Worth <<EMAIL>>)
=========================================================
This is the fourth update in cairo's stable 1.4 series. It comes just
over five weeks after the 1.4.6 release. This release includes a
thread-safe surface-cache for solid patterns which significantly
improves text rendering with the xlib backend. Also, dozens of error
paths in cairo have been fixed thanks to extensive fault-injection
testing by Chris Wilson.

Surface cache for solid patterns
--------------------------------
Originally written by Jorn Baayen, the introduction of a small cache
for surfaces created for solid patterns improves performance
dramatically. For example, this reduces the volume of X requests
during text rendering to the same level as Xft.

This cache first made its appearance in a 1.3.x snapshot, but was
removed before appearing in any previous major release due to
complications with multi-threaded programs. For example, programs like
evince that would carefully restrict usage of cairo-xlib to a single
thread were unpleasantly surprised to find that using cairo-image in a
separate thread could trigger X requests.

Behdad Esfahbod designed a fix which was implemented by Chris
Wilson. Now, the necessary X requests are queued up until the next
time the application directly operates on an xlib surface.

Improved error handling paths
------------------------------
Chris Wilson continued the excellent work he started in cairo 1.4.4 to
make cairo much more robust against out-of-memory and other errors. He
applied his memory allocation fault injection cairo's main test suite,
(previously he had applied it to cairo's performance suite).

Chris's testing found dozens of bugs which he fixed. Many of these
bugs had perhaps never been hit by any users. But at least one was
hit by the gnome-about program which resulted in dozens of duplicated
bug reports against that program:

	https://bugzilla.gnome.org/show_bug.cgi?id=431990

We were very pleasantly surprised to see this bug get fixed as a
side-effect of Chris's work. Well done, Chris!

Other fixes
-----------
Cleanup of mutex declarations (Behdad Esfahbod)

Remove unnecessary clip region from SVG output (Emmanuel Pacaud)

Remove Xsun from the buggy_repeat blacklist (Elaine Xiong)

ATSUI: Fix glyph measurement: faster and more correct (Brian Ewins)

Quartz: fixed 'extend' behaviour for patterns, improved pattern performance,
and a few smaller correctness fixes. (Brian Ewins, Vladimir Vukicevic)

Release 1.4.6 (2007-05-01 Carl Worth <<EMAIL>>)
=========================================================
This is the third update in cairo's stable 1.4 series. It comes a
little less than three weeks since the 1.4.4 release. This release
fixes the broken mutex initialization that made cairo 1.4.4 unusable
on win32, OS/2, and BeOS systems. This release also adds significant
improvements to cairo's PDF backend, (native gradients!), and a couple
of performance optimizations, (one of which is very significant for
users of the xlib backend). See below for more details.

Repaired mutex initialization
-----------------------------
We apologize that cairo 1.4.4 did little more than crash on many
platforms which are less-frequently used by the most regular cairo
maintainers, (win32, OS/2, and BeOS). The mutex initialization
problems that caused those crashes should be fixed now. And to avoid
similar problems in the future, we've now started posting pre-release
snapshots to get better testing, (<NAME_EMAIL>
if you're interested in getting notified of those and testing them).

PDF Improvements
----------------
Thanks to Adrian Johnson, (cairo PDF hacker extraordinaire), we have
several improvements to cairo's PDF backend to announce:

Native gradients:

  As of cairo 1.4.6, cairo will now generate native PDF gradients in
  many cases, (previously, the presence of a gradient on any page
  would force rasterized output for that page). Currently, only
  gradients with extend types of PAD (the default) or NONE will
  generate native PDF gradients---others will still trigger
  rasterization, (but look for support for other extend modes in a
  future release). Many thanks to Miklós Erdélyi as well, who did the
  initial work for this support.

Better compatibility with PDF viewers:

  The PDF output from cairo should now be displayed correctly by a
  wider range of PDF viewers. Adrian tested cairo's PDF output against
  many PDF viewers, identified a common bug in many of those viewers
  (ignoring the CTM matrix in some cases), and modified cairo's output
  to avoid triggering that bugs (pre-transforming coordinates and
  using an identity matrix).

Better OpenType/CFF subsetting:

  Cairo will now embed CFF and TrueType fonts as CID fonts.

Performance optimizations
-------------------------
Faster cairo_paint_with_alpha:

  The cairo_paint_with_alpha call is used to apply a uniform alpha
  mask to a pattern. For example, it can be used to gradually fade an
  image out or in. Jeff Muizelaar fixed some missing/broken
  optimizations within the implementation of this function resulting
  in cairo_paint_with_alpha being up to 4 times faster when using
  cairo's image backend.

Optimize rendering of "off-screen" geometry:

  Something that applications often do is to ask cairo to render
  things that are either partially or wholly outside the current clip
  region. Since 1.4.0 the image backend has been fixed to not waste
  too much time in this case. But other backends have still been
  suffering.

  In particular, the xlib backend has often performed quite badly in
  this situation. This is due to a bug in the implementation of
  trapezoid rasterization in many X servers.

  Now, in cairo 1.4.6 there is a higher-level fix for this
  situation. Cairo now eliminates or clips trapezoids that are wholly
  or partially outside the clip region before handing the trapezoids
  to the backend. This means that the X server's performance bug is
  avoided in almost all cases.

  The net result is that doing an extreme zoom-in of vector-based
  objects drawn with cairo might have previously brought the X server
  to its knees as it allocated buffers large enough to fit all of the
  geometry, (whether visible or not). But now the memory usage should
  be bounded and performance should be dramatically better.

Miscellaneous
-------------
Behdad contributed an impressively long series of changes that
organizes cairo's internals in several ways that will be very
beneficial to cairo developers. Thanks, Behdad!

Behdad has also provided a utility for generating malloc statistics,
(which was used during the great malloc purges of 1.4.2 and
1.4.4). This utility isn't specific to cairo so may be of benefit to
others. It is found in cairo/util/malloc-stats.c and here are Behdad's
notes on using it:

    To build, do:

	make malloc-stats.so

    inside util/, and to use, run:

	LD_PRELOAD=malloc-stats.so some-program

    For binaries managed by libtool, eg, cairo-perf, do:

	../libtool --mode=execute /bin/true ./cairo-perf
	LD_PRELOAD="../util/malloc-stats.so" .libs/lt-cairo-perf

Finally, the cairo-perf-diff-files utility was enhanced to allow for
generating performance reports from several runs of the same backend
while some system variables were changed. For example, this is now
being used to allow cairo-perf to measure the performance of various
different acceleration architectures and configuration options of the
X.org X server.

Release 1.4.4 (2007-04-13 Carl Worth <<EMAIL>>)
=========================================================
This is the second update release in cairo's stable 1.4 series. It
comes just less than a month after 1.4.2. The changes since 1.4.2
consist primarily of bug fixes, but also include at least one
optimization. See below for details.

Of all the work that went into the 1.4.4 release

There have been lots of individuals doing lots of great work on cairo,
but two efforts during the 1.4.4 series deserve particular mention:

Internal cleanup of error handling, (Chris Wilson)
--------------------------------------------------
Chris contributed a tremendous series of patches (74 patches!) to
improve cairo's handling of out-of-memory and other errors. He began
by adding gcc's warn_unused_attribute to as many functions as
possible, and then launched into the ambitious efforts of adding
correct code to quiet the dozens of resulting warnings.

Chris also wrote a custom valgrind skin to systematically inject
malloc failures into cairo, and did all the work necessary to verify
that cairo's performance test suite runs to completion without
crashing.

The end result is a much more robust implementation. Previously, many
error conditions would have gone unnoticed and would have led to
assertion failures, segmentation faults, or other harder-to-diagnose
problems. Now, more than ever, cairo should cleanly let the user know
of problems through cairo_status and other similar status
functions. Well done, Chris!

More malloc reduction, (Mathias Hasselmann)
-------------------------------------------
After 1.4.0, Behdad launched an effort to chase down excessive calls
to malloc within the implementation of cairo. He fixed a lot of
malloc-happy objects for 1.4.2, but one of the worst offenders,
(pixman regions), was left around. Mathias contributed an excellent
series of 15 patches to finish off this effort.

The end result is a cairo that calls malloc much less often than it
did before. Compared to 1.4.2, 55% of the calls to malloc have been
eliminate, (and 60% have been eliminated compared to 1.4.0). Well
done, Mathias!

Other improvements since 1.4.2
------------------------------
• Centralize mutex declarations (will reduce future build breaks),
  (Mathias Hasselmann)

• Reduce malloc by caching recently freed pattern objects (Chris
  Wilson)

• Fix some broken composite operations (David Reveman)
	https://bugs.freedesktop.org/show_bug.cgi?id=5777

Backend-specific fixes
----------------------
PDF:
 • Use TJ operator for more compact representation of glyphs (Adrian
   Johnson)

 • Fix glyph positioning bug when glyphs are not horizontal
	https://lists.freedesktop.org/archives/cairo/2007-April/010337.html

win32:
 • Fix crash when rendering with bitmap fonts (Carl Worth)
	https://bugzilla.mozilla.org/show_bug.cgi?id=376498

xlib:
 • Turn metrics-hinting on by default (Behdad Esfahbod)

 • Fix edge-effect problem with transformed images drawn to xlib
   (Behdad Esfahbod)
	https://bugs.freedesktop.org/show_bug.cgi?id=10508

 • Avoid dereferencing a NULL screen. (Chris Wilson)
	https://bugs.freedesktop.org/show_bug.cgi?id=10517

Quartz/ATSUI:
 • Fix scaling of glyph surfaces
   (Brian Ewins)
	https://bugs.freedesktop.org/show_bug.cgi?id=9568

 • Fix compilation failure when both xlib and quartz enabled
   (Brian Ewins)

 • Fix rounding bug leading to incorrectly positioned glyphs
   (Robert O'Callahan)
	https://bugs.freedesktop.org/show_bug.cgi?id=10531

Release 1.4.2 (2007-03-19 Carl Worth <<EMAIL>>)
=========================================================
This is the first update release in cairo's stable 1.4 series. It
comes just less than 2 weeks after 1.4.0. We hadn't anticipated an
update this early, but we've managed to collect some important fixes
that we wanted to get out to cairo users as soon as possible, (6 fixes
for crashes, 1 case where graphical elements would not be drawn at
all, a handful of backend-specific bugs, and several important build
fixes).

There's almost nothing but bug fixes in this release, (see below one
optimization that Behdad did sneak in), so we recommend that everyone
upgrade to this release when possible.

Thanks to the many people that worked to fix these bugs, and those
that did the work to report them and to test the fixes, (wherever
possible both names are credited below).

Critical fixes
--------------
• Fix a crash due to a LOCK vs. UNLOCK typo (M. Drochner fixing Carl
  Worth's embarrassing typo).

  https://bugs.freedesktop.org/show_bug.cgi?id=10235

• Fix potential buffer overflow, which on some systems with a checking
  variant of snprintf would lead to a crash (Adrian Johnson, Stanislav
  Brabec, and sangu).

  https://bugs.freedesktop.org/show_bug.cgi?id=10267
  https://bugzilla.redhat.com/bugzilla/show_bug.cgi?id=232576

• Fix a crash in cairo_stroke_extents or cairo_in_stroke when line
  width is 0.0. (Carl Worth and Sebastien Bacher)

  https://bugs.freedesktop.org/show_bug.cgi?id=10231

• Fix a crash on certain combinations of X server/video drivers (Carl
  Worth and Tomas Carnecky).

  https://bugs.freedesktop.org/show_bug.cgi?id=10250

• Fix a crash due to mishandling of invalid user input (Carl Worth and
  Alexander Darovsky).

  https://bugs.freedesktop.org/show_bug.cgi?id=9844

• xlib: Cleanup server-side glyph caches on XCloseDisplay. This
  eliminated a crash detected by the perf suite, (and that
  applications could have run into as well). (Chris Wilson)

Other bug fixes
---------------
• Fix for some geometry which simply disappeared under some
  transformations---a stroked line with an extreme skew in X, for
  example (Carl Worth and Jonathan Watt).

  https://bugzilla.mozilla.org/show_bug.cgi?id=373632

• SVG: Fix radial gradients for CAIRO_EXTEND_REFLECT and when r0 > r1
  (Emmanuel Pacaud).

• PDF: Set page group color space to DeviceRGB.

  This fixes incorrect (muddy) transparent colors when rendering cairo
  PDF output in some viewers. (Adrian Johnson, Adam Goode, and
  MenTaLguY).

  https://lists.freedesktop.org/archives/cairo/2006-November/008551.html

• win32: Return correct metrics when hinting is off, and fix font
  descent computation (Behdad Esfahbod).

• quartz: Fix glyph interfaces to correctly return user-space rather
  than device-space coordinates (Brian Ewins).

  https://bugs.freedesktop.org/show_bug.cgi?id=9568

• xcb: Fix parameter-order confusion with xcb_create_pixmap, which now
  makes all tests that pass with xlib now pass with xcb (Carl Worth,
  Jamey Sharp).

• Fix some memory leaks in the perf suite (Chris Wilson).

• Fix perf suite to consider changes in pixman/src (Mathias
  Hasselmann).

Build fixes
-----------
• Don't include pre-generated cairo-features.h file. This was causing
  build failures when building with the directfb backend enabled
  (Behdad Esfahbod).

  https://bugs.freedesktop.org/show_bug.cgi?id=10189

• Eliminate use of maintainer mode from cairo's automake/configure
  script. This means that updates to files such as Makefile.am will
  take effect, (by rerunning automake and friends as necessary) when
  invoking make rather than being silently ignored.  (Behdad Esfahbod)

• Don't compile cairo-deflate-stream.c, which depends on zlib, unless
  building the pdf backend which requires it. (Carl Worth, Tor
  Lillqvist)

  https://bugs.freedesktop.org/show_bug.cgi?id=10202

• Don't make the ps backend link against zlib anymore, since it
  doesn't require it (Carl Worth).

• Use "find !" rather than "find -not" for better portability (Thomas
  Klausner).

  https://bugs.freedesktop.org/show_bug.cgi?id=10226

• Don't use unsupported visibility attribute "hidden" on Solaris
  (Gilles Dauphin, Thomas Klausner).

  https://bugs.freedesktop.org/show_bug.cgi?id=10227

Optimization
------------
• It was Behdad that suggested we focus strictly on bug fixes now that
  we shipped so many performance improvements in 1.4.0, but it was
  also Behdad that got distracted by the chance to remove a lot of
  mallocs from cairo. Paths, gstates, trapezoids, splines, polygons,
  and gradient color stops will now use small, stack-allocated buffers
  in the most common cases rather than calling malloc as
  often. (Behdad Esfahbod). And look for more from Mathias Hasselmann
  soon.

Release 1.4.0 (2007-03-06 Carl Worth <<EMAIL>>)
=========================================================
The many people[*] who have been working hard on cairo are very
pleased to announce the long-awaited release of cairo 1.4. This
release comes 4 months after the last stable update release (1.2.6)
and 9 months since the initial release of 1.2.0.

The release notes below are intended to capture the highlights of the
changes that have occurred from the 1.2 series to the new 1.4.0
release.

Performance improvements
------------------------
Within the cairo project, the last 6 months or so has seen an intense
effort focusing on the performance of cairo itself. That effort has
paid off considerably, as can be seen in the following highlights of
some of the performance differences from cairo 1.2.6 to cairo 1.4.0.

(Note: The performance results reported here were measured on an x86
laptop. Many of the improvements in 1.4---particular those involving
text rendering---are even more dramatic on embedded platforms without
hardware floating-point units. Such devices played an important part
of many of the optimizations that found their way into cairo over the
last few months.)

• Dramatic improvement when drawing objects that are mostly off-screen
  with the image backend (with the xlib backend this case is still
  slow due to an X server bug):

  image-rgba       long-lines-uncropped-100  479.64 ->  4.98: 96.24x speedup
  ███████████████████████████████████████████████▋

• Dramatic improvement when copying a small fraction of an image
  surface to an xlib surface:

   xlib-rgba              subimage_copy-512    3.93 ->  0.07: 54.52x speedup
  ██████████████████████████▊

• Dramatic improvement to tessellation speed for complex objects:

  image-rgb              tessellate-256-100  874.16 -> 34.79: 25.13x speedup
  ████████████▏
   xlib-rgba        zrusin_another_fill-415  148.40 -> 13.85: 10.72x speedup
  ████▉
   xlib-rgb                  world_map-800  680.20 -> 345.54:  1.97x speedup
  ▌

• Dramatic improvement to the speed of stroking rectilinear shapes,
  (such as the outline of a rectangle or "box"):

  image-rgb          box-outline-stroke-100    0.18 ->  0.01: 24.22x speedup
  ███████████▋
   xlib-rgb          box-outline-stroke-100    0.46 ->  0.06:  8.05x speedup
  ███▌


• Dramatic improvements to text rendering speeds:

   xlib-rgba       text_image_rgba_over-256   63.12 ->  9.61:  6.57x speedup
  ██▊

• 3x improvements to floating-point to fixed-point conversion speeds:

  image-rgba      pattern_create_radial-16     9.29 ->  3.44:  2.70x speedup
  ▉

• 2x improvements to linear gradient computation:

  image-rgb     paint_linear_rgb_source-512   26.22 -> 11.61:  2.26x speedup
  ▋

• 2x improvement to a case common in PDF rendering:

  image-rgb              unaligned_clip-100    0.10 ->  0.06:  1.81x speedup
  ▍

• 1.3x improvement to rectangle filling speed (note: this improvement
  is new since 1.3.16---previously this test case was a 1.3x slowdown
  compared to 1.2.6):

  image-rgba                 rectangles-512    6.19 ->  4.37:  1.42x speedup
  ▎
  xlib-rgba                  rectangles-512    7.48 ->  5.58:  1.34x speedup
  ▏

NOTE: In spite of our best efforts, there are some measurable
performance regressions in 1.4 compared to 1.2. It appears that the
primary problem is the increased overhead of the new tessellator when
drawing many, very simple shapes. The following test cases capture
some of that slowdown:

  image-rgba    mosaic_tessellate_lines-800   11.03 -> 14.29:  1.30x slowdown
  ▏
  image-rgba           box-outline-fill-100    0.01 ->  0.01:  1.26x slowdown
  ▏
  image-rgba        fill_solid_rgb_over-64     0.20 ->  0.22:  1.12x slowdown

  image-rgba       fill_image_rgba_over-64     0.23 ->  0.25:  1.10x slowdown

   xlib-rgb     paint_image_rgba_source-256    3.24 ->  3.47:  1.07x slowdown

We did put some special effort into eliminating this slowdown for the
very common case of drawing axis-aligned rectangles with an identity
matrix (see the box-outline-stroke and rectangles speedup numbers
above). Eliminating the rest of this slowdown will be a worthwhile
project going forward.

Also note that the "box-outline-fill" case is a slowdown while
"box-outline-stroke" is a (huge) speedup. These two test cases
resulted from the fact that some GTK+ theme authors were filling
between two rectangles to avoid slow performance from the more natural
means of achieving the same shape by stroking a single rectangle. With
1.4 that workaround should definitely be eliminated as it will now
cause things to perform more slowly.

Greatly improved PDF output
---------------------------
We are very happy to be able to announce that cairo-generated PDF
output will now have text that can be selected, cut-and-pasted, and
searched with most capable PDF viewer applications. This is something
that was not ever possible with cairo 1.2.

Also, the PDF output now has much more compact encoding of text than
before. Cairo is now much more careful to not embed multiple copies of
the same font at different sizes. It also compresses text and font
streams within the PDF output.

API additions
-------------
There are several new functions available in 1.4 that were not
available in 1.2. Curiously, almost all of the new functions simply
allow the user to query state that has been set in cairo (many new
"get" functions) rather than providing any fundamentally new
operations. The new functionality is:

• Getting information about the current clip region

  cairo_clip_extents
  cairo_copy_clip_rectangle_list
  cairo_rectangle_list_destroy

• Getting information about the current dash setting

  cairo_get_dash_count
  cairo_get_dash

• Getting information from a pattern

  cairo_pattern_get_rgba
  cairo_pattern_get_surface
  cairo_pattern_get_color_stop_rgba
  cairo_pattern_get_color_stop_count
  cairo_pattern_get_linear_points
  cairo_pattern_get_radial_circles

• Getting the current scaled font

  cairo_get_scaled_font

• Getting reference counts

  cairo_get_reference_count
  cairo_surface_get_reference_count
  cairo_pattern_get_reference_count
  cairo_font_face_get_reference_count
  cairo_scaled_font_get_reference_count

• Setting/getting user data on objects

  cairo_set_user_data
  cairo_get_user_data
  cairo_pattern_set_user_data
  cairo_pattern_get_user_data
  cairo_scaled_font_set_user_data
  cairo_scaled_font_get_user_data

• New cairo-win32 functions:

  cairo_win32_surface_create_with_ddb
  cairo_win32_surface_get_image
  cairo_win32_scaled_font_get_logical_to_device
  cairo_win32_scaled_font_get_device_to_logical

API deprecation
---------------
The CAIRO_FORMAT_RGB16_565 enum value has been deprecated. It never
worked as a format value for cairo_image_surface_create, and it wasn't
necessary for supporting 16-bit 565 X server visuals.

A sampling of bug fixes in cairo 1.4
------------------------------------
  • Fixed radial gradients
  • Fixed dashing (degenerate and "leaky" cases)
  • Fixed transformed images in PDF/PS output (eliminate bogus repeating)
  • Eliminate errors from CAIRO_EXTEND_REFLECT and CAIRO_EXTEND_PAD
  • cairo_show_page no longer needed for single-page output
  • SVG: Fix bug preventing text from appearing in many viewers
  • cairo-ft: Return correct metrics when hinting is off
  • Eliminate crash in cairo_create_similar if nil surface is returned
  • Eliminate crash after INVALID_RESTORE error
  • Fix many bugs related to multi-threaded use and locking
  • Fix for glyph spacing 32 times larger than desired (cairo-win32)
  • Fixed several problems in cairo-atsui (assertion failures)
  • Fix PDF output to avoid problems when printing from Acrobat Reader
  • Fix segfault on Mac OS X (measuring a zero-length string)
  • Fix text extents to not include the size of non-inked characters
  • Fix for glyph cache race condition in glitz backend (Jinghua Luo)
  • Fix make check to work on OPD platforms (IA64 or PPC64)
  • Fix compilation problems of cairo "wideint" code on some platforms
  • Many, many others...

Experimental backends (quartz, XCB, OS/2, BeOS, directfb)
---------------------------------------------------------
None of cairo's experimental backends are graduating to "supported"
status with 1.4.0, but two of them in particular (quartz and xcb), are
very close.

The quartz baceknd has been entirely rewritten and is now much more
efficient. The XCB backend has been updated to track the latest XCB
API (which recently had a 1.0 release).

We hope to see these backends become supported in a future release,
(once they are passing all the tests in cairo's test suite).

The experimental OS/2 backend is new in cairo 1.4 compared to cairo
1.2.

Documentation improvements
--------------------------
We have added documentation for several functions and types that
were previously undocumented, and improved documentation on other
ones.  As of this release, there remain only two undocumented
symbols: cairo_filter_t and cairo_operator_t.

[*]Thanks to everyone
---------------------
I've accounted for 41 distinct people with attributed code added to
cairo between 1.2.6 and 1.4.0, (their names are below). That's an
impressive number, but there are certainly dozens more that
contributed with testing, suggestions, clarifying questions, and
encouragement. I'm grateful for the friendships that have developed as
we have worked on cairo together. Thanks to everyone for making this
all so much fun!

Adrian Johnson, Alfred Peng, Alp Toker, Behdad Esfahbod,
Benjamin Otte, Brian Ewins, Carl Worth, Christian Biesinger,
Christopher (Monty) Montgomery, Daniel Amelang, Dan Williams,
Dave Yeo, David Turner, Emmanuel Pacaud, Eugeniy Meshcheryakov,
Frederic Crozat, Hans Breuer, Ian Osgood, Jamey Sharp, Jeff Muizelaar,
Jeff Smith, Jinghua Luo, Jonathan Watt, Joonas Pihlaja, Jorn Baayen,
Kalle Vahlman, Kjartan Maraas, Kristian Høgsberg, M Joonas Pihlaja,
Mathias Hasselmann, Mathieu Lacage, Michael Emmel, Nicholas Miell,
Pavel Roskin, Peter Weilbacher, Robert O'Callahan,
Soren Sandmann Pedersen, Stuart Parmenter, T Rowley,
Vladimir Vukicevic

Snapshot 1.3.16 (2007-03-02 Carl Worth <<EMAIL>>)
===========================================================
New API functions
-----------------
A few new public functions have been added to the cairo API since the
1.3.14 snapshot. These include a function to query the current scaled
font:

	cairo_get_scaled_font

New functions to query the reference count of all cairo objects:

	cairo_get_reference_count

	cairo_surface_get_reference_count
	cairo_pattern_get_reference_count

	cairo_font_face_get_reference_count
	cairo_scaled_font_get_reference_count

And new functions to allow the use of user_data with any cairo object,
(previously these were only available on cairo_surface_t and
cairo_font_face_t objects):

	cairo_set_user_data
	cairo_get_user_data

	cairo_pattern_set_user_data
	cairo_pattern_get_user_data

	cairo_scaled_font_set_user_data
	cairo_scaled_font_get_user_data

Usability improvement for PDF/PS/SVG generation
-----------------------------------------------
In previous versions of cairo, generating single-page output with the
cairo-pdf, cairo-ps, or cairo-svg backends required a final call to
cairo_show_page. This was often quite confusing as people would port
functional code from a non-paginated backend and be totally mystified
as to why the output was blank until they learned to add this call.

Now that call to cairo_show_page is optional, (it will be generated
implicitly if the user does not call it). So cairo_show_page is only
needed to explicitly separate multiple pages.

Greatly improved PDF output
---------------------------
We are very happy to be able to announce that cairo-generated PDF
output will now have text that can be selected, cut-and-paste, and
searched with most capable PDF viewer applications. This is something
that was not ever possible with cairo 1.2.

Also, the PDF output now has much more compact encoding of text than
before. Cairo is now much more careful to not embed multiple copies of
the same font at different sizes. It also compresses text and font
streams within the PDF output.

Major bug fixes
---------------
  • Fixed radial gradients

    The rendering of radial gradients has been greatly improved. In
    the cairo 1.2 series, there was a serious regression affecting
    radial gradients---results would be very incorrect unless one of
    the gradient circles had a radius of 0.0 and a center point within
    the other circle. These bugs have now been fixed.

  • Fixed dashing

    Several fixes have been made to the implementation of dashed
    stroking. Previously, some dashed, stroked rectangles would
    mis-render and fill half of the rectangle with a large triangular
    shape. This bug has now been fixed.

  • Fixed transformed images in PDF/PS output

    In previous versions of cairo, painting with an image-based source
    surface pattern to the PDF or PS backends would cause many kinds
    of incorrect results. One of the most common problems was that an
    image would be repeated many times even when the user had
    explicitly requested no repetition with CAIRO_EXTEND_NONE. These
    bugs have now been fixed.

  • Eliminate errors from CAIRO_EXTEND_REFLECT and CAIRO_EXTEND_PAD

    In the 1.2 version of cairo any use of CAIRO_EXTEND_REFLECT or
    CAIRO_EXTEND_PAD with a surface-based pattern resulted in an
    error, (cairo would stop rendering). This bug has now been
    fixed.

    Now, CAIRO_EXTEND_REFLECT should work properly with surface
    patterns.

    CAIRO_EXTEND_PAD is still not working correctly, but it will now
    simply behave as CAIRO_EXTEND_NONE rather than triggering the
    error.

New rewrite of quartz backend (still experimental)
--------------------------------------------------
Cairo's quartz backend has been entirely rewritten and is now much
more efficient. This backend is still marked as experimental, not
supported, but it is now much closer to becoming an officially
supported backend. (For people that used the experimental nquartz
backend in previous snapshots, that implementation has now been
renamed from "nquartz" to "quartz" and has replaced the old quartz
backend.)

Documentation improvements
--------------------------
We have added documentation for several functions and types that
were previously undocumented, and improved documentation on other
ones.  As of this release, there remain only two undocumented
symbols: cairo_filter_t and cairo_operator_t.

Other bug fixes
---------------
  • cairo-svg: Fix bug that was preventing text from appearing in many
    viewers

  • cairo-ft: Return correct metrics when hinting is off

  • Cairo 1.3.14 deadlocks in cairo_scaled_font_glyph_extents or
    _cairo_ft_unscaled_font_lock_face

    https://bugs.freedesktop.org/show_bug.cgi?id=10035

  • cairo crashes in cairo_create_similar if nil surface returned by
    other->backend->create_similar

    https://bugs.freedesktop.org/show_bug.cgi?id=9844

  • evolution crash in _cairo_gstate_backend_to_user()
    https://bugs.freedesktop.org/show_bug.cgi?id=9906

  • Fix memory leak in rectilinear stroking code

Things not in this release
--------------------------
  • Solid-surface-pattern cache: This patch had been applied during
    the 1.3.x series, but it was reverted due to some inter-thread
    problems it caused. The patch is interesting since it made a big
    benefit for text rendering performance---so we'll work to bring a
    corrected version of this patch back as soon as possible.

Snapshot 1.3.14 (2006-02-13 Carl Worth <<EMAIL>>)
===========================================================
This is the seventh development snapshot in the 1.3 series, (and there
likely won't be many more before the 1.4.0 release). It comes just
over 3 weeks after the 1.3.12 snapshot.

Since we're so close to the 1.4.0 release, there are not a lot of new
features nor even a lot of new performance improvements in this
snapshot. Instead, there are a great number of bug fixes. Some are
long-standing bugs that we're glad to say goodbye to, and several are
fixes for regressions that were introduced as part of the optimization
efforts during the 1.3.x series.

PDF text selection fixed
------------------------
The inability to correctly select text in cairo-generated PDF has been
a defect ever since the initial support for the PDF backend in the
cairo 1.2.0 release. With the 1.3.14 snapshot, in most situations, and
with most PDF viewer applications, the PDF generated by cairo will
allow text to be correctly selected for copy-and-paste, (as well as
searching).

We're very excited about this new functionality, (and very grateful to
Adrian Johnson, Behdad Esfahbod, and others that have put a lot of
work into this lately). Please test this new ability and give feedback
<NAME_EMAIL> list.

Many thread-safety issues fixed
-------------------------------
We've discovered that no release of cairo has ever provided safe text
rendering from a multi-threaded application. With the 1.3.14 snapshot
a huge number of the bugs in this area have been fixed, and multiple
application dvelopers have now reported success at writing
multi-threaded applications with cairo.

Other fixes
-----------
Fixed a bug that was causing glyph spacing to be 32 times larger than
desired when using cairo-win32.

Fixed a regression in the rendering of linear gradients that had been
present since the 1.3.8 snapshot.

Fixed several problems in cairo-atsui that were leading to assertion
failures when rendering text.

Fix corrupted results when rendering a transformed source image
surface to an xlib surface. This was a regression that had been
present since the 1.3.2 snapshot.

Fixed PDF output to prevent problems printing from some versions of
Acrobat Reader, (a single glyph was being substituted for every
glyph).

And many other fixes as well, (see the logs for details).

Snapshot 1.3.12 (2007-01-20 Carl Worth <<EMAIL>>)
===========================================================
The relentless march toward the cairo 1.4 release continues, (even if
slightly late out of the starting blocks in 2007). This is the sixth
development snapshot in the 1.3 series. It comes 4 weeks after the
1.3.10 snapshot.

Performance
-----------
As usual, this snapshot has some fun performance improvements to show
off:

image-rgba long-lines-uncropped-100  470.08 -> 4.95: 94.91x speedup
███████████████████████████████████████████████
image-rgb  long-lines-uncropped-100  461.60 -> 4.96: 93.02x speedup
██████████████████████████████████████████████

This 100x improvement, (and yes, that's 100x, not 100%), in the image
backend occurs when drawing large shapes where only a fraction of the
shape actually appears in the final result, (the rest being outside
the bounds of the destination surface). Many applications should see
speedups here, and the actual amount of speedup depends on the ratio
of non-visible to visible portions of geometry.

[Note: There remains a similar performance bug when drawing mostly
non-visible objects with the xlib backend. This is due to a similar
bug in the X server itself, but we hope a future cairo snapshot will
workaround that bug to get a similar speedup with the xlib backend.]

image-rgba       unaligned_clip-100    0.09 -> 0.06:  1.67x speedup
▍
image-rgb        unaligned_clip-100    0.09 -> 0.06:  1.66x speedup
▍

This speedup is due to further MMX optimization by Soeren Sandmann for
a case commonly hit when rendering PDF files, (and thanks to Jeff
Muizelaar for writing code to extract the test case for us).

There's another MMX optimization in this snapshot (without a fancy
speedup chart) by Dan Williams which improves compositing performance
specifically for the OLPC machine.

Thanks to Adrian Johnson, cairo's PDF output is now much more
efficient in the way it encodes text output. By reducing redundant
information and adding compression to text output streams, Adrian
achieved a ~25x improvement in the efficiency of encoding text in PDF
files, (was ~45 bytes per glyph and is now ~1.6 bytes per glyph).

Bug fixes
---------
In addition to those performance improvements, this snapshot includes
several bug fixes:

 * A huge number of bug fixes for cairo-atsui text rendering, (for mac
   OS X). These bugs affect font selection, glyph positioning, glyph
   rendering, etc. One noteworthy bug fixes is that
   cairo_select_font_face will no longer arbitrarily select bold nor
   italic when not requested, (at least not when using a standard CSS2
   font family name such as "serif", "sans-serif", "monospace", etc.).
   All these fixes are thanks to Brian Ewins who continues to do a
   great job as the new cairo-atsui maintainer.

 * Fix PDF output so that images that are scaled down no longer
   mysteriously repeat (Carl Worth).

 * Fix segfault on Mac OS X dues to attempt to measure extents of a
   zero-length string (Behdad Esfahbod).

 * Fix text extents to not include the size of initial/trailing
   non-inked characters (Behdad Esfahbod).

API tweaks
----------
Three functions have had API changes to improve consistency. Note that
the API functions being changed here are all functions that were
introduced as new functions during these 1.3.x snapshots. As always,
there will not be any API changes to functions included in a major
release (1.2.x, 1.4.x, etc.) of cairo.

The changes are as follows:

 * Rename of cairo_copy_clip_rectangles to cairo_copy_clip_rectangle_list.

 * Change cairo_get_dash_count to return an int rather than accepting a
   pointer to an int for the return value.

 * Change cairo_get_dash to have a void return type rather than
   returning cairo_status_t.

It's possible there will be one more round of changes to these
functions, (and perhaps cairo_get_color_stop as well), as we seek to
establish a unifying convention for returning lists of values.

Snapshot 1.3.10 (2006-12-23 Carl Worth <<EMAIL>>)
===========================================================
Santa Claus is coming just a little bit early this year, and he's
bringing a shiny new cairo snapshot for all the good little boys and
girls to play with.

This is the fifth development snapshot in the 1.3 series. It comes 9
days after the 1.3.8 snapshot, and still well within our goal of
having a new snapshot every week, (though don't expect one next
week---we'll all be too stuffed with sugar plums).

Speaking of sugar plums, there's a sweet treat waiting in this cairo
snapshot---greatly improved performance for stroking rectilinear
shapes, like the ever common rectangle:

image-rgb          box-outline-stroke-100 0.18 -> 0.01: 25.58x speedup
████████████████████████▋
image-rgba         box-outline-stroke-100 0.18 -> 0.01: 25.57x speedup
████████████████████████▋
xlib-rgb          box-outline-stroke-100 0.49 -> 0.06:  8.67x speedup
███████▋
xlib-rgba         box-outline-stroke-100 0.22 -> 0.04:  5.39x speedup
████▍

In past releases of cairo, some people had noticed that using
cairo_stroke to draw rectilinear shapes could be awfully slow. Many
people had worked around this by using cairo_fill with a more complex
path and gotten a 5-15x performance benefit from that.

If you're one of those people, please rip that workaround out, as now
the more natural use of cairo_stroke should be 1.2-2x faster than the
unnatural use of cairo_fill.

And if you hadn't ever implemented that workaround, then you just
might get to see your stroked rectangles now get drawn 5-25x faster.

Beyond that performance fix, there are a handful of bug fixes in this
snapshot:

 * Fix for glyph cache race condition in glitz backend (Jinghua Luo)

 * Many fixes for ATSUI text rendering (Brian Ewins)

 * Un-break recent optimization-triggered regression in rendering text
   with a translation in the font matrix (Behdad Esfahbod)

 * Fix make check to work on OPD platforms (IA64 or PPC64)
   (Frederic Crozat)

 * Fix a couple of character spacing issues on Windows
    (Jonathan Watt)

Have fun with that, everybody, and we'll be back for more in the new
year, (with a plan to add the last of our performance improvements in
this round, fix a few bad, lingering bugs, and then finish off a nice,
stable 1.4 release before the end of January).

-Carl

Snapshot 1.3.8 (2006-12-14 Carl Worth <<EMAIL>>)
==========================================================
This is the fourth development snapshot in the 1.3 series. It comes
just slightly more than one week after the 1.3.6 snapshot.

After the bug fixes in 1.3.6, we're back to our original program of
weekly snapshots, each one faster than the one from the week
before. Cairo 1.3.8 brings a 2x improvement in the speed of rendering
linear gradients (thanks to David Turner), and a significant reduction
in X traffic when rendering text (thanks to Xan Lopez and Behdad
Esfahbod), making cairo behave very much like Xft does.

A few other things in the 1.3.8 snapshot worth noting include a more
forgiving image comparator in the test suite, (using the "perceptual
diff" metric and GPL implementation by Hector Yee[*]), a bug fix for
broken linking on x86_64 (thanks to M Joonas Pihlaja) and an even
better implementation of _cairo_lround, (not faster, but supporting a
more complete input range), from Daniel Amelang.

[*] http://pdiff.sourceforge.net/

Snapshot 1.3.6 (2006-12-06 Carl Worth <<EMAIL>>)
==========================================================
This is the third development snapshot in the 1.3 series. It comes two
weeks after the 1.3.4 snapshot.

We don't have fancy performance charts this week as the primary
changes in this snapshot are bug fixes. The performance work continues
and the next snapshot (planned for one week from today) should include
several improvements. The bug fixes in this snapshot include:

 * Fix undesirable rounding in glyph positioning (Dan Amelang)

   This bug was noticed by several users, most commonly by seeing
   improper text spacing or scrambled glyphs as drawn by nautilus. For
   example:

	Update to cairo-1.3.4 worsen font rendering
	https://bugzilla.redhat.com/bugzilla/show_bug.cgi?id=217819

 * Fix reduced range of valid input coordinates to tessellator
   (M Joonas Pihlaja)

   This bug was causing lots of assertion failures in mozilla as
   mentioned here:

	CAIRO_BO_GUARD_BITS and coordinate space?
	https://lists.freedesktop.org/archives/cairo/2006-December/008743.html

 * Fix several regressions in new tessellator (M Joonas Pihlaja)

   Joonas just had a good eye for detail here. I don't think any
   external cairo users had noticed any of these bugs yet.

 * Fix compilation problems of cairo "wideint" code on some platforms
   (Mathieu Lacage)

 * Fix failed configure due to broken grep (Dan Amelang)

   This bug was reported here:

	AX_C_FLOAT_WORDS_BIGENDIAN doesn't work because grep doesn't
	work with binary file
	https://bugs.freedesktop.org/show_bug.cgi?id=9124

 * Remove the pkg-config minimum version requirement (Behdad Esfahbod)

   Some systems ship with pkg-config 0.15 and there was really no good
   reason for cairo to insist on having version 0.19 before it would
   build.

There is also one new (but inert) feature in this snapshot. There's a
new option that can be passed to cairo's configure script:

	--disable-some-floating-point

	Disable certain code paths that rely heavily on double precision
	floating-point calculation. This option can improve
	performance on systems without a double precision floating-point
	unit, but might degrade performance on those that do.

As of this snapshot, this option does not make any change to cairo,
but it is possible that future versions of cairo will respect this
option and change the implementation of various functions as
appropriate.

Snapshot 1.3.4 (2006-11-22 Carl Worth <<EMAIL>>)
==========================================================
This is the second development snapshot in the 1.3 series. It comes
one week after the 1.3.2 snapshot.

This snapshot has a couple of significant performance improvements,
and also adds new support for producing multi-page SVG output, (when
targeting SVG 1.2)---thanks to Emmanuel Pacaud. The details of the
performance improvements are as follows:

1. The long-awaited "new tessellator".

   The credit for this being an improvement goes to Joonas Pihlaja. He
   took my really slow code and really put it through its paces to get
   the dramatic performance improvement seen below (up to 38x faster
   on realistic cases, and more than 10x faster for the zrusin_another
   test).

   His own writeup of the work he did is quite thorough, but more than
   can be quoted here. Please see his post for the interesting details:

   https://lists.freedesktop.org/archives/cairo/2006-November/008483.html

   (Though note that this snapshot also includes some additional,
   significant improvements that were only sketched out in that
   email---see "Generating fewer trapezoids").

2. More floating-point improvements

   Daniel Amelang continues to work the magic he began in the 1.3.2
   snapshot. This time he short-circuits floating-point
   transformations by identity matrices and applies the earlier
   floating-to-fixed-point technique to the problem of rounding.

   The improvements here will primarily benefit text performance, and
   will benefit platforms without hardware floating-point more than
   those that have it, (some text tests show 20% improvement on an x86
   machine and closer to 80% improvement on arm).

The performance chart comparing 1.3.2 to 1.3.4 really speaks for
itself, (this is on an x86 laptop). This is quite a lot of progress
for one week:

 xlib-rgb    stroke_similar_rgba_over-256   74.99 1.45% ->   2.03 68.38%: 36.86x speedup
███████████████████████████████████▉
 xlib-rgb  stroke_similar_rgba_source-256   78.23 1.43% ->   3.30 67.05%: 23.71x speedup
██████████████████████▊
 xlib-rgba             tessellate-256-100  820.42 0.15% ->  35.06 2.84%: 23.40x speedup
██████████████████████▍
image-rgba             tessellate-256-100  819.55 0.32% ->  35.04 3.56%: 23.39x speedup
██████████████████████▍
 xlib-rgb      stroke_image_rgba_over-256   78.10 1.43% ->   4.33 65.56%: 18.04x speedup
█████████████████
 xlib-rgb    stroke_image_rgba_source-256   80.11 1.63% ->   5.75 63.99%: 13.94x speedup
█████████████
 xlib-rgba  zrusin_another_tessellate-415   89.22 0.35% ->   8.38 5.23%: 10.65x speedup
█████████▋
image-rgba  zrusin_another_tessellate-415   87.38 0.89% ->   8.37 5.22%: 10.44x speedup
█████████▍
image-rgba        zrusin_another_fill-415  117.67 1.34% ->  12.88 2.77%:  9.14x speedup
████████▏
 xlib-rgba        zrusin_another_fill-415  140.52 1.57% ->  15.79 2.88%:  8.90x speedup
███████▉
image-rgba              tessellate-64-100    9.68 3.42% ->   1.42 0.60%:  6.82x speedup
█████▉
 xlib-rgba              tessellate-64-100    9.78 4.35% ->   1.45 0.83%:  6.72x speedup
█████▊
 xlib-rgb     stroke_linear_rgba_over-256   46.01 2.44% ->   7.74 54.51%:  5.94x speedup
█████
 xlib-rgb   stroke_linear_rgba_source-256   48.09 2.15% ->   9.14 53.00%:  5.26x speedup
████▎
 xlib-rgb     stroke_radial_rgba_over-256   50.96 2.34% ->  12.46 47.99%:  4.09x speedup
███▏
 xlib-rgb   stroke_radial_rgba_source-256   53.06 1.57% ->  13.96 46.57%:  3.80x speedup
██▊
image-rgba  paint_similar_rgba_source-256    0.12 1.57% ->   0.08 9.92%:  1.42x speedup
▍
image-rgba    paint_image_rgba_source-256    0.12 2.49% ->   0.08 10.70%:  1.41x speedup
▍
image-rgba                  world_map-800  356.28 0.46% -> 275.72 1.15%:  1.29x speedup
▎
 xlib-rgba                  world_map-800  456.81 0.39% -> 357.95 1.39%:  1.28x speedup
▎
image-rgb               tessellate-16-100    0.09 0.57% ->   0.07 3.43%:  1.23x speedup
▎
image-rgba              tessellate-16-100    0.09 0.06% ->   0.07 2.46%:  1.23x speedup
▎
image-rgba        text_solid_rgb_over-256    5.39 4.01% ->   4.47 0.70%:  1.21x speedup
▎
image-rgba       text_solid_rgba_over-256    5.37 0.82% ->   4.45 0.75%:  1.21x speedup
▎
image-rgba        text_image_rgb_over-64     0.78 0.10% ->   0.65 0.74%:  1.20x speedup
▎
image-rgba       text_image_rgba_over-64     0.78 0.29% ->   0.65 0.68%:  1.19x speedup
▎
image-rgb         text_solid_rgb_over-64     0.76 2.45% ->   0.63 0.81%:  1.19x speedup
▎
image-rgba       text_solid_rgba_over-64     0.76 0.33% ->   0.64 0.66%:  1.19x speedup
▎
image-rgba     text_similar_rgba_over-256    5.99 4.72% ->   5.04 1.09%:  1.19x speedup
▎

We should point out that there is some potential for slowdown in this
snapshot. The following are the worst slowdowns reported by the cairo
performance suite when comparing 1.3.2 to 1.3.4:

image-rgba              subimage_copy-256    0.01 0.87% ->   0.01 3.61%:  1.45x slowdown
▌
 xlib-rgb        paint_solid_rgb_over-256    0.31 10.23% ->   0.38 0.33%:  1.26x slowdown
▎
image-rgba           box-outline-fill-100    0.01 0.30% ->   0.01 2.52%:  1.21x slowdown
▎
image-rgba        fill_solid_rgb_over-64     0.20 1.22% ->   0.22 1.59%:  1.12x slowdown
▏
image-rgb       fill_similar_rgb_over-64     0.21 1.04% ->   0.24 1.06%:  1.11x slowdown
▏
image-rgba        fill_image_rgb_over-64     0.21 1.19% ->   0.24 0.72%:  1.11x slowdown
▏
image-rgba      fill_similar_rgb_over-64     0.21 0.18% ->   0.24 0.30%:  1.11x slowdown
▏
image-rgb        fill_solid_rgba_over-64     0.22 1.66% ->   0.24 1.15%:  1.11x slowdown
▏
image-rgb         fill_image_rgb_over-64     0.21 0.14% ->   0.24 0.80%:  1.11x slowdown
▏
image-rgba       fill_image_rgba_over-64     0.22 1.34% ->   0.25 0.20%:  1.11x slowdown
▏
image-rgba       fill_solid_rgba_over-64     0.22 1.48% ->   0.24 0.95%:  1.11x slowdown
▏
image-rgb      fill_similar_rgba_over-64     0.22 1.13% ->   0.25 1.25%:  1.10x slowdown
▏

The 45% slowdown for subimage_copy is an extreme case. It's unlikely
to hit many applications unless they often use cairo_rectangle;
cairo_fill to copy a single pixel at a time. In any case, it shows a
worst-case impact of the overhead of the new tessellator. The other
slowdowns (~ 10%) are probably more realistic, and still very
concerning.

We will work to ensure that performance regressions like these are not
present from one major release of cairo to the next, (for example,
from 1.2 to 1.4).

But we're putting this 1.3.4 snapshot out there now, even with this
potential slowdown so that people can experiment with it. If you've
got complex geometry, we hope you will see some benefit from the new
tessellator. If you've got primarily simple geometry, we hope things
won't slowdown too much, but please let us know what slowdown you see,
if any, so we can calibrate our performance suite against real-world
impacts.

Thanks, and have fun with cairo!

Snapshot 1.3.2 (2006-11-14 Carl Worth <<EMAIL>>)
==========================================================
This is the first development snapshot since the 1.2 stable series
branched off shortly after the 1.2.4 release in August 2006.

This snapshot includes all the bug fixes from the 1.2.6 release,
(since they originated here on the 1.3 branch first and were
cherry-picked over to 1.2). But more importantly, it contains some new
API in preparation for a future 1.4 release, and most importantly, it
contains several performance improvements.

The bug fixes will not be reviewed here, as most of them are already
described in the 1.2.6 release notes. But details for the new API and
some performance improvements are included here.

As with all snapshots, this is experimental code, and the new API
added here is still experimental and is not guaranteed to appear
unchanged in any future release of cairo.

API additions
-------------
Several new API additions are available in this release. There is a
common theme among all the additions in that they allow cairo to
advertise information about its state that it was refusing to
volunteer earlier. So this isn't groundbreaking new functionality, but
it is essential for easily achieving several tasks.

The new functions can be divided into three categories:

	Getting information about the current clip region
	-------------------------------------------------
	cairo_clip_extents
	cairo_copy_clip_rectangles
	cairo_rectangle_list_destroy

	Getting information about the current dash setting
	--------------------------------------------------
	cairo_get_dash_count
	cairo_get_dash

	Getting information from a pattern
	----------------------------------
	cairo_pattern_get_rgba
	cairo_pattern_get_surface
	cairo_pattern_get_color_stop_rgba
	cairo_pattern_get_color_stop_count
	cairo_pattern_get_linear_points
	cairo_pattern_get_radial_circles

In each of these areas, we have new API for providing a list of
uniform values from cairo. The closest thing we had to this before was
cairo_copy_path, (which is rather unique in providing a list of
non-uniform data).

The copy_clip_rectangles/rectangle_list_destroy functions follow a
style similar to that of cairo_copy_path. Meanwhile, the dash and
pattern color stop functions introduce a new style in which there is a
single call to return the number of elements available (get_dash_count
and get_color_stop_count) and then a function to be called once to get
each element (get_dash and get_color_stop_rgba).

I'm interested in hearing feedback from users of these new API
functions, particularly from people writing language bindings. One
open question is whether the clip "getter" functionality should adopt
a style similar to that of the new dash and color_stop interfaces.

API deprecation
---------------
The CAIRO_FORMAT_RGB16_565 enum value has been deprecated. It never
worked as a format value for cairo_image_surface_create, and it wasn't
necessary for supporting 16-bit 565 X server visuals.

XCB backend changes
-------------------
The XCB backend has been updated to track the latest XCB API (which
recently had a 1.0 release).

New quartz backend
------------------
Vladimir Vukicevic has written a new "native quartz" backend which
will eventually replace the current "image-surface wrapping" quartz
backend. For now, both backends are available, (the old one is
"quartz" and the new one is "nquartz"). But it is anticipated that the
new backend will replace the old one and take on the "quartz" name
before this backend is marked as supported in a release of cairo.

New OS/2 backend
----------------
Doodle and Peter Weilbacher have contributed a new, experimental
backend for using cairo on OS/2 systems.

Performance improvements
------------------------
Here are some highlights from cairo's performance suite showing
improvements from cairo 1.2.6 to cairo 1.3.2. The command used to
generate this data is:

	./cairo-perf-diff 1.2.6 HEAD

available in the perf/ directory of a recent checkout of cairo's
source, (the cairo-perf-diff script does require a git checkout and
will not work from a tar file---though ./cairo-perf can still be used
to generate a single report there and ./cairo-perf-diff-files can be
used to compare two reports).

Results are described below both for an x86 laptop (with an old Radeon
video card, recent X.org build, XAA, free software drivers), as well
as for a Nokia 770. First the x86 results with comments on each, (all
times are reported in milliseconds).

Copying subsets of an image surface to an xlib surface (much faster)
--------------------------------------------------------------------
 xlib-rgba              subimage_copy-512   10.50 ->   : 53.97x speedup
█████████████████████████████████████████████████████

Thanks to Christopher (Monty) Montgomery for this big performance
improvement. Any application which has a large image surface and is
copying small pieces of it at a time to an xlib surface, (imagine an
application that loads a single image containing all the "sprites" for
that application), will benefit from this fix. The larger the ratio of
the image surface to the portion being copied, the larger the benefit.

Floating-point conversion (3x faster)
-------------------------------------
 xlib-rgba  pattern_create_radial-16    27.75 ->   3.93 :  2.94x speedup
██
image-rgb   pattern_create_radial-16    26.06 ->   3.74 :  2.90x speedup
█▉

Thanks to Daniel Amelang, (and others who had contributed the idea
earlier), for this nice improvement in the speed of converting
floating-point values to fixed-point.

Text rendering (1.3 - 2x faster)
------------------------------
 xlib-rgba text_image_rgba_source-256  319.73 ->  62.40 :  2.13x speedup
█▏
image-rgb    text_solid_rgba_over-64     2.85 ->   0.88 :  1.35x speedup
▍

I don't think we've ever set out to improve text performance
specifically, but we did it a bit anyway. I believe the extra
improvement in the xlib backend is due to Monty's image copying fix
above, and the rest is due to the floating-point conversion speedup.

Thin stroke improvements (1.5x faster)
---------------------------------------------
image-rgb               world_map-800  1641.09 -> 414.77 :  1.65x speedup
▋
 xlib-rgba              world_map-800  1939.66 -> 529.94 :  1.52x speedup
▌

The most modest stuff to announce in this release is the 50%
improvement I made in the world_map case. This is in improvement that
should help basically anything that is doing strokes with many
straight line segments, (and the thinner the better, since that makes
tessellation dominate rasterization). The fixes here are to use a
custom quadrilateral tessellator rather than the generic tessellator
for straight line segments and the miter joins.

Performance results from the Nokia 770
--------------------------------------
 xlib-rgba          subimage_copy-512     55.88 ->     2.04 : 27.34x speedup
██████████████████████████▍
 xlib-rgb     text_image_rgb_over-256   1487.58 ->   294.43 :  5.05x speedup
████
image-rgb   pattern_create_radial-16     187.13 ->    91.86 :  2.04x speedup
█
 xlib-rgba              world_map-800  21261.41 -> 15628.02 :  1.36x speedup
▍

Here we see that the subimage_copy improvement was only about half as
large as the corresponding improvement on my laptop, (27x faster
compared to 54x) and the floating-point conversion fix also was quite
as significant, (2x compared to 3x). Oddly the improvement to text
rendering performance was more than twice as good (5x compared to
2x). I don't know what the reason for that is, but I don't think it's
anything anybody should complain about.

Release 1.2.6 (2006-11-02 Behdad Esfahbod <<EMAIL>>)
==============================================================
This is the third bug fix release in the 1.2 series, coming less than
two months after the 1.2.4 release made on August 18.

The 1.2.4 release turned out to be a pretty solid one, except for a crash
bug when forwarding an X connection where the client and the server have
varying byte orders, eg. from a PPC to an i686.  Other than that, various
other small bugs have been fixed.

Various improvements have been made in the testing infrastructure to prevent
false positives, and to make sure the generated cairo shared object behaves as
expected in terms of exported symbols and relocations.

There were a total of 89 changes since 1.2.4.  The following list the most
important ones:

Common fixes
------------
- Avoid unsigned loop control variable to eliminate infinite,
  memory-scribbling loop. (#7593)
- Fix cairo_image_surface_create to report INVALID_FORMAT errors.
  Previously the detected error was being lost and a nil surface was
  returned that erroneously reported CAIRO_STATUS_NO_MEMORY.
- Change _cairo_color_compute_shorts to not rely on any particular
  floating-point epsilon value. (#7497)
- Fix infinite-join test case (bug #8379)
- Pass correct surface to create_similar in _cairo_clip_init_deep_copy().

PS/PDF fixes
------------
- Fix Type 1 embedding in PDF.
- Correct the value of /LastChar in the PDF Type 1 font dictionary.
- Improve error checking in TrueType subsetting.
- Compute right index when looking up left side bearing. (bug #8180)
- Correct an unsigned to signed conversion problem in truetype subsetting
  bbox.
- Type1 subsetting: Don't put .notdef in Encoding when there are 256 glyphs.
- Add cairo version to PS header / PDF document info dictionary.
- Set CTM before path construction.

Win32 fixes
-----------
- Get correct unhinted outlines on win32. (bug 7603)
- Make cairo as a win32 static library possible.
- Use CAIRO_FORMAT_RGB24 for BITSPIXEL==32 surfaces too.

Build system fixes
------------------
- Define WINVER if it's not defined. (bug 6456)
- Fix the AMD64 final link by removing SLIM from pixman.
- Misc win32 compilation fixes.
- Add Sun Pro C definition of pixman_private.
- Use pixman_private consistently as prefix not suffix.
- Added three tests check-plt.sh, check-def.sh, and check-header.sh that check
  that the shared object, the .def file, and the public headers agree about
  the exported symbols.
- Require pkg-config 0.19. (#8686)


Release 1.2.4 (2006-08-18 Carl Worth <<EMAIL>>)
=========================================================
This is the second bug fix release in the 1.2 series, coming less than
two weeks after the 1.2.2 release made on August 8.

The big motivation for a quick release was that there were a log of
build system snags that people ran into with the 1.2.2 release. But,
by the time we got those all done, we found that we had a bunch of
fixes for cairo's rendering as well. So there's a lot of goodness in
here for such a short time period.

Rendering fixes
---------------
Fix image surfaces to not be clipped when used as a source (Vladimir Vukicevic)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=72e25648c4c4bc82ddd938aa4e05887a293f0d8b

Fix a couple of corner cases in dashing degenerate paths (Jeff Muizelaar)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=fbb1758ba8384650157b2bbbc93d161b0c2a05f0

Fix support for type1 fonts on win32 (Adrian Johnson)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=da1019c9138695cb838a54f8b871bbfd0e8996d7

Fix assertion failure when rotating bitmap fonts (Carl Worth)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=0bfa6d4f33b8ddb5dc55bbe419c15df4af856ff9

Fix assertion failure when calling cairo_text_path with bitmap fonts (Carl Worth)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=9878a033531e6b96b5f27e69e10e90dee7440cd9

Fix mis-handling of cairo_close_path in some situations (Tim Rowley, Carl Worth)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=53f74e59faf1af78f2f0741ccf1f23aa5dad4efc

Respect font_matrix translation in _cairo_gstate_glyph_path (Behdad Esfahbod)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=f183b835b111d23e838889178aa8106ec84663b3

Fix vertical metrics adjustment to work with non-identity shapes (Behdad Esfahbod)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=b7bc263842a798d657a95e539e1693372448837f

[PS] Set correct ImageMatrix in _cairo_ps_surface_emit_bitmap_glyph_data (Behdad Esfahbod)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=d47388ad759b0a1a0869655a87d9b5eb6ae2445d

Build system fixes
------------------
Fix xlib detection to prefer pkg-config to avoid false libXt dependency (Behdad Esfahbod)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=0e78e7144353703cbd28aae6a67cd9ca261f1d68

Fix typos causing win32 build problem with PS,PDF, and SVG backends (Behdad Esfahbod)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=aea83b908d020e26732753830bb3056e6702a774

Fix configure cache to not use stale results (Behdad Esfahbod)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=6d0e3260444a2d5b6fb0cb223ac79f1c0e7b3a6e

Fix to not pass unsupported warning options to the compiler (Jens Granseuer)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=97524a8fdb899de1ae4a3e920fb7bda6d76c5571

Fix to allow env. variables such as png_REQUIRES to override configure detection (Jens Granseuer)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=abd16e47d6331bd3811c908e524b4dcb6bd23bf0

Fix test suite to not use an old system cairo when converting svg2png (Behdad Esfahbod)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=6122cc85c8f71b1ba2df3ab86907768edebe1781

Fix test suite to not require signal.h to be present (Behdad Esfahbod)
https://gitweb.freedesktop.org/?p=cairo;a=commit;h=6f8cf53b1e1ccdbe1ab6a275656b19c6e5120e40

Code cleanups
-------------
Many useful warnings cleanups from sparse, valgrind, and careful eyes
(Kjartan Maraas, Pavel Roskin)

Release 1.2.2 (2006-08-08 Carl Worth <<EMAIL>>)
=========================================================
This is the first bug fix release in the 1.2 series since the original
1.2.0 release made six weeks ago.

There were some very serious bugs in the 1.2.0 release, (see below),
so everybody is encouraged to upgrade from 1.2.0 to 1.2.2. The 1.2.2
release maintains source and binary compatibility with 1.2.0 and does
not make any API additions.

Fix crashes with BGR X servers
------------------------------
With cairo 1.2.0 many people reported problems with all cairo-using
programs, (including all GTK+ programs with GTK+ >= 2.8) immediately
crashing with a complaint about an unsupported image format. This bug
affected X servers that do not provide the Render extension and that
provide a visual with BGR rather than RGB channel order.

report:	https://bugs.freedesktop.org/show_bug.cgi?id=7294
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=9ae66174e774b57f16ad791452ed44efc2770a59

Fix the "disappearing text" bug
-------------------------------
With cairo 1.2.0 many people reported that text would disappear from
applications, sometimes reappearing with mouse motion or
selection. The text would disappear after the first space in a string
of text. This bug was caused by an underlying bug in (very common) X
servers, and only affected text rendered without antialiasing, (either
a bitmap font or a vector font with antialiasing disabled). The bug
was also exacerbated by a KDE migration bug that caused antialiasing
to be disabled more than desired.

report:	https://bugs.freedesktop.org/show_bug.cgi?id=7494
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=456cdb3058f3b416109a9600167cd8842300ae14
see also:
Xorg:	https://bugs.freedesktop.org/show_bug.cgi?id=7681
KDE:	http://qa.mandriva.com/show_bug.cgi?id=23990

Fix broken image fallback scaling (aka. "broken printing")
----------------------------------------------------------
The various "print" backends, (pdf, ps, and svg), sometimes fallback
to using image-based rendering for some operations. In cairo 1.2.0
these image fallbacks were scaled improperly. Applications using cairo
can influence the resolution of the image fallbacks with
cairo_surface_set_fallback_resolution. With the bug, any value other
than 72.0 would lead to incorrect results, (larger values would lead
to increasingly shrunken output).

report:	https://bugs.freedesktop.org/show_bug.cgi?id=7533
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=1feb4291cf7813494355459bb547eec604c54ffb

Fix inadvertent semantic change of font matrix translation (Behdad Esfahbod)
----------------------------------------------------------------------------
The 1.2.0 release introduced an inadvertent change to how the
translation components of a font matrix are interpreted. In the 1.0
series, font matrix translation could be used to offset the glyph
origin, (though glyph metrics were reported incorrectly in
1.0). However in 1.2.0, the translation was applied to the advance
values between each glyph. The 1.2.0 behavior is fairly useless in
practice, and it was not intentional to introduce a semantic
change. With 1.2.2 we return to the 1.0 semantics, with a much better
implementation that provides correct glyph metrics.

fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=84840e6bba6e72aa88fad7a0ee929e8955ba9051

Fix create_similar to preserve fallback resolution and font options (Behdad Esfahbod)
-------------------------------------------------------------------------------------
There has been a long-standing issue with cairo_surface_create_similar
such that font options and other settings from the original
destination surface would not be preserved to the intermediate
"similar" surface. This could result in incorrect rendering
(particularly with respect to text hinting/antialiasing) with
fallbacks, for example.

report:	https://bugs.freedesktop.org/show_bug.cgi?id=4106
fixes:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=9fcb3c32c1f16fe6ab913e27eb54d18b7d9a06b0
	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=bdb4e1edadb78a2118ff70b28163f8bd4317f1ec

xlib: Fix text performance regression from 1.0 to 1.2.0 (Vladimir Vukicevic)
----------------------------------------------------------------------------
Several people noticed that upgrading from cairo 1.0 to cairo 1.2.0
caused a significant performance regression when using the xlib
backend. This performance regression was particularly noticeable when
doing lots of text rendering and when using a high-latency connection
to the X server, (such as a remote X server over an ssh
connection). The slowdown was identified and fixed in 1.2.2.

report:	https://bugs.freedesktop.org/show_bug.cgi?id=7514
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=b7191885c88068dad57d68ced69a752d1162b12c

PDF: Eliminate dependency on FreeType library dependency (Adrian Johnson)
-------------------------------------------------------------------------
The cairo 1.2 series adds a supported pdf backend to cairo. In cairo
1.2.0 this backend required the freetype library, which was an
undesirable dependency on systems such as win32, (cairo is designed to
always prefer the "native" font system). As of cairo 1.2.2 the
freetype library is not required to use the pdf backend on the win32
platform.

report:	https://bugs.freedesktop.org/show_bug.cgi?id=7538
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=a0989f427be87c60415963dd6822b3c5c3781691

PDF: Fix broken output on amd64 (Adrian Johnson)
------------------------------------------------
report:	https://bugzilla.gnome.org/show_bug.cgi?id=349826
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=f4b12e497b7ac282b2f6831b8fb68deebc412e60

PS: Fix broken output for truetype fonts > 64k (Adrian Johnson)
---------------------------------------------------------------
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=067d97eb1793a6b0d0dddfbd0b54117844511a94

PDF: Fix so that dashing doesn't get stuck on (Kent Worsnop)
------------------------------------------------------------
Kent notices that with the PDF backend in cairo 1.2.0 as soon as a
stroke was performed with dashing, all subsequent strokes would also
be dashed. There was no way to turn dashing off again.

fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=778c4730a86296bf0a71080cf7008d7291792256

Fix memory leaks in failure paths in gradient creation (Alfred Peng)
--------------------------------------------------------------------
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=db06681b487873788b51a6766894fc619eb8d8f2

Fix memory leak in _cairo_surface_show_glyphs (Chris Wilson)
------------------------------------------------------------
report:	https://bugs.freedesktop.org/show_bug.cgi?id=7766
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=e2fddcccb43d06486d3680a19cfdd5a54963fcbd

Solaris: Add definition of cairo_private for some Sun compilers (Alfred Peng)
-----------------------------------------------------------------------------
report:	https://bugzilla.mozilla.org/show_bug.cgi?id=341874
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=04757a3aa8deeff3265719ebe01b021638990ec6

Solaris: Change version number of Sun's Xorg server with buggy repeat (Brian Cameron)
-------------------------------------------------------------------------------------
report: https://bugs.freedesktop.org/show_bug.cgi?id=7483
fix:	https://gitweb.freedesktop.org/?p=cairo;a=commit;h=e0ad1aa995bcec4246c0b8ab0d5a5a79871ce235

Various memory leak fixes
-------------------------
Fix memory leak in _cairo_surface_show_glyphs (bug 7766)
Fix file handle leak in failure path (bug 7616)
Fix some memory leaks in the test cases.
Fix some memory leaks in font subsetting code used in print backends.

Documentation improvements (Behdad Esfahbod)
--------------------------------------------
Added new documentation for several functions (cairo_show_page,
cairo_copy_page, cairo_in_stroke, cairo_in_fill).

Fixed some syntax errors that were preventing some existing
documentation from being published.

Fixed several minor typographical errors.

Added an index for new symbols in 1.2.

Release 1.2.0 (2006-06-27 Carl Worth <<EMAIL>>)
=========================================================
This is the culmination of the work that has gone on within the 1.1
branch of cairo.

There has been one API addition since the cairo 1.1.10 snapshot:

	cairo_xlib_surface_get_width
	cairo_xlib_surface_get_height

There's also a new feature without any API change:

	Dots can now be drawn by using CAIRO_LINE_CAP_ROUND with
	degenerate sub-paths, (cairo_move_to() followed by either
	cairo_close_path() or a cairo_line_to() to the same location).

And at least the following bugs have been fixed:

 6759  fontconfig option AntiAlias doesn't work in cairo 1.1.2
 6955  Some characters aren't displayed when using xlib (cache u...
 7268  positive device_offset values don't work as source
 * PDF emit_glyph function needs to support bitmapped glyphs
 * PS emit_glyph function needs to support bitmapped glyphs
 * SVG emit_glyph function needs to support bitmapped glyphs
 * PDF: minefield page one is falling back unnecessarily
 * PS/PDF: Fix broken placement for vertical glyphs
 * PS: Fix to not draw BUTT-capped zero-length dash segments
 * Do device offset before float->fixed conversion
   https://bugzilla.gnome.org/show_bug.cgi?id=332266
 * PS: Fix source surfaces with transformations
 * PS: Fix to not draw BUTT-capped degenerate sub-paths
 * PS: Don't walk off end of array when printing "~>"
 * Fix some memory leaks in the test suite rig
 * SVG: Fix memory leak when using cairo_mask
 * Fix EXTEND_REFLECT and EXTEND_PAD to not crash (though these are
   still not yet fully implemented for surface patterns).

This has been a tremendous effort by everyone, and I'm proud to have
been a part of it. Congratulations to all contributors to cairo!

Snapshot 1.1.10 (2006-06-16 Carl Worth <<EMAIL>>)
===========================================================
This is the fifth in a series of snapshots working toward the 1.2
release of cairo.

The primary motivation for this snapshot is to fix a long-standing bug
that had long been silent, but as of the 1.1.8 snapshot started
causing crashes when run against 16-bit depth X servers, (often Xvnc
or Xnest). The fix for this adds a new CAIRO_FORMAT_RGB16_565 to the
API.

This snapshot also includes a rewrite of cairo's SVG backend to
eliminate the dependency on libxml2. With this in place, cairo 1.2
will not depend on any libraries that cairo 1.0 did not.

As usual, there are also a few fixes for minor bugs.

Snapshot 1.1.8 (2006-06-14 Carl Worth <<EMAIL>>)
==========================================================
This is the fourth in a series of snapshots working toward the 1.2
release of cairo. At this point, all major features of the 1.2 release
are in place, leaving just a few bug fixes left.

In particular, there well be no additional API changes between this
1.1.8 snapshot and the 1.2 release.

The announcement for 1.1.6 mentioned several API changes being
considered. Only one of these changes was actually implemented
(set_dpi -> fallback_resolution). This change does introduce one
source-level incompatibility with respect to previous 1.1.x snapshots,
so see below for details.

Here is an abbreviated summary of changes since the 1.1.6 snapshot:

** API Change **
----------------
According to the plan mentioned in the 1.1.6 notes, one source-level
incompatible change has been implemented. The following three
functions have been removed from cairo's API:

	cairo_pdf_surface_set_dpi
	cairo_ps_surface_set_dpi
	cairo_svg_surface_set_dpi

and in their place the following function has been added:

	cairo_surface_set_fallback_resolution

The signature and semantics of the function remains the same, so it is
a simple matter of changing the name of the function when calling
it. As a transition mechanism, this snapshot will (on many systems)
build to include the old symbols so that code previously compiled will
still run. However, all source code using the old names must be
updated before it will compile. And the upcoming 1.2 release is not
anticipated to include the old symbols.

Finally, it should be pointed out that the old symbols never existed
in the supported API of any stable release of cairo. (In the stable
1.0 releases the PDF, PS, and SVG backends were advertised as
experimental and unstable.)

And, as always, cairo continues to maintain source and binary
compatibility between major releases. So applications compiled against
supported backends in a stable release of cairo (1.0.4 say) will
continue to compile and run without modification against new major
releases (1.2.0 say) without modification.

API additions
-------------
The following new functions have been added to cairo's API:

	cairo_surface_get_content
	cairo_debug_reset_static_data
	cairo_image_surface_get_data
	cairo_image_surface_get_format
	cairo_image_surface_get_stride
	cairo_win32_font_face_create_for_hfont

New, backend-specific pkg-config files
--------------------------------------
In addition to the original cairo.pc file, cairo will also now install
a pkg-config files for each configured backend, (for example
cairo-pdf.pc, cairo-svg.pc, cairo-xlib.pc, cairo-win32.pc, etc.) this
also includes optional font backends (such as cairo-ft.pc) and the
optional png functionality (cairo-png.pc).

These new pkg-config files should be very convenient for allowing
cairo-using code to easily check for the existing of optional
functionality in cairo without having to write complex rules to grub
through cairo header files or the compiled library looking for
symbols.

Printing backend (PS, PDF, and SVG)
-----------------------------------
Improving the quality of the "printing" backends has been a priority
of the development between cairo 1.1.6 and cairo 1.1.8.

The big improvement here is in the area of text output. Previously, at
best, text was output as paths without taking advantage of any font
support available in the output file format.

Now, at the minimum text paths will be shared by using type3 fonts
(for PS and PDF---and similarly, defs for SVG). Also, if possible,
type3 and truetype fonts will be embedded in PostScript and PDF
output. There are still some known bugs with this, (for example,
selecting text in a cairo-generated PDF file with an embedded truetype
font does not work). So there will be some more changes in this area
before cairo 1.2, but do try test this feature out as it exists so
far.

Many thanks to Kristian Høgsberg for the truetype and type1 font
embedding.

win32 backend
-------------
Performance improvements by preferring GDI over pixman rendering when possible.
Fixes for text rendering.

xlib backend
------------
Fix potentially big performance bug by making xlib's create_similar
try harder to create a pixmap of a depth matching that of the screen.

Bug fixes
---------
Among various other fixes, the following bugs listed in bugzilla have
been fixed:

    Bug 2488: Patch to fix pixman samping location bug (#2488).
    https://bugs.freedesktop.org/show_bug.cgi?id=2488

    Bug 4196: undef MIN an MAX before defining to avoid duplicate definition
    https://bugs.freedesktop.org/show_bug.cgi?id=4196

    Bug 4723: configure.in: Fix m4 quoting when examining pkg-config version
    https://bugs.freedesktop.org/show_bug.cgi?id=4723

    Bug 4882: Flag Sun's X server has having buggy_repeat.
    https://bugs.freedesktop.org/show_bug.cgi?id=4882

    Bug 5306: test/pdf2png: Add missing include of stdio.h
    https://bugs.freedesktop.org/show_bug.cgi?id=5306

    Bug 7075: Fix make clean to remove cairo.def
    https://bugs.freedesktop.org/show_bug.cgi?id=7075

(Many thanks to Behdad Esfahbod for helping us track down and fix many
of these.)

Snapshot 1.1.6 (2006-05-04 Carl Worth <<EMAIL>>)
==========================================================
This is the third in a series of snapshots working toward the imminent
1.2 release of cairo. For a list of items still needing work on the
cairo 1.2 roadmap, please see:

	https://cairographics.org/ROADMAP

As can be seen in that list, there are no longer any API additions
left on the roadmap. Instead, there is a feature (PDF type 3 fonts) a
performance optimization (X server gradients) and a list of bug
fixes. This gives us a fair amount of freedom to cut the 1.2 release
at almost any point by deciding to defer remaining bug fixes to
subsequent maintenance releases such as 1.2.2 and 1.2.4.

Before we will do that, we must first be wiling to commit to all the
new API additions. As a heads-up, there are a couple of potential API
changes being considered. (Note that these are changes to new API
introduced during 1.1 so these will not introduce API
incompatibilities compared to the stable 1.0 series). The changes
being considered are:

  cairo_get_group_target: may acquire x and y offset return
	parameters. May also be eliminated in favor of
	cairo_get_target assuming its role

  cairo_pdf_surface_set_dpi:
  cairo_ps_surface_set_dpi:
  cairo_svg_surface_set_dpi: These functions may be removed in favor
	of a new cairo_surface_set_fallback_resolution

Additionally there is the possibility of a slight change in the
semantics of cairo_set_line_width. We believe the current behavior of the sequence:

	cairo_set_line_width; ... change CTM ...; cairo_stroke;

is buggy. It is currently behaving the same as:

	... change CTM ...; cairo_set_line_width; cairo_stroke;

We are considering fixing this bug before 1.2 with the hope that
nobody is already relying on the buggy behavior described here. Do
shout if you suspect you might be in that position.

The items included in this snapshot (since the 1.1.4 snapshot) are
described below.

API additions
-------------
The long-awaited group-rendering support is now available with the
following function calls:

	cairo_push_group
	cairo_push_group_with_content
	cairo_pop_group
	cairo_pop_group_to_source
	cairo_get_group_target

This API provides a much more convenient mechanism for doing rendering
to an intermediate surface without the need to manually create a
temporary cairo_surface_t and a temporary cairo_t and clean them up
afterwards.

Add the following missing get function to complement
cairo_surface_set_device_offset:

	cairo_surface_get_device_offset

PDF backend (API addition)
--------------------------
The PDF backend now provides for per-page size changes, (similar to
what the PostScript backend got in the 1.1.4 snapshot). The new API
is:

	cairo_pdf_surface_set_size

Xlib backend (API additions)
----------------------------
The following functions have been added to allow the extraction of
Xlib surface:

	cairo_xlib_surface_get_display
	cairo_xlib_surface_get_drawable
	cairo_xlib_surface_get_screen
	cairo_xlib_surface_get_visual
	cairo_xlib_surface_get_depth

XCB backend (experimental)
--------------------------
Update backend so that it now compiles with the recent XCB 0.9 release.

Bug fixes and memory leak cleanup
---------------------------------
Various little things, nothing too significant though.

Snapshot 1.1.4 (2006-05-03 Carl Worth <<EMAIL>>)
==========================================================
This is the second in a series of snapshots working toward the
upcoming 1.2 release of cairo. For a list of items still needing work
on the cairo 1.2 roadmap, please see:

	https://cairographics.org/ROADMAP

The items included in this snapshot (since the 1.1.2 snapshot) are
described below.

PostScript backend: new printing-oriented API
---------------------------------------------
We anticipate that with cairo 1.2, toolkits will begin to use cairo
for printing on systems that use PostScript as the spool format. To
support this use case, we have added 4 new function calls that are
specific to the PostScript backend:

	cairo_ps_surface_set_size
	cairo_ps_surface_dsc_comment
	cairo_ps_surface_dsc_begin_setup
	cairo_ps_surface_dsc_begin_page_setup

These functions allow variation of the page size/orientation from one
page to the next in the PostScript output. They also allow the toolkit
to provide per-document and per-page printer control options in a
device-independent way, (for example, by using PPD options and
emitting them as DSC comments into the PostScript output). This should
allow toolkits to provide very fine-grained control of many options
available in printers, (media size, media type, tray selection, etc.).

SVG backend: builds by default, version control
-----------------------------------------------
The SVG backend continues to see major improvements. It is expected
that the SVG backend will be a supported backend in the 1.2
release. This backend will now be built by default if its dependencies
(freetype and libxml2) are met.

Additionally, the SVG backend now has flexibility with regard to what
version of SVG it targets. It will target SVG 1.1 by default, which
will require image fallbacks for some of the "fancier" cairo
compositing operators. Or with the following new function calls:

	cairo_svg_surface_restrict_to_version
	cairo_svg_get_versions
	cairo_svg_version_to_string

it can be made to target SVG 1.2 in which there is native support for
these compositing operators.

Bug fixes
---------
At least the following bugs have been fixed since the 1.1.2 snapshot:

crash at XRenderAddGlyphs
https://bugs.freedesktop.org/show_bug.cgi?id=4705

Can't build cairo-1.1.2 on opensolaris due to " void function cannot return value"
https://bugs.freedesktop.org/show_bug.cgi?id=6792

Missing out-of-memory check at gfx/cairo/cairo/src/cairo-atsui-font.c:185
https://bugzilla.mozilla.org/show_bug.cgi?id=336129

A couple of memory leaks.

Snapshot 1.1.2 (2006-04-25 Carl Worth <<EMAIL>>)
==========================================================
This is the first in a series of snapshots working toward the upcoming
1.2 release of cairo. (Subsequent snapshot will use successive even
numbers for the third digit, 1.1.4, 1.1.6, etc.) This snapshot is
backwards-compatible with the 1.0 series---it makes a few API
additions but does not remove any API.

PostScript and PDF backends are no longer "experimental"
--------------------------------------------------------
The major theme of the 1.2 release is improved PostScript and PDF
backends for cairo. Unlike the 1.0 series, in the 1.2 series these
backends will not be marked as experimental and will be enabled by
default. We encourage people to test this snapshot and the PS/PDF
backends in particular as much as possible.

The PostScript and PDF output is not yet ideal.

 * One major problem with the PostScript output is that image
   fallbacks are used more often than strictly necessary, and the
   image fallbacks are at a lower resolution than desired, (the
   cairo_ps_surface_set_dpi call is ignored).

  * The major drawback of the current PDF backend implementation is
    its text support. Every glyph is represented by a filled path in
    the PDF file. The causes file sizes to be much larger and
    rendering to be much slower than desired.

It is anticipated that both of these shortcomings will see some
improvements before the final 1.2 release.

In spite of those shortcomings, we hope that the PS and PDF backends
will yield faithful results for pretty much any cairo operations you
can throw at them. Please let us know if you are getting obviously
"different" results from the PS/PDF backends than from the image or
xlib backends.

Other new experimental backends
-------------------------------
This snapshot includes three new backends that did not exist in the
1.0 series:

	* beos backend

	* directfb backend

	* svg backend

These are all currently marked "experimental" and are disabled by
default. But the SVG backend in particular has seen a lot of recent
development and is very close to passing the entire cairo test
suite. It is possible that this backend will become a fully supported
backend by the time of the cairo 1.2 release.

Public API additions
--------------------
There have been a few new API functions added to cairo, including:

New get_type functions for querying sub-types of object:

	cairo_surface_get_type
	cairo_pattern_get_type
	cairo_font_face_get_type
	cairo_scaled_font_get_type

More convenience in working with cairo_scaled_font_t with new getter
functions:

	cairo_scaled_font_get_font_face
	cairo_scaled_font_get_font_matrix
	cairo_scaled_font_get_ctm
	cairo_scaled_font_get_font_options

As well as a convenience function for setting a scaled font into a
cairo context:

	cairo_set_scaled_font

and a function to allow text extents to be queried directly from a
scaled font, (without requiring a cairo_surface_t or a cairo_t):

	cairo_scaled_font_text_extents

These new scaled font functions were motivated by the needs of the
pango library.

Finally, a new path-construction function was added which clears the
current point in preparation for a new sub path. This makes cairo_arc
easier to use in some situations:

	cairo_new_sub_path

Before the 1.2 release is final we do still plan a few more API
additions specifically motivated by the needs of Mozilla/Firefox.

Optimizations and bug fixes
---------------------------
Shortly after the 1.0 maintenance series branched off the mainline
there was a major rework of the cairo font internals. This should
provide some good performance benefits, but it's also another area
people should look at closely for potential regressions.

There has not yet been any widespread, systematic optimization of
cairo, but various performance improvements have been made, (and some
of them are fairly significant). So if some things seem faster than
1.0 then things are good. If there are any performance regressions
compared to 1.0 then there is a real problem and we would like to hear
about that.

There has been a huge number of bug fixes---too many to mention in
detail. Again, things should be better, and never worse compared to
1.0. Please let us know if your testing shows otherwise.

Release 1.0.2 (2005-10-03 Carl Worth <<EMAIL>>)
=========================================================
For each bug number XXXX below, see:

	https://bugs.freedesktop.org/show_bug.cgi?id=XXXX

for more details.

General bug fixes
-----------------
 * 4408 - Add support for dashing of stroked curves
	  (Carl Worth)

 * 4409 - Fix dashing so that each dash is capped on both ends
	  (Carl Worth)

 * 4414 - Prevent SIGILL failures (proper use of -mmmx and -msse flags)
	  (Sebastien Bacher, Billy Biggs)

 * 4299 - Fix crashes with text display in multi-threaded program
	  (Alexey Shabalin, Carl Worth)

 * 4401 - Do not use sincos function since it is buggy on some platforms)
	  (Tim Mooney, Carl Worth)

 * 4245 - Fix several bugs in the test suite exposed by amd64 systems
	  (Seemant Kulleen, Carl Worth)

 * 4321 - Add missing byteswapping on GetImage/PutImage
	  (Sjoerd Simons, Owen Taylor)

 * 4220 - Make the check for rectangular trapezoids simpler and more accurate
	  (Richard Stellingwerff, Owen Taylor)

 * 4260 - Add missing channel-order swapping for antialised fonts
	  (Barbie LeVile, Owen Taylor)

 * 4283 - Fix compilation failure with aggressive inlining (gcc -O3)
	  (Marco Manfredini, Owen Taylor)

 * 4208 - Fix some warnings from sparse
	  (Kjartan Maraas, Billy Biggs)

 * 4269 - Fix to not crash when compiled with -fomit-frame-pointer
	  (Ronald Wahl, Owen Taylor)

 * 4263 - Improve performance for vertical gradients
	  (Richard Stellingwerff, Owen Taylor)

 * 4231
 * 4298 - Accommodate gentoo and Mandriva versions in X server vendor string check
	  (Billy Biggs, Frederic Crozat, Owen Taylor)

win32-specific fixes
--------------------
 * 4599 - Fix "missing wedges" on some stroked paths (win32)
	  (Tim Rowley, Jonathan Watt, Bertram Felgenhauer, Carl Worth, Keith Packard)

 * 4612 - Fix disappearing text if first character out of surface (win32)
	  (Tim Rowley)

 * 4602 - Fix shutdown of cairo from failing intermediate, size-0 bitmaps (win32)
	  Aka. the "white rectangles" bug from mozilla-svg testing
	  (Tim Rowley)

 * Various portability improvements for win32
	  (Hans Breuer, Owen Taylor, Carl Worth)

 * 4593 - Fix font sizes to match user expectations (win32)
	  (Tor Lillqvist, Owen Taylor)

 * 3927 - Fix to report metrics of size 0 for glyph-not-available (win32)
	  (Hans Breuer, Owen Taylor, Tor Lillqvist)

 * Add locking primitives for win32
	  (Hans Breuer)

xlib-specific fixes
-------------------
 * Fix crash from size-0 pixmap due to empty clip region (xlib)
	  (Radek Doulík, Carl Worth)

Release 1.0.0 (2005-08-24 Carl Worth <<EMAIL>>)
=========================================================
Experimental backends
---------------------
 * The Glitz, PS, PDF, Quartz, and XCB backends have been declared
   experimental, and are not part of the API guarantees that accompany
   this release. They are not built by default, even when the required
   libraries are available, and must be enabled explicitly with
   --enable-ps, --enable-pdf, --enable-quartz or --enable-xcb.

   It is very painful for us to be pushing out a major release without
   these backends enabled. There has been a tremendous amount of work
   put into each one and all are quite functional to some
   extent. However, each also has some limitations. And none of these
   backends have been tested to the level of completeness and
   correctness that we expect from cairo backends.

   We do encourage people to experiment with these backends and report
   success, failure, or means of improving them.

Operator behavior
-----------------
 * Prior to 0.9.0 the SOURCE, CLEAR and a number of other operators
   behaved in an inconsistent and buggy fashion and could affect areas
   outside the clip mask. In 0.9.0, these six "unbounded" operators
   were fixed to consistently clear areas outside the shape but within
   the clip mask.  This is useful behavior for an operator such as IN,
   but not what was expected for SOURCE and CLEAR. So, in this release
   the behavior of SOURCE and CLEAR has been changed again. They now
   affect areas only within both the source and shape. We can write
   the new operators as:

     SOURCE: dest' = (mask IN clip) ? source : dest
     CLEAR:  dest' = (mask IN clip) ? 0 : dest

Behavior and API changes
------------------------
 * Setting the filter on a gradient pattern would change the
   interpolation between color stops away from the normal linear
   interpolation. This dubious behavior has been removed.

 * The CAIRO_CONTENT_VALID() and CAIRO_FORMAT_VALID() macros --
   implementation details that leaked into cairo.h -- have been moved
   into an internal header.

 * The cairo_show_text function now advances the current point
   according to the total advance values of the string.

API additions
-------------
 * cairo_set_dash can now detect error and can set
   CAIRO_STATUS_INVALID_DASH.

Features
--------
 * When compiled against recent versions of fontconfig and FreeType,
   artificial bold fonts can now be turned on from fonts.conf using
   the FC_EMBOLDEN fontconfig key.

Optimization
------------
 * The compositing code from the 'xserver' code tree has now been
   completely merged into libpixman. This includes MMX optimization of
   common operations.

 * The image transformation code in libpixman has been improved and
   now performs significantly faster.

Bug fixes
---------
 * Several crashes related to corruption in the font caches have been
   fixed.

 * All test cases now match pixel-for-pixel on x86 and PPC; this
   required fixing bugs in the compositing, stroking, and pattern
   rendering code.

 * Negative dash offsets have been fixed to work correctly.

 * The stroking of paths with multiple subpaths has now been fixed to
   apply caps to all subpaths rather than just the last one.

 * Many build fixes for better portability on various systems.

 * Lots of other bug fixes, but we're too tired to describe them in
   more detail here.

Release 0.9.2 (2005-08-13 Carl Worth <<EMAIL>>)
=========================================================
Release numbering
-----------------
 * You will notice that this release jumped from 0.9.0 to 0.9.2. We've
   decided to use an odd micro version number (eg. 0.9.1) to indicate
   in-progress development between releases. As soon as 0.9.2 is
   tagged, the version will be incremented in CVS to 0.9.3 where it
   will stay until just before 0.9.4 is built, uploaded, and tagged.

   So, even-micro == a released version, odd-micro == something in-between.

Libpixman dependency dropped
----------------------------
 * As of this release, the dependency on an external libpixman has
   been dropped. Instead, the code from libpixman needed for cairo has
   been incorporated into the cairo source tree. The motivation for
   this change is that while cairo's API is stable and ready to be
   maintained after the 1.0 release, libpixman's API is not, so we do
   not want to expose it at this time.

   Also, the incorporation of libpixman into cairo also renames all
   previously-public libpixman symbols in order to avoid any conflict
   with a future release of libpixman

API additions
-------------
 * Macros and functions have been added so that the version of cairo
   can be queried at either compile-time or at run-time. The version
   is made available as both a human-readable string and as a single
   integer:

	CAIRO_VERSION_STRING		   eg. "0.9.2"
	CAIRO_VERSION			   eg. 000902

	const char*
	cairo_version_string (void);	/* eg. "0.9.2" */

	int
	cairo_version (void);		/* eg. 000902 */

   A macro is provided to convert a three-part component version into
   the encoded single-integer form:

	CAIRO_VERSION_ENCODE(X,Y,Z)

   For example, the CAIRO_VERSION value of 000902 is obtained as
   CAIRO_VERSION_ENCODE(0,9,2). The intent is to make version
   comparisons easy, either at compile-time:

	#if CAIRO_VERSION >= CAIRO_VERSION_ENCODE(0,9,2)
	...
	#endif

   Or at run-time:

	if (cairo_version() >= CAIRO_VERSION_ENCODE(0,9,2)) { /* ... */ }

Thread safety
-------------
 * This release adds pthread-based locking (when available) to make
   the caches used by cairo safe for threaded programs. Some may
   remember a failed experiment with this locking between the 0.5.1
   and 0.5.2 snapshots, (where even single-threaded programs that
   linked with -lpthread would deadlock). We believe that that problem
   has been fixed, so we are looking forward to testing and reports
   from users with threaded applications.

Bug fixes
---------
 * The XCB and Quartz backends failed to compiled in the 0.9.0 release
   due to minor syntax errors. These have now been fixed.

 * Various crashes in glitz and pixman due to size 0 glyphs have been
   fixed.

Release 0.9.0 (2005-08-08 Carl Worth <<EMAIL>>)
=========================================================
Soname change
-------------
 * In all prior snapshots, the libtool library versioning was set to
   1:0:0. As this release is intended to mark the beginning of
   backwards-compatible releases, the versioning has been incremented
   to 2:0:0. You will notice that the numeric extension on the
   installed library filename will change similarly.

   This change will also require all cairo-using applications to be
   recompiled. We recognize that this may cause some frustration since
   this release is backwards-compatible with 0.6.0 and in that sense
   "shouldn't" require re-compilation. However, since all historical
   snapshots have used the same 1:0:0 version in spite of incompatible
   API changes between them, it was essential that the upcoming 1.0
   release series have distinct library versioning.

   All future releases will use the library versioning to properly
   indicate compatibility between releases. So, any application
   re-compiled now to work with the 0.9.0 will not need to be
   recompiled when a compatible 1.0 release of cairo is made in the
   future.

API additions
-------------
 * Add new function calls to set/get the current antialiasing mode in
   the graphics state:

	cairo_set_antialias
	cairo_get_antialias

   This call accepts the same modes recently added for font options
   (NONE or GRAY) but affects the rendering of geometry other than
   text. The intent of this call is to enable more precise control of
   which pixels are affected by each operation, for example to allow
   for full-scene antialiasing for seam-free rendering. It is not
   expected that non-antialiased rendering will perform better than
   anti-aliased rendering.

 * Three new functions were added to provide support for mixed cairo-
   and non-cairo drawing to the same surface:

	cairo_surface_mark_dirty
	cairo_surface_mark_dirty_rectangle
	cairo_surface_flush

 * The return type of the several "reference" functions was change,
   (API compatibly), from void to the same type as the argument. The
   affected functions are:

	cairo_font_face_reference
	cairo_scaled_font_reference
	cairo_pattern_reference
	cairo_surface_reference
	cairo_reference

   This allows a convenient way to assign and reference in a single
   statement.

Semantic changes
----------------
 * The behavior of cairo_set_source with a pattern with a non-identity
   matrix was previously not well-defined. The new behavior is as
   follows:

	The pattern's transformation matrix will be locked to the
	user space in effect at the time of cairo_set_source(). This means
	that further modifications of the CTM will not affect the source
	pattern.

cairo-win32
-----------
 * Some portability improvements, (eg. workaround for missing stdint.h).

cairo-ft
--------
 * Updated to allow compilation with older versions of freetype.

Bug fixes
---------
 * Fix the unbounded operators to actually produce a correct result,
   (previously the results were artificially restricted to the
   bounding box of whatever shape was being drawn rather than
   extending out infinitely). The fixed operators are:

	CAIRO_OPERATOR_CLEAR
	CAIRO_OPERATOR_SOURCE
	CAIRO_OPERATOR_OUT
	CAIRO_OPERATOR_IN
	CAIRO_OPERATOR_DEST_IN
	CAIRO_OPERATOR_DEST_ATOP

 * Fix cairo_mask and cairo_mask_surface to transform the mask by the
   current transformation matrix (CTM).

 * Fix cairo_set_source to lock the CTM used to transform the pattern.

 * Workaround for X server Render bug involving repeating patterns
   with a general transformation matrix.

 * cairo_get_font_face fixed to return a "nil" font face object rather
   than NULL on error.

 * cairo_set_font_face fixed to not crash if given a NULL font face,
   (which is the documented interface for restoring the default font
   face).

 * Fix xlib glyphset caching to not try to free a NULL glyph.

Snapshot 0.6.0 (2005-07-28 Carl Worth <<EMAIL>>)
==========================================================
API changes
-----------
* The prototypes of the following functions have changed:

	cairo_xlib_surface_create_with_xrender_format
	cairo_xlib_surface_create_for_bitmap

  A Screen* parameter has been added to each. This allows the cairo
  xlib backend to work correctly with multi-head X servers.

* The following function has been modified:

	cairo_scaled_font_create

  to accept a cairo_font_options_t*. See below fore more details.

* All opaque, reference-counted cairo objects have now been moved to a
  standard error-handling scheme. The new objects to receive this
  treatment are cairo_font_face_t, cairo_scaled_font_t, and
  cairo_surface_t. (Previous snapshots already provided this scheme
  for cairo_t, cairo_path_t, and cairo_pattern_t.)

  This changes two functions to have a return type of void rather than
  cairo_status_t:

	cairo_scaled_font_extent
	cairo_surface_finish

  And significantly, none of the create functions for any of the
  objects listed above will return NULL. The pointer returned from any
  function will now always be a valid pointer and should always be
  passed to the corresponding destroy function when finished

  The simplest strategy for porting code is to switch from:

	object = cairo_<object>_create ();
	if (object == NULL)
	    goto BAILOUT;

	/* act on object */

	cairo_<object>_destroy (object);

  to:

	object = cairo_<object>_create ();
	if (cairo_<object>_status (object))
	    goto BAILOUT;

	/* act on object */

	cairo_<object>_destroy (object);

   But significantly, it is not required to check for an error status
   before the "act on object" portions of the code above. All
   operations on an object with an error status are, by definition,
   no-ops without side effect. So new code might be written in an
   easier-to-read style of:

	object = cairo_<object>_create ();

	/* act on object */

	cairo_<object>_destroy (object);

   with cairo_<object>_status checks placed only at strategic
   locations. For example, passing an error object to another object,
   (eg. cairo_set_source with an in-error pattern), will propagate the
   error to the subsequent object (eg. the cairo_t). This means that
   error checking can often be deferred even beyond the destruction of
   a temporary object.

API additions
-------------
* New functions for checking the status of objects that have been
  switched to the common error-handling scheme:

	cairo_font_face_status
	cairo_scaled_font_status
	cairo_surface_status

* The _cairo_error function which was added in 0.5.1 has now been made
  much more useful. In 0.5.1 only errors on cairo_t objects passed
  through _cairo_error. Now, an error on any object should pass
  through _cairo_error making it much more reliable as a debugging
  mechanism for finding when an error first occurs.

* Added new font options support with a myriad of functions:

	cairo_font_options_create
	cairo_font_options_copy
	cairo_font_options_destroy

	cairo_font_options_status

	cairo_font_options_merge
	cairo_font_options_equal
	cairo_font_options_hash

	cairo_font_options_set_antialias
	cairo_font_options_get_antialias
	cairo_font_options_set_subpixel_order
	cairo_font_options_get_subpixel_order
	cairo_font_options_set_hint_style
	cairo_font_options_get_hint_style
	cairo_font_options_set_hint_metrics
	cairo_font_options_get_hint_metrics

	cairo_surface_get_font_options

	cairo_ft_font_options_substitute

	cairo_set_font_options
	cairo_get_font_options

   This new font options support allows the application to have much
   more fine-grained control over how fonts are rendered.
   Significantly, it also allows surface backends to have some
   influence over the process. For example, the xlib backend now
   queries existing Xft properties to set font option defaults.

* New function:

	cairo_xlib_surface_set_drawable

  which allows the target drawable for an xlib cairo_surface_t to be
  changed to another with the same format, screen, and display. This
  is necessary in certain double-buffering techniques.

New features
------------
* Sub-pixel text antialiasing is now supported.

Bug fixes
---------
* Fixed assertion failure in cairo_surface_create_similar when
  application commits an error by passing a cairo_format_t rather than
  a cairo_content_t.

* Avoid division by zero in various places (cairo-ft).

* Fix infinite loop when using non-default visuals (cairo-xlib).

* Eliminate segfault in cairo_image_surface_create_from_png_stream.

* Prevent errant sign-extension of masks on 64-bit architectures
  (cairo-xlib and cairo-xcb).

* Other miscellaneous fixes.

Snapshot 0.5.2 (2005-07-18 Carl Worth <<EMAIL>>)
==========================================================
API changes
-----------
* New functions for creating patterns of a single color:

	cairo_pattern_create_rgb
	cairo_pattern_create_rgba

* Change cairo_surface_create_similar to accept a new type of
  cairo_content_t rather than cairo_format_t:

	typedef enum _cairo_content {
	    CAIRO_CONTENT_COLOR		= 0x1000,
	    CAIRO_CONTENT_ALPHA		= 0x2000,
	    CAIRO_CONTENT_COLOR_ALPHA	= 0x3000
	} cairo_content_t;

* Add new CAIRO_FORMAT_VALID and CAIRO_CONTENT_VALID macros.

* Remove unused status value:

	CAIRO_STATUS_NO_TARGET_SURFACE

* Add new status values:

	CAIRO_STATUS_INVALID_STATUS

* Require libpixman >= 0.1.5 (for necessary bug fixes)

Bug fixes
---------
* Fix cairo_surface_write_to_png for RGB24 images.

* Fix broken metrics and rendering for bitmap fonts. Add mostly
  useless bitmap glyph transformation.

* Fix glyph caches to not eject entries that might be immediately
  needed, (fixing intermittent crashes when rendering text).

* Fix all memory leaks found by running "make check-valgrind".

ATSUI backend changes
---------------------
* Allow building against < 10.3 SDK.

* Prevent crash on empty strings.

Glitz backend changes
---------------------
* Require glitz >= 0.4.4.

* Use frame buffer objects instead of pbuffers for accelerated
  offscreen drawing.

* Minor improvement to gradient pattern creation.

PostScript backend fixes
------------------------
* Rewrite of the PS backend to generate more interesting output that
  the old big-image implementation.

Win32 backend fixes
-------------------
* Implement glyph path support.

* Fix swap of blue and green values in the fill_rectangles path.

Xlib backend fixes
------------------
* Add optimization to use XCopyArea rather than XRenderComposite when
  transforming only with an integer translation, and using SOURCE
  operator or OVER with a source pattern without alpha.

Snapshot 0.5.1 (2005-06-20 Carl Worth <<EMAIL>>)
==========================================================
API changes
-----------
* Removed cairo_status_string(cairo_t*) and add
  cairo_status_to_string(cairo_status_t) in its place. Code using
  cairo_status_string can be ported forward as follows:

	cairo_status (cr);
	->
	cairo_status_to_string (cairo_status (cr));

* Removed the BAD_NESTING restriction which means that two different
  cairo_t objects can now interleave drawing to the same
  cairo_surface_t without causing an error.

* The following functions which previously had a return type of
  cairo_status_t now have a return type of void:

	cairo_pattern_add_color_stop_rgba
	cairo_pattern_set_matrix
	cairo_pattern_get_matrix
	cairo_pattern_set_extend
	cairo_pattern_set_filter

  See discussion of cairo_pattern_status below for more details.

API additions
-------------
* Improved error handling:

	cairo_status_t
	cairo_pattern_status (cairo_pattern_t *pattern);

  This snapshot expands the status-based error handling scheme from
  cairo_t to cairo_path_t and cairo_pattern_t. It also expands the
  scheme so that object-creating functions, (cairo_create,
  cairo_pattern_create_*, cairo_copy_path_*), are now guaranteed to
  not return NULL. Instead, in the case of out-of-memory these
  functions will return a static object with
  status==CAIRO_STATUS_NO_MEMORY. The status can be checked with the
  functions cairo_status and cairo_pattern_status, or by direct
  inspection of the new status field in cairo_path_t.

  Please note that some objects, including cairo_surface_t and all of
  the font-related objects have not been converted to this
  error-handling scheme.

* In addition to the above changes, a new private function has been added:

	_cairo_error

  This function can be used to set a breakpoint in a debugger to make
  it easier to find programming error in cairo-using code. (Currently,
  _cairo_error is called when any error is detected within a cairo_t
  context, but is not called for non-cairo_t errors such as for
  cairo_path_t and cairo_pattern_t).

* Fixed cairo_path_data_t so that its enum is visible to C++ code, (as
  cairo_path_data_type_t).

Performance improvements
------------------------
* Made a minor performance improvement for clipping, (restrict clip
  surface to the new intersected bounds).

* Optimize rendering of a solid source pattern with a pixel-aligned
  rectangular path to use backend clipping rather than rasterization
  and backend compositing.

* Optimize cairo_paint_with_alpha to defer to cairo_paint when alpha
  is 1.0.

Bug fixes
---------
* Fixed memory leak in cairo_copy_path.

* A build fix for non-srcdir builds.

PDF backend fixes
-----------------
* New support for path-based clipping.

* Fix for text rotated to angles other than multiples of π/2.

Win32 backend fixes
-------------------
* Fix for text extents.

Xlib backend
------------
* Implemented a complex workaround for X server bug[*] related to
  Render-based compositing with untransformed, repeating source
  pictures. The workaround uses core Xlib when possible for
  performance, (ie. with CAIRO_OPERATOR_SOURCE or CAIRO_OPERATOR_OVER
  with an opaque source surface), and falls back to the pixman
  image-based compositing otherwise.

  [*] https://bugs.freedesktop.org/show_bug.cgi?id=3566

* Various bug fixes, particularly in the fallback paths.

Snapshot 0.5.0 (2005-05-17 Carl Worth <<EMAIL>>)
==========================================================
This is a pretty big, and fairly significant snapshot.  It represents
between 2 and 3 months of solid work from a lot of people on improving
the API as much as possible. I'd like to express my appreciation and
congratulations to everyone who has worked on the big API Shakeup,
(whether in email battles over names, or fixing my silly bugs).

This snapshot will require some effort on the part of users, since
there are a _lot_ of API changes (ie. no cairo program ever written is
safe --- they're all broken now in at least one way). But, in spite of
that, we do encourage everyone to move their code to this snapshot as
soon as possible. And we're doing everything we can think of to make
the transition as smooth as possible.

The idea behind 0.5 is that we've tried to make every good API change
we could want now, and get them all done with. That is, between now
and the 1.0 release of cairo, we expect very few new API changes,
(though some will certainly sneak in). We will have some significant
additions, but the pain of moving code from cairo 0.4 to cairo 0.5
should be a one time experience, and things should be much smoother as
we continue to move toward cairo 1.0.

And with so many changes coming out for the first time in this 0.5
release, we really do need a lot of people trying this out to make
sure the ideas are solid before we freeze the API in preparation for
the 1.0 release.

OK, enough introduction. Here is a (not-quite-complete) description of
the API removals, changes and additions in this snapshot, (compared to
0.4.0)

API removals
============
The following public functions have been removed:

- cairo_set_target_*

	This is a big change. See the description of cairo_create in
	the API changes section for how to deal with this.

- cairo_set_alpha

	Alpha blending hasn't gone away; there's just a much more
	unified rendering model now. Almost all uses of
	cairo_set_alpha will be trivially replaced with
	cairo_set_source_rgba and a few others will be replaced just
	as easily with cairo_paint_with_alpha.

- cairo_show_surface

	Another useful function that we realized was muddling up the
	rendering model. The replacement is quite easy:
	cairo_set_source_surface and cairo_paint.

- cairo_matrix_create
- cairo_matrix_destroy
- cairo_matrix_copy
- cairo_matrix_get_affine

	These functions supported an opaque cairo_matrix_t. We now
	have an exposed cairo_matrix_t structure, so these can be
	dropped.

- cairo_surface_set_repeat
- cairo_surface_set_matrix
- cairo_surface_set_filter

	These properties don't belong on surfaces. If you were using
	them, you'll just want to instead use
	cairo_pattern_create_for_surface and then set these properties
	on the pattern.

- cairo_copy

	This was a confusing function and hopefully nobody will miss
	it. But if you really don't find cairo_save/restore adequate,
	let us know and we have another idea for a potential
	replacement.

And while we're on the subject of removals, we carefully tightened up
the cairo header files so they no longer gratuitously include header
files that are not strictly necessary, (stdio.h, stdint.h, pixman.h,
Xrender.h, etc. and their dependencies). This may lead to some
surprising errors, so keep your eyes open for that.

API changes
===========
Here are some of the API changes that have occurred:

~ cairo_create(void) -> cairo_create(cairo_surface_t *)

	This is the big change that breaks every program. The ability
	to re-target a cairo_t was not particularly useful, but it did
	introduce a lot of muddy semantic questions. To eliminate
	that, cairo_create now requires its target surface to be
	passed in at creation time. This isn't too hard to cope with
	as the typical first operation after cairo_create was often
	cairo_set_target_foo. So the order of those two swap and the
	application instead has cairo_foo_surface_create, then
	cairo_create.

~ cairo_current_* -> cairo_get_*

	We had a strange mixture of cairo_get and cairo_current
	functions. They've all been standardized on cairo_get, (though
	note one is cairo_get_current_point).

~ CAIRO_OPERATOR_SRC -> CAIRO_OPERATOR_SOURCE
~ CAIRO_OPERATOR_OVER_REVERSE -> CAIRO_OPERATOR_DEST_OVER

	Many of the cairo_operator_t symbolic values were renamed to
	reduce the amount of abbreviation. The confusing "OP_REVERSE"
	naming was also changed to use "DEST_OP" instead which is
	easier to read and has wider acceptance in other
	libraries/languages.

~ cairo_set_pattern -> cairo_set_source
~ cairo_set_rgb_color -> cairo_set_source_rgb

	All of the various functions that changed the source
	color/pattern were unified to use cairo_set_source names to
	make the relation more clear.

~ cairo_transform_point		   -> cairo_user_to_device
~ cairo_transform_distance	   -> cairo_user_to_device_distance
~ cairo_inverse_transform_point	   -> cairo_device_to_user
~ cairo_inverse_transform_distance -> cairo_device_to_user_distance

	These names just seemed a lot more clear.

~ cairo_init_clip	-> cairo_reset_clip
~ cairo_concat_matrix	-> cairo_transform

	More abbreviation elimination

~ cairo_current_path	  -> cairo_copy_path
~ cairo_current_path_flat -> cairo_copy_path_flat

	The former mechanism for examining the current path was a
	function that required 3 or 4 callbacks. This was more
	complexity than warranted in most situations. The new
	cairo_copy_path function copies the current path into an
	exposed data structure, and the documentation provides a
	convenient idiom for navigating the path data.

API additions
-------------
+ cairo_paint

	A generalized version of the painting operators cairo_stroke
	and cairo_fill. The cairo_paint call applies the source paint
	everywhere within the current clip region. Very useful for
	clearing a surface to a solid color, or painting an image,
	(see cairo_set_source_surface).

+ cairo_paint_with_alpha

	Like cairo_paint but applying some alpha to the source,
	(making the source paint translucent, eg. to blend an image on
	top of another).

+ cairo_mask

	A more generalized version of cairo_paint_with_alpha which
	allows a pattern to specify the amount of translucence at each
	point rather than using a constant value everywhere.

+ cairo_mask_surface

	A convenience function on cairo_mask for when the mask pattern
	is already contained within a surface.

+ cairo_surface_set_user_data
+ cairo_surface_get_user_data
+ cairo_font_face_set_user_data
+ cairo_font_face_get_user_data

	Associate arbitrary data with a surface or font face for later
	retrieval. Get notified when a surface or font face object is
	destroyed.

+ cairo_surface_finish

	Allows the user to instruct cairo to finish all of its
	operations for a given surface. This provides a safe point for
	doing things such as flushing and closing files that the
	surface may have had open for writing.

+ cairo_fill_preserve
+ cairo_stroke_preserve
+ cairo_clip_preserve

	One interesting change in cairo is that the path is no longer
	part of the graphics state managed by
	cairo_save/restore. This allows functions to construct paths
	without interfering with the graphics state. But it prevents
	the traditional idiom for fill-and-stroke:

		cairo_save; cairo_fill; cairo_restore; cairo_stroke

	Instead we know have alternate versions cairo cairo_fill,
	cairo_stroke, and cairo_clip that preserve the current path
	rather than consuming it. So the idiom now becomes simply:

		cairo_fill_preserve; cairo_stroke

+ cairo_surface_write_to_png
+ cairo_surface_write_to_png_stream

	In place of a single PNG backend, now a surface created
	through any backend (except PDF currently) can be written out
	to a PNG image.

+ cairo_image_surface_create_from_png
+ cairo_image_surface_create_from_png_stream

	And its just as easy to load a PNG image into a surface as well.

+ cairo_append_path

	With the new, exposed path data structure, it's now possible
	to append bulk path data to the current path, (rather than
	issuing a long sequence of cairo_move_to/line_to/curve_to
	function calls).

Xlib and XCB backends
---------------------

Any cairo_format_t and Colormap arguments have been dropped from
cairo_xlib_surface_create. There are also two new
cairo_xlib|xcb_surface_create functions:

	cairo_xlib|xcb_surface_create_for_bitmap
		(Particular for creating A1 surfaces)
	cairo_xlib|xcb_surface_create_with_xrender_format
		(For any other surface types, not described by a Visual*)

All of these surface create functions now accept width and height. In
addition, there are new cairo_xlib|xcb_surface_set_size functions
which must be called each time a window that is underlying a surface
changes size.

Print backends (PS and PDF)
---------------------------
The old FILE* based interfaces have been eliminated. In their place we
have two different functions. One accepts a simple const char
*filename. The other is a more general function which accepts a
callback write function and a void* closure. This should allow the
flexibility needed to hook up with various stream object in many
languages.

In addition, when specifying the surface size during construction, the
units are now device-space units (ie. points) rather than inches. This
provides consistency with all the other surface types and also makes
it much easier to reason about the size of the surface when drawing to
it with the default identity matrix.

Finally, the DPI parameters, which are only needed to control the
quality of fallbacks, have been made optional. Nothing is required
during surface_create (300 DPI is assumed) and
cairo_ps|pdf_surface_set_dpi can be used to set alternate values if
needed.

Font system
-----------
Owen very graciously listened to feedback after the big font rework he
had done for 0.4, and came up with way to improve it even more. In 0.4
there was a cairo_font_t that was always pre-scaled. Now, there is an
unscaled cairo_font_face_t which is easier to construct, (eg. no
scaling matrix required) and work with, (it can be scaled and
transformed after being set on the graphics state). And the font size
manipulation functions are much easier. You can set an explicit size
and read/modify/write the font matrix with:

	cairo_set_font_size
	cairo_get_font_matrix
	cairo_set_font_matrix

(Previously you could only multiply in a scale factor or a matrix.) A
pleasant side effect is that we can (and do) now have a default font
size that is reasonable, as opposed to the old default height of one
device-space unit which was useless until scaled.

Of course, the old pre-scaled font had allowed some performance
benefits when getting many metrics for a font. Those benefits are
still made available through the new cairo_scaled_font_t. And a
cairo_font_face_t can be "promoted" to a cairo_scaled_font_t by
suppling a font_matrix and the desired CTM.

Quartz backend
--------------
Tim Rowley put in the work to bring the Quartz backend back after it
had been disabled in the 0.4.0 snapshot. He was not able to bring back
the function that allows one to create a cairo_font_t from an ATSUI
style:

	cairo_font_t *
	cairo_atsui_font_create (ATSUStyle style);

because he didn't have a test case for it. If you care about this
function, please provide a fairly minimal test and we'll try to bring
it back in an upcoming snapshot.

Snapshot 0.4.0 (2005-03-08 Carl Worth <<EMAIL>>)
==========================================================
New documentation
-----------------
Owen Taylor has converted cairo's documentation system to gtk-doc and
has begun some long-needed work on the documentation, which can now be
viewed online here:

	https://cairographics.org/manual/

New backend: win32
------------------
This is the first snapshot to include a functional win32 backend,
(thanks to Owen Taylor). The interface is as follows:

	#include <cairo-win32.h>

	void
	cairo_set_target_win32 (cairo_t *cr,
				HDC      hdc);

	cairo_surface_t *
	cairo_win32_surface_create (HDC hdc);

	cairo_font_t *
	cairo_win32_font_create_for_logfontw (LOGFONTW       *logfont,
					      cairo_matrix_t *scale);

	cairo_status_t
	cairo_win32_font_select_font (cairo_font_t *font,
				      HDC           hdc);

	void
	cairo_win32_font_done_font (cairo_font_t *font);

	double
	cairo_win32_font_get_scale_factor (cairo_font_t *font);

And see also the documentation at:

https://cairographics.org/manual/cairo-Microsoft-Windows-Backend.html

Disabled backend: quartz
------------------------
Unfortunately, the quartz backend code is currently out of date with
respect to some recent backend interface changes. So, the quartz
backend is disabled in this snapshot.

If the quartz backend is brought up-to-date before the next snapshot,
we would be glad to make a 0.4.1 snapshot that re-enables it, (we do
not expect many more big backend interface changes).

API Changes
-----------
The font system has been revamped, (as Owen Taylor's work with
integrating pango and cairo gave us the first serious usage of the
non-toy font API).

One fundamental, user-visible change is that the cairo_font_t object
now represents a font that is scaled to a particular device
resolution. Further changes are described below.

 cairo.h
 -------
 Removed cairo_font_set_transform and cairo_font_current_transform.

 Added cairo_font_extents and cairo_font_glyph_extents. See
 documentation for details:

 https://cairographics.org/manual/cairo-cairo-t.html#cairo-font-extents

 cairo-ft.h
 ----------
 The cairo_ft_font API changed considerably. Please see the
 documentation for details:

 https://cairographics.org/manual/cairo-FreeType-Fonts.html

Performance
-----------
Make the fast-path clipping (pixel-aligned rectangles) faster.

Add optimization for applying a constant alpha to a pattern.

Optimize gradients that are horizontal or vertical in device space.

Xlib: When RENDER is not available, use image surfaces for
intermediate surfaces rather than xlib surfaces.

Backend-specific changes
------------------------
 Glitz
 -----
 Major update to glitz backend. The output quality should now be just
 as good as the image and xlib backends.

 Track changes to glitz 0.4.0.

 PDF
 ---
 Various improvements to produce more conformant output.

Internals
---------
David Reveman contributed a large re-work of the cairo_pattern_t
implementation, providing cleaner code and more optimization
opportunities.

 Backend interface changes
 -------------------------
 Rework backend interface to accept patterns, not surfaces for source
 and mask.

 Remove set_matrix, set_filter, and set_repeat functions.

 More sophisticated backend interface for image fallbacks,
 ({acquire,release}_{source,dest}_image() and clone_similar).

Bug fixes
---------
Only install header files for backends that have been compiled.

Fixed some rounding errors leading to incorrectly placed glyphs.

Many other minor fixes.

Snapshot 0.3.0 (2005-01-21 Carl Worth <<EMAIL>>)
==========================================================
Major API changes
-----------------
1) The public header files will no longer be directly installed into
   the system include directory. They will now be installed in a
   subdirectory named "cairo", (eg. in /usr/include/cairo rather than
   in /usr/include).

   As always, the easiest way for applications to discover the
   location of the header file is to let pkg-config generate the
   necessary -I CFLAGS and -L/-l LDFLAGS. For example:

	cc `pkg-config --cflags --libs cairo` -o foo foo.c

   IMPORTANT: Users with old versions of cairo installed will need to
	      manually remove cairo.h and cairo-features.h from the
	      system include directories in order to prevent the old
	      headers from being used in preference to the new ones.

2) The backend-specific portions of the old monolithic cairo.h have
   been split out into individual public header files. The new files
   are:

	cairo-atsui.h
	cairo-ft.h
	cairo-glitz.h
	cairo-pdf.h
	cairo-png.h
	cairo-ps.h
	cairo-quartz.h
	cairo-xcb.h
	cairo-xlib.h

   Applications will need to be modified to explicitly include the new
   header files where appropriate.

3) There are two new graphics backends in this snapshot, a PDF
   backend, and a Quartz backend. There is also one new font backend,
   ATSUI.

PDF backend
-----------
Kristian Høgsberg has contributed a new backend to allow cairo-based
applications to generate PDF output. The interface for creating a PDF
surface is similar to that of the PS backend, as can be seen in
cairo-pdf.h:

	void
	cairo_set_target_pdf (cairo_t	*cr,
			      FILE	*file,
			      double	width_inches,
			      double	height_inches,
			      double	x_pixels_per_inch,
			      double	y_pixels_per_inch);

	cairo_surface_t *
	cairo_pdf_surface_create (FILE		*file,
				  double	width_inches,
				  double	height_inches,
				  double	x_pixels_per_inch,
				  double	y_pixels_per_inch);

Once a PDF surface has been created, applications can draw to it as
any other cairo surface.

This code is still a bit rough around the edges, and does not yet
support clipping, surface patterns, or transparent gradients.  Text
only works with TrueType fonts at this point and only black text is
supported.  Also, the size of the generated PDF files is currently
quite big.

Kristian is still actively developing this backend, so watch this
space for future progress.

Quartz backend
--------------
Calum Robinson has contributed a new backend to allow cairo
applications to target native Mac OS X windows through the Quartz
API. Geoff Norton integrated this backend into the current
configure-based build system, while Calum also provided Xcode build
support in the separate "macosx" module available in CVS.

The new interface, available in cairo-quartz.h, is as follows:

	void
	cairo_set_target_quartz_context (cairo_t	*cr,
					 CGContextRef	context,
					 int		width,
					 int		height);

	cairo_surface_t *
	cairo_quartz_surface_create (CGContextRef context,
				     int	  width,
				     int	  height);

There is an example program available in CVS in cairo-demo/quartz. It
is a port of Keith Packard's fdclock program originally written for
the xlib backend. A screenshot of this program running on Mac OS X is
available here:

	https://cairographics.org/~cworth/images/fdclock-quartz.png

ATSUI font backend
------------------
This new font backend complements the Quartz backend by allowing
applications to use native font selection on Mac OS X. The interface
is a single new function:

	cairo_font_t *
	cairo_atsui_font_create (ATSUStyle style);

Minor API changes
-----------------
Prototype for non-existent function "cairo_ft_font_destroy" removed.

Now depends on libpixman 0.1.2 or newer, (0.1.3 is being released
concurrently and has some useful performance improvements).

Default paint color is now opaque black, (was opaque white). Default
background color is transparent (as before).

Renamed "struct cairo" to "struct _cairo" to free up the word "cairo"
from the C++ identifier name space.

Functions returning multiple return values through provided pointers,
(cairo_matrix_get_affine, cairo_current_point, and
cairo_current_color_rgb), will now accept NULL for values the user
wants to ignore.

CAIRO_HAS_FREETYPE_FONT has now been renamed to CAIRO_HAS_FT_FONT.

Performance improvements
------------------------
Alexander Larsson provided some fantastic performance improvements
yielding a 10000% performance improvement in his application, (when
also including his performance work in libpixman-0.1.3). These include

 * Fixed handling of cache misses.

 * Creating intermediate clip surfaces at the minimal size required.

 * Eliminating roundtrips when creating intermediate Xlib surfaces.

Implementation
--------------
Major re-work of font metrics system by Keith Packard. Font metrics
should now be much more reliable.

Glitz backend
-------------
Updated for glitz-0.3.0.
Bug fixes in reference counting.

Test suite
----------
New tests for cache crashing, rotating text, improper filling of
complex polygons, and leaky rasterization.

Bug fixes
---------
Fixed assertion failure when selecting the same font multiple times in
sequence.

Fixed reference counting so cache_destroy functions work.

Remove unintended copyright statement from files generated with
PostScript backend.

Fixed to eliminate new warnings from gcc 3.4 and gcc 4.

Snapshot 0.2.0 (2004-10-27 Carl Worth <<EMAIL>>)
===========================================================
New license: LGPL/MPL
---------------------
The most significant news with this release is that the license of
cairo has changed. It is now dual-licensed under the LGPL and the
MPL. For details see the COPYING file as well as COPYING-LGPL-2.1 and
COPYING-MPL-1.1.

I express my thanks to everyone involved in the license change process
for their patience and support!

New font and glyph internals
----------------------------
Graydon Hoare has put a tremendous amount of work into new internals
for handling fonts and glyphs, including caches where appropriate.
This work has no impact on the user-level API, but should result in
great performance improvements for applications using text.

New test suite
--------------
This snapshot of cairo includes a (small) test suite in
cairo/test. The tests can be run with "make check". The test suite was
designed to make it very easy to add new tests, and we hope to see
many contributions here. As you find bugs, please try adding a minimal
test case to the suite, and submit it with the bug report to the
<EMAIL> mailing list. This will make it much easier
for us to track progress in fixing bugs.

New name for glitz backend
--------------------------
The gl backend has now been renamed to the glitz backend. This means
that the following names have changed:

	CAIRO_HAS_GL_SURFACE    -> CAIRO_HAS_GLITZ_SURFACE
	cairo_set_target_gl     -> cairo_set_target_glitz
	cairo_gl_surface_create -> cairo_glitz_surface_create

This change obviously breaks backwards compatibility for applications
using the old gl backend.

Up-to-date with latest glitz snapshots
--------------------------------------
This snapshot of cairo is now up to date with the latest glitz
snapshot, (currently 0.2.3). We know that the latest cairo and glitz
snapshots have been incompatible for a very long time. We've finally
fixed that now and we're determined to not let that happen again.

Revert some tessellation regression bugs
----------------------------------------
People that have been seeing some tessellation bugs, (eg. leaked
fills), in the CVS version of cairo may have better luck with this
release. A change since the last snapshot was identified to trigger
some of these bugs and was reverted before making the snapshot. The
behavior should be the same as the previous (0.1.23) snapshot.

Miscellaneous changes
---------------------
Changed CAIRO_FILTER_DEFAULT to CAIRO_FILTER_BEST to make gradients
easier.

Track XCB API change regarding iterators.

Various bug fixes
-----------------
Fix calculation of required number of vertices for pen.

Fix to avoid zero-dimensioned pixmaps.

Fix broken sort of pen vertices.

Fix bug when cairo_show_text called with a NULL string.

Fix clipping bugs.

Fix bug in computing image length with XCB.

Fix infinite loop bug in cairo_arc.

Fix memory management interactions with libpixman.

Snapshot 0.1.23 (2004-05-11 Carl Worth <<EMAIL>>)
========================================================
Fixes for gcc 3.4
-----------------
Fix prototype mismatches so that cairo can be built by gcc 3.4.

Updates to track glitz
----------------------
Various fixes to support the latest glitz snapshot (0.1.2).

Gradient updates
----------------
Radial gradients now support both inner and outer circles.
Transformed linear gradients are now properly handled.
Fixes for extend type reflect.

Glitz updates
-------------
Converted shading routines to use fixed point values and introduced a
shading operator structure for more efficient shading calculations.
Support compositing with mask surface when mask is solid or
multi-texturing is available.

PNG backend cleanups
--------------------
Fix output to properly compensate for pre-multiplied alpha format in cairo.
Add support for A8 and A1 image formats.

Bug fixes
---------
Avoid crash or infinite loop on null strings and degeneratively short
splines.

New? bugs in cairo_clip
-----------------------
There are some fairly serious bugs in cairo_clip. It is sometimes
causing an incorrect result. And even when it does work, it is
sometimes so slow as to be unusable. Some of these bugs may not be
new, (indeed cairo_clip has only ever had a braindead-slow
implementation), but I think they're worth mentioning here.

Snapshot 0.1.22 (2004-04-16 Carl Worth <<EMAIL>>)
========================================================
Cairo was updated to track the changes in libpixman, and now depends
on libpixman version 0.1.1.

Snapshot 0.1.21 (2004-04-09 David Reveman <<EMAIL>>)
=============================================================
New OpenGL backend
------------------
The OpenGL backend provides hardware accelerated output for
X11 and OS X. The significant new functions are:

	cairo_set_target_gl
	cairo_gl_surface_create

Automatic detection of available backends
-----------------------------------------
The configure script now automatically detect what backends are
available, (use ./configure --disable-`backend' to prevent
compilation of specific backends).

Snapshot 0.1.20 (2004-04-06 Carl Worth <<EMAIL>>)
========================================================
New pattern API
---------------
David Reveman has contributed a new pattern API which enable linear
and radial gradient patterns in addition to the original surface-based
patterns. The significant new top-level functions are:

	cairo_pattern_create_linear
	cairo_pattern_create_radial
	cairo_pattern_create_for_surface
	cairo_pattern_add_color_stop
	cairo_set_pattern

Any code using the old cairo_set_pattern, (which accepted a
cairo_surface_t rather than a cairo_pattern_t), will need to be
updated.

Update to XCB backend
---------------------
The XCB backend is now enabled by default, (use ./configure
--disable-xcb to turn it off).

Faster clipping
---------------
Graydon Hoare has added optimizations that make cairo_clip much faster
when the path is a pixel-aligned, rectangular region.

Bug fixes.

Snapshot 0.1.19 (2004-02-24 Carl Worth <<EMAIL>>)
========================================================
New PNG backend
---------------
Olivier Andrieu contributed a new PNG backend. It builds on the
existing image backend to make it easy to render "directly" to a
.png file. The user never needs to deal with the actual image
buffer. The significant new functions are:

	cairo_set_target_png
	cairo_png_surface_create

The PNG backend is not enabled by default so that by default there is
not a new dependency on libpng. Use ./configure --enable-png to enable
this backend.

Snapshot 0.1.18 (2004-02-17 Carl Worth <<EMAIL>>)
========================================================
Path query functionality
------------------------
It's now possible to query the current path. The two new functions
are:

	cairo_current_path
	cairo_current_path_flat

Each function accepts a number of callback functions that will be
called for each element in the path (move_to, line_to, curve_to,
close_path). The cairo_current_path_flat function does not accept a
curve_to callback. Instead, all curved portions of the path will be
converted to line segments, (within the current tolerance value). This
can be handy for doing things like text-on-path without having to
manually interpolate Bézier splines.

New XCB backend
---------------
Jamey Sharp has contributed a second X backend that uses the new, lean
XCB library rather than Xlib. It cannot currently be compiled at the
same time as the Xlib backend. See ./configure --enable-xcb.

Build fixes for cygwin.

Bug fixes.

Snapshot 0.1.17 (2003-12-16 Carl Worth <<EMAIL>>)
========================================================

Better text support
-------------------
This snapshot provides much better text support by implementing the
following four functions:

	cairo_text_extents
	cairo_glyph_extents
	cairo_text_path
	cairo_glyph_path

The text/glyph_extents functions can be used to determine the bounding
box (and advance) for text as if drawn by show_text/glyphs.

The text/glyph_path objects functions place text shapes on the current
path, where they can be subsequently manipulated. For example,
following these functions with cairo_stroke allows outline text to be
drawn. Calling cairo_clip allows clipping to a text-shaped region.

Combined dependencies
---------------------
The cairo core now depends only on the libpixman library. This single
library replaces the three previous libraries libic, libpixregion, and
slim. Thanks to Dave Beckett <<EMAIL>> for all of
the heavy lifting with this renaming effort.

Conditional compilation of backends
-----------------------------------
Cairo now allows optional backends to be disabled at compile time. The
following options may now be passed to the configure script:

	--disable-xlib
	--disable-ps

Note that the first option is a change from the old --without-x option
which will no longer have any effect.

OS X supported - several byte-order issues resolved
---------------------------------------------------
Cairo has now been successfully compiled under OS X. Testing revealed
that there were some byte-order problems in the PostScript backend and
the PNG generation in the demos. These have now been resolved.

2003-10
=======
Graydon Hoare <<EMAIL>> implemented the first real text
support using FreeType/fontconfig, (previous versions of cairo used
Xft and could only draw text when using an X backend).

2003-09
=======
Graydon Hoare <<EMAIL>> added the first real support for
running cairo with a non-render-aware X server.

Jamey Sharp <<EMAIL>> virtualized the backend font and
surface interfaces in September, 2003.

2003-06
=======
Xr is renamed cairo to avoid confusion since it no longer had a strict
dependence on X.

2003-05
=======
A new image surface backend is added to Xr. Keith Packard
<<EMAIL>> wrote the image compositing code in libic that is
used for the image_surface backend. This code was originally written
as the software fallback for the render extension within the X
server.

2002-06
=======
Carl Worth <<EMAIL>> wrote the first lines of Xr, after Keith
Packard <<EMAIL>> proposed the plan for a stateful drawing
library in C providing a PostScript-like rendering model.

 LocalWords:  mutex BeOS extraordinaire distro's URL lcd bool tarball
