# IMPORTANT: Keep in sync with boilerplate/make-cairo-boilerplate-constructors.py!
import argparse
import sys
import re

if __name__=='__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('output')
    parser.add_argument('input', nargs='+')
    args = parser.parse_args()

    test_names = []

    match_test_line = re.compile(r'^CAIRO_TEST.*')
    match_test_name = re.compile(r'^CAIRO_TEST.*\((.*),.*')

    for fname in args.input:
        with open(fname, 'r', encoding='utf-8') as f:
            for l in f.readlines():
                if match_test_line.match(l):
                    test_names.append(match_test_name.match(l).group(1))

    with open(args.output, 'w', encoding='utf-8') as f:
        f.write('/* WARNING: Autogenerated file - see %s! */\n\n' % sys.argv[0])
        f.write('#include "cairo-test-private.h"\n\n')
        f.write('void _cairo_test_runner_register_tests (void);\n\n')

        for test_name in test_names:
            f.write('extern void _register_%s (void);\n' % test_name)

        f.write('void\n')
        f.write('_cairo_test_runner_register_tests (void)\n')
        f.write('{\n')

        for test_name in test_names:
            f.write('    _register_%s ();\n' % test_name)
        f.write('}\n')
