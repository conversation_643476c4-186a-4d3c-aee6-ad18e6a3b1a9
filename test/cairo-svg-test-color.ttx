<?xml version="1.0" encoding="UTF-8"?>
<ttFont sfntVersion="\x00\x01\x00\x00" ttLibVersion="4.38">

  <GlyphOrder>
    <!-- The 'id' attribute is only for humans; it is ignored when parsed. -->
    <GlyphID id="0" name=".notdef"/>
    <GlyphID id="1" name="zero"/>
    <GlyphID id="2" name="one"/>
    <GlyphID id="3" name="two"/>
    <GlyphID id="4" name="three"/>
    <GlyphID id="5" name="four"/>
    <GlyphID id="6" name="five"/>
    <GlyphID id="7" name="six"/>
    <GlyphID id="8" name="seven"/>
  </GlyphOrder>

  <head>
    <!-- Most of this table will be recalculated by the compiler -->
    <tableVersion value="1.0"/>
    <fontRevision value="1.0"/>
    <checkSumAdjustment value="0x1d3a9a74"/>
    <magicNumber value="0x5f0f3cf5"/>
    <flags value="00000000 00000011"/>
    <unitsPerEm value="1000"/>
    <created value="Wed Jun 15 00:00:00 2022"/>
    <modified value="Fri Jan 27 08:44:23 2023"/>
    <xMin value="0"/>
    <yMin value="0"/>
    <xMax value="1000"/>
    <yMax value="1000"/>
    <macStyle value="00000000 00000000"/>
    <lowestRecPPEM value="6"/>
    <fontDirectionHint value="2"/>
    <indexToLocFormat value="0"/>
    <glyphDataFormat value="0"/>
  </head>

  <hhea>
    <tableVersion value="0x00010000"/>
    <ascent value="1000"/>
    <descent value="0"/>
    <lineGap value="200"/>
    <advanceWidthMax value="1100"/>
    <minLeftSideBearing value="0"/>
    <minRightSideBearing value="100"/>
    <xMaxExtent value="1000"/>
    <caretSlopeRise value="1"/>
    <caretSlopeRun value="0"/>
    <caretOffset value="0"/>
    <reserved0 value="0"/>
    <reserved1 value="0"/>
    <reserved2 value="0"/>
    <reserved3 value="0"/>
    <metricDataFormat value="0"/>
    <numberOfHMetrics value="1"/>
  </hhea>

  <maxp>
    <!-- Most of this table will be recalculated by the compiler -->
    <tableVersion value="0x10000"/>
    <numGlyphs value="9"/>
    <maxPoints value="4"/>
    <maxContours value="1"/>
    <maxCompositePoints value="0"/>
    <maxCompositeContours value="0"/>
    <maxZones value="1"/>
    <maxTwilightPoints value="0"/>
    <maxStorage value="0"/>
    <maxFunctionDefs value="0"/>
    <maxInstructionDefs value="0"/>
    <maxStackElements value="256"/>
    <maxSizeOfInstructions value="1"/>
    <maxComponentElements value="0"/>
    <maxComponentDepth value="0"/>
  </maxp>

  <OS_2>
    <!-- The fields 'usFirstCharIndex' and 'usLastCharIndex'
         will be recalculated by the compiler -->
    <version value="3"/>
    <xAvgCharWidth value="1000"/>
    <usWeightClass value="400"/>
    <usWidthClass value="5"/>
    <fsType value="00000000 00000001"/>
    <ySubscriptXSize value="600"/>
    <ySubscriptYSize value="600"/>
    <ySubscriptXOffset value="0"/>
    <ySubscriptYOffset value="75"/>
    <ySuperscriptXSize value="600"/>
    <ySuperscriptYSize value="600"/>
    <ySuperscriptXOffset value="0"/>
    <ySuperscriptYOffset value="300"/>
    <yStrikeoutSize value="0"/>
    <yStrikeoutPosition value="300"/>
    <sFamilyClass value="0"/>
    <panose>
      <bFamilyType value="0"/>
      <bSerifStyle value="0"/>
      <bWeight value="0"/>
      <bProportion value="0"/>
      <bContrast value="0"/>
      <bStrokeVariation value="0"/>
      <bArmStyle value="0"/>
      <bLetterForm value="0"/>
      <bMidline value="0"/>
      <bXHeight value="0"/>
    </panose>
    <ulUnicodeRange1 value="00000000 00000000 00000000 00000001"/>
    <ulUnicodeRange2 value="00000000 00000000 00000000 00000000"/>
    <ulUnicodeRange3 value="00000000 00000000 00000000 00000000"/>
    <ulUnicodeRange4 value="00000000 00000000 00000000 00000000"/>
    <achVendID value="djr "/>
    <fsSelection value="00000000 01000000"/>
    <usFirstCharIndex value="48"/>
    <usLastCharIndex value="55"/>
    <sTypoAscender value="1000"/>
    <sTypoDescender value="0"/>
    <sTypoLineGap value="200"/>
    <usWinAscent value="1000"/>
    <usWinDescent value="300"/>
    <ulCodePageRange1 value="00000000 00000000 00000000 00000001"/>
    <ulCodePageRange2 value="00000000 00000000 00000000 00000000"/>
    <sxHeight value="500"/>
    <sCapHeight value="720"/>
    <usDefaultChar value="0"/>
    <usBreakChar value="32"/>
    <usMaxContext value="3"/>
  </OS_2>

  <hmtx>
    <mtx name=".notdef" width="1100" lsb="0"/>
    <mtx name="five" width="1100" lsb="0"/>
    <mtx name="four" width="1100" lsb="0"/>
    <mtx name="one" width="1100" lsb="0"/>
    <mtx name="seven" width="1100" lsb="0"/>
    <mtx name="six" width="1100" lsb="0"/>
    <mtx name="three" width="1100" lsb="0"/>
    <mtx name="two" width="1100" lsb="0"/>
    <mtx name="zero" width="1100" lsb="0"/>
  </hmtx>

  <cmap>
    <tableVersion version="0"/>
    <cmap_format_4 platformID="0" platEncID="3" language="0">
      <map code="0x30" name="zero"/><!-- DIGIT ZERO -->
      <map code="0x31" name="one"/><!-- DIGIT ONE -->
      <map code="0x32" name="two"/><!-- DIGIT TWO -->
      <map code="0x33" name="three"/><!-- DIGIT THREE -->
      <map code="0x34" name="four"/><!-- DIGIT FOUR -->
      <map code="0x35" name="five"/><!-- DIGIT FIVE -->
      <map code="0x36" name="six"/><!-- DIGIT SIX -->
      <map code="0x37" name="seven"/><!-- DIGIT SEVEN -->
    </cmap_format_4>
  </cmap>

  <loca>
    <!-- The 'loca' table will be calculated by the compiler -->
  </loca>

  <glyf>

    <!-- The xMin, yMin, xMax and yMax values
         will be recalculated by the compiler. -->

    <TTGlyph name=".notdef"/><!-- contains no outline data -->

    <TTGlyph name="five" xMin="0" yMin="0" xMax="1000" yMax="1000">
      <contour>
        <pt x="0" y="0" on="1"/>
        <pt x="0" y="1000" on="1"/>
        <pt x="1000" y="1000" on="1"/>
        <pt x="1000" y="0" on="1"/>
      </contour>
      <instructions/>
    </TTGlyph>

    <TTGlyph name="four" xMin="0" yMin="0" xMax="1000" yMax="1000">
      <contour>
        <pt x="0" y="0" on="1"/>
        <pt x="0" y="1000" on="1"/>
        <pt x="1000" y="1000" on="1"/>
        <pt x="1000" y="0" on="1"/>
      </contour>
      <instructions/>
    </TTGlyph>

    <TTGlyph name="one" xMin="0" yMin="0" xMax="1000" yMax="1000">
      <contour>
        <pt x="0" y="0" on="1"/>
        <pt x="0" y="1000" on="1"/>
        <pt x="1000" y="1000" on="1"/>
        <pt x="1000" y="0" on="1"/>
      </contour>
      <instructions/>
    </TTGlyph>

    <TTGlyph name="seven" xMin="0" yMin="0" xMax="1000" yMax="1000">
      <contour>
        <pt x="0" y="0" on="1"/>
        <pt x="0" y="1000" on="1"/>
        <pt x="1000" y="1000" on="1"/>
        <pt x="1000" y="0" on="1"/>
      </contour>
      <instructions/>
    </TTGlyph>

    <TTGlyph name="six" xMin="0" yMin="0" xMax="1000" yMax="1000">
      <contour>
        <pt x="0" y="0" on="1"/>
        <pt x="0" y="1000" on="1"/>
        <pt x="1000" y="1000" on="1"/>
        <pt x="1000" y="0" on="1"/>
      </contour>
      <instructions/>
    </TTGlyph>

    <TTGlyph name="three" xMin="0" yMin="0" xMax="1000" yMax="1000">
      <contour>
        <pt x="0" y="0" on="1"/>
        <pt x="0" y="1000" on="1"/>
        <pt x="1000" y="1000" on="1"/>
        <pt x="1000" y="0" on="1"/>
      </contour>
      <instructions/>
    </TTGlyph>

    <TTGlyph name="two" xMin="0" yMin="0" xMax="1000" yMax="1000">
      <contour>
        <pt x="0" y="0" on="1"/>
        <pt x="0" y="1000" on="1"/>
        <pt x="1000" y="1000" on="1"/>
        <pt x="1000" y="0" on="1"/>
      </contour>
      <instructions/>
    </TTGlyph>

    <TTGlyph name="zero" xMin="0" yMin="0" xMax="1000" yMax="1000">
      <contour>
        <pt x="0" y="0" on="1"/>
        <pt x="0" y="1000" on="1"/>
        <pt x="1000" y="1000" on="1"/>
        <pt x="1000" y="0" on="1"/>
      </contour>
      <instructions/>
    </TTGlyph>

  </glyf>

  <name>
    <namerecord nameID="1" platformID="3" platEncID="1" langID="0x409">
      Cairo Svg Test Color
    </namerecord>
    <namerecord nameID="2" platformID="3" platEncID="1" langID="0x409">
      Regular
    </namerecord>
    <namerecord nameID="4" platformID="3" platEncID="1" langID="0x409">
      Cairo Svg Test Color Regular
    </namerecord>
  </name>

  <post>
    <formatType value="3.0"/>
    <italicAngle value="0.0"/>
    <underlinePosition value="0"/>
    <underlineThickness value="0"/>
    <isFixedPitch value="0"/>
    <minMemType42 value="0"/>
    <maxMemType42 value="0"/>
    <minMemType1 value="0"/>
    <maxMemType1 value="0"/>
  </post>

  <CPAL>
    <version value="0"/>
    <numPaletteEntries value="2"/>
    <palette index="0">
      <color index="0" value="#C5A1D7FF"/>
      <color index="1" value="#80DFC8FF"/>
    </palette>
    <palette index="1">
      <color index="0" value="#6392A9FF"/>
      <color index="1" value="#7896B3FF"/>
    </palette>
  </CPAL>

  <SVG>
    <svgDoc endGlyphID="0" startGlyphID="0">
      <![CDATA[<svg xmlns="http://www.w3.org/2000/svg"></svg>]]>
    </svgDoc>
    <svgDoc endGlyphID="1" startGlyphID="1">
      <![CDATA[<svg version="1.1" xmlns="http://www.w3.org/2000/svg">
  <rect x="100" y="-750" width="800" height="500"
        fill="var(--color0, red)" />
</svg>]]>
    </svgDoc>
    <svgDoc endGlyphID="2" startGlyphID="2">
      <![CDATA[<svg version="1.1" xmlns="http://www.w3.org/2000/svg">
  <rect x="100" y="-750" width="800" height="500"
        fill="var(--color1, blue)" />
</svg>]]>
    </svgDoc>
    <svgDoc endGlyphID="3" startGlyphID="3">
      <![CDATA[<svg version="1.1" xmlns="http://www.w3.org/2000/svg">
  <rect x="100" y="-750" width="800" height="500"
        fill="currentColor" />
</svg>]]>
    </svgDoc>
    <svgDoc endGlyphID="4" startGlyphID="4">
      <![CDATA[<svg version="1.1" xmlns="http://www.w3.org/2000/svg">
  <rect x="100" y="-750" width="800" height="500"
        fill="currentColor" fill-opacity="0.5"  />
</svg>]]>
    </svgDoc>
    <svgDoc endGlyphID="5" startGlyphID="5">
      <![CDATA[<svg version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="33%" x21="66%">
      <stop offset="0%" stop-color="currentColor" stop-opacity="1" />
      <stop offset="100%" stop-color="red" stop-opacity="1" />
    </linearGradient>
  </defs>
  <rect x="100" y="-900" width="800" height="800" fill="url(#grad)" />
</svg>]]>
    </svgDoc>
    <svgDoc endGlyphID="6" startGlyphID="6">
      <![CDATA[<svg version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="33%" x21="66%">
      <stop offset="0%" stop-color="currentColor" stop-opacity="0.3" />
      <stop offset="100%" stop-color="red" stop-opacity="0.3" />
    </linearGradient>
  </defs>
  <rect x="100" y="-900" width="800" height="800" fill="url(#grad)" />
</svg>]]>
    </svgDoc>
    <svgDoc endGlyphID="7" startGlyphID="7">
      <![CDATA[<svg version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="grad" cx="50%" cy="50%"
                    fx="0.75" fy="0.35" r="0.5">
      <stop offset="0%" stop-color="currentColor" stop-opacity="1" />
      <stop offset="100%" stop-color="blue" stop-opacity="1" />
    </radialGradient>
  </defs>
  <rect x="100" y="-900" width="800" height="800" fill="url(#grad)" />
</svg>]]>
    </svgDoc>
    <svgDoc endGlyphID="8" startGlyphID="8">
      <![CDATA[<svg version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="grad" cx="50%" cy="50%"
                    fx="0.75" fy="0.35" r="0.5">
      <stop offset="0%" stop-color="currentColor" stop-opacity="0.5" />
      <stop offset="100%" stop-color="blue" stop-opacity="0.5" />
    </radialGradient>
  </defs>
  <rect x="100" y="-900" width="800" height="800" fill="url(#grad)" />
</svg>]]>
    </svgDoc>
  </SVG>

</ttFont>
