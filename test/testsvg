#!/bin/sh

IMAGEDIFF=./imagediff

OUTDIR=testsvg-output
REFDIR=testsvg-reference
DIFFDIR=testsvg-diff
IMAGELIST=testsvg-imagelist

if [ $# -lt 1 ]; then
	argv0=`basename $0`
	echo "Usage: $argv0 file.svg [...]" >&2
	exit 1;
fi

mkdir -p $OUTDIR
mkdir -p $DIFFDIR
rm -f $IMAGELIST

err=0
for svg in $@; do
	svgbase=`basename $svg`
	png=${svgbase/\.svg/.png}
	outpng=$OUTDIR/$png
	refpng=$REFDIR/$png
	diffpng=$DIFFDIR/$png
#	if xsvg $svg -p $outpng ; then
	if svg2png $svg $outpng ; then
		if [ -e $refpng ]; then
			if $IMAGEDIFF $refpng $outpng > $diffpng; then
				echo "Rendering of $svg matches." >&2
				rm -f $diffpng
			else
				echo "ERROR: Rendering of $svg differs from reference image." >&2
				echo $refpng $outpng $diffpng >> $IMAGELIST
				err=$(($err+1))
			fi
		else
			echo "WARNING: No reference file found for $svg (looked in $refpng)" >&2
		fi
	else
		echo "ERROR: Failed to render $svg" >&2
		err=$(($err+1))
	fi
done

if [ $err -gt 0 ] ; then
	echo "Differences found in $err renderings."
else
	echo "All renderings matched reference images."
fi
