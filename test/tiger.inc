static const struct command {
  char type;
  float x0, y0;
  float x1, y1;
  float x2, y2;
} tiger_commands[] = {
{'m', -122.30, 84.28, 0, 0, 0, 0}, 
{'c', -122.30, 84.28, -122.20 ,86.18, -123.03, 86.16}, 
{'c', -123.85, 86.14, -140.31 ,38.07, -160.83, 40.31}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -118.77, 81.26, 0, 0, 0, 0}, 
{'c', -118.77, 81.26, -119.32 ,83.08, -120.09, 82.78}, 
{'c', -120.86, 82.48, -119.98 ,31.68, -140.04, 26.80}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -91.28, 123.59, 0, 0, 0, 0}, 
{'c', -91.28, 123.59, -89.65 ,124.55, -90.12, 125.23}, 
{'c', -90.59, 125.90, -139.76 ,113.10, -149.22, 131.46}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -94.09, 133.80, 0, 0, 0, 0}, 
{'c', -94.09, 133.80, -92.24 ,134.20, -92.47, 134.99}, 
{'c', -92.70, 135.78, -143.41 ,139.12, -146.60, 159.52}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -98.30, 128.28, 0, 0, 0, 0}, 
{'c', -98.30, 128.28, -96.53 ,128.94, -96.87, 129.69}, 
{'c', -97.22, 130.44, -147.87 ,126.35, -154.00, 146.06}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -109.01, 110.07, 0, 0, 0, 0}, 
{'c', -109.01, 110.07, -107.70 ,111.45, -108.34, 111.97}, 
{'c', -108.98, 112.49, -152.72 ,86.63, -166.87, 101.68}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -116.55, 114.26, 0, 0, 0, 0}, 
{'c', -116.55, 114.26, -115.10 ,115.48, -115.67, 116.07}, 
{'c', -116.25, 116.66, -162.64 ,95.92, -174.99, 112.47}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -119.15, 118.33, 0, 0, 0, 0}, 
{'c', -119.15, 118.33, -117.55 ,119.34, -118.04, 120.01}, 
{'c', -118.53, 120.67, -167.31 ,106.45, -177.29, 124.52}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -108.42, 118.95, 0, 0, 0, 0}, 
{'c', -108.42, 118.95, -107.30 ,120.48, -108.00, 120.92}, 
{'c', -108.70, 121.35, -148.77 ,90.10, -164.73, 103.21}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -128.20, 90.00, 0, 0, 0, 0}, 
{'c', -128.20, 90.00, -127.60 ,91.80, -128.40, 92.00}, 
{'c', -129.20, 92.20, -157.80 ,50.20, -177.00, 57.80}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -127.50, 96.98, 0, 0, 0, 0}, 
{'c', -127.50, 96.98, -126.53 ,98.61, -127.27, 98.97}, 
{'c', -128.01, 99.34, -164.99 ,64.50, -182.10, 76.06}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -127.62, 101.35, 0, 0, 0, 0}, 
{'c', -127.62, 101.35, -126.50 ,102.88, -127.20, 103.31}, 
{'c', -127.90, 103.75, -167.97 ,72.50, -183.93, 85.61}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -129.83, 103.06, 0, 0, 0, 0}, 
{'c', -129.33, 109.11, -128.34 ,115.68, -126.60, 118.80}, 
{'c', -126.60, 118.80, -130.20 ,131.20, -121.40, 144.40}, 
{'c', -121.40, 144.40, -121.80 ,151.60, -120.20, 154.80}, 
{'c', -120.20, 154.80, -116.20 ,163.20, -111.40, 164.00}, 
{'c', -107.52, 164.65, -98.79 ,167.72, -88.93, 169.12}, 
{'c', -88.93, 169.12, -71.80 ,183.20, -75.00, 196.00}, 
{'c', -75.00, 196.00, -75.40 ,212.40, -79.00, 214.00}, 
{'c', -79.00, 214.00, -67.40 ,202.80, -77.00, 219.60}, 
{'l', -81.40, 238.40, 0, 0, 0, 0}, 
{'c', -81.40, 238.40, -55.80 ,216.80, -71.40, 235.20}, 
{'l', -81.40, 261.20, 0, 0, 0, 0}, 
{'c', -81.40, 261.20, -61.80 ,242.80, -69.00, 251.20}, 
{'l', -72.20, 260.00, 0, 0, 0, 0}, 
{'c', -72.20, 260.00, -29.00 ,232.80, -59.80, 262.40}, 
{'c', -59.80, 262.40, -51.80 ,258.80, -47.40, 261.60}, 
{'c', -47.40, 261.60, -40.60 ,260.40, -41.40, 262.00}, 
{'c', -41.40, 262.00, -62.20 ,272.40, -65.80, 290.80}, 
{'c', -65.80, 290.80, -57.40 ,280.80, -60.60, 291.60}, 
{'l', -60.20, 303.20, 0, 0, 0, 0}, 
{'c', -60.20, 303.20, -56.20 ,281.60, -56.60, 319.20}, 
{'c', -56.60, 319.20, -37.40 ,301.20, -49.00, 322.00}, 
{'l', -49.00, 338.80, 0, 0, 0, 0}, 
{'c', -49.00, 338.80, -33.80 ,322.40, -40.20, 335.20}, 
{'c', -40.20, 335.20, -30.20 ,326.40, -34.20, 341.60}, 
{'c', -34.20, 341.60, -35.00 ,352.00, -30.60, 340.80}, 
{'c', -30.60, 340.80, -14.60 ,310.20, -20.60, 336.40}, 
{'c', -20.60, 336.40, -21.40 ,355.60, -16.60, 340.80}, 
{'c', -16.60, 340.80, -16.20 ,351.20, -7.00, 358.40}, 
{'c', -7.00, 358.40, -8.20 ,307.60, 4.60, 343.60}, 
{'l', 8.60, 360.00, 0, 0, 0, 0}, 
{'c', 8.60, 360.00, 11.40 ,350.80, 11.00, 345.60}, 
{'c', 11.00, 345.60, 25.80 ,329.20, 19.00, 353.60}, 
{'c', 19.00, 353.60, 34.20 ,330.80, 31.00, 344.00}, 
{'c', 31.00, 344.00, 23.40 ,360.00, 25.00, 364.80}, 
{'c', 25.00, 364.80, 41.80 ,330.00, 43.00, 328.40}, 
{'c', 43.00, 328.40, 41.00 ,370.80, 51.80, 334.80}, 
{'c', 51.80, 334.80, 57.40 ,346.80, 54.60, 351.20}, 
{'c', 54.60, 351.20, 62.60 ,343.20, 61.80, 340.00}, 
{'c', 61.80, 340.00, 66.40 ,331.80, 69.20, 345.40}, 
{'c', 69.20, 345.40, 71.00 ,354.80, 72.60, 351.60}, 
{'c', 72.60, 351.60, 76.60 ,375.60, 77.80, 352.80}, 
{'c', 77.80, 352.80, 79.40 ,339.20, 72.20, 327.60}, 
{'c', 72.20, 327.60, 73.00 ,324.40, 70.20, 320.40}, 
{'c', 70.20, 320.40, 83.80 ,342.00, 76.60, 313.20}, 
{'c', 76.60, 313.20, 87.80 ,321.20, 89.00, 321.20}, 
{'c', 89.00, 321.20, 75.40 ,298.00, 84.20, 302.80}, 
{'c', 84.20, 302.80, 79.00 ,292.40, 97.00, 304.40}, 
{'c', 97.00, 304.40, 81.00 ,288.40, 98.60, 298.00}, 
{'c', 98.60, 298.00, 106.60 ,304.40, 99.00, 294.40}, 
{'c', 99.00, 294.40, 84.60 ,278.40, 106.60, 296.40}, 
{'c', 106.60, 296.40, 118.20 ,312.80, 119.00, 315.60}, 
{'c', 119.00, 315.60, 109.00 ,286.40, 104.60, 283.60}, 
{'c', 104.60, 283.60, 113.00 ,247.20, 154.20, 262.80}, 
{'c', 154.20, 262.80, 161.00 ,280.00, 165.40, 261.60}, 
{'c', 165.40, 261.60, 178.20 ,255.20, 189.40, 282.80}, 
{'c', 189.40, 282.80, 193.40 ,269.20, 192.60, 266.40}, 
{'c', 192.60, 266.40, 199.40 ,267.60, 198.60, 266.40}, 
{'c', 198.60, 266.40, 211.80 ,270.80, 213.00, 270.00}, 
{'c', 213.00, 270.00, 219.80 ,276.80, 220.20, 273.20}, 
{'c', 220.20, 273.20, 229.40 ,276.00, 227.40, 272.40}, 
{'c', 227.40, 272.40, 236.20 ,288.00, 236.60, 291.60}, 
{'l', 239.00, 277.60, 0, 0, 0, 0}, 
{'l', 241.00, 280.40, 0, 0, 0, 0}, 
{'c', 241.00, 280.40, 242.60 ,272.80, 241.80, 271.60}, 
{'c', 241.00, 270.40, 261.80 ,278.40, 266.60, 299.20}, 
{'l', 268.60, 307.60, 0, 0, 0, 0}, 
{'c', 268.60, 307.60, 274.60 ,292.80, 273.00, 288.80}, 
{'c', 273.00, 288.80, 278.20 ,289.60, 278.60, 294.00}, 
{'c', 278.60, 294.00, 282.60 ,270.80, 277.80, 264.80}, 
{'c', 277.80, 264.80, 282.20 ,264.00, 283.40, 267.60}, 
{'l', 283.40, 260.40, 0, 0, 0, 0}, 
{'c', 283.40, 260.40, 290.60 ,261.20, 290.60, 258.80}, 
{'c', 290.60, 258.80, 295.00 ,254.80, 297.00, 259.60}, 
{'c', 297.00, 259.60, 284.60 ,224.40, 303.00, 243.60}, 
{'c', 303.00, 243.60, 310.20 ,254.40, 306.60, 235.60}, 
{'c', 303.00, 216.80, 299.00 ,215.20, 303.80, 214.80}, 
{'c', 303.80, 214.80, 304.60 ,211.20, 302.60, 209.60}, 
{'c', 300.60, 208.00, 303.80 ,209.60, 303.80, 209.60}, 
{'c', 303.80, 209.60, 308.60 ,213.60, 303.40, 191.60}, 
{'c', 303.40, 191.60, 309.80 ,193.20, 297.80, 164.00}, 
{'c', 297.80, 164.00, 300.60 ,161.60, 296.60, 153.20}, 
{'c', 296.60, 153.20, 304.60 ,157.60, 307.40, 156.00}, 
{'c', 307.40, 156.00, 307.00 ,154.40, 303.80, 150.40}, 
{'c', 303.80, 150.40, 282.20 ,95.60, 302.60, 117.60}, 
{'c', 302.60, 117.60, 314.45 ,131.15, 308.05, 108.35}, 
{'c', 308.05, 108.35, 298.94 ,84.34, 299.72, 80.05}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 299.72, 80.25, 0, 0, 0, 0}, 
{'c', 300.35, 80.43, 302.55 ,81.55, 303.80, 83.20}, 
{'c', 303.80, 83.20, 310.60 ,94.00, 305.40, 75.60}, 
{'c', 305.40, 75.60, 296.20 ,46.80, 305.00, 58.00}, 
{'c', 305.00, 58.00, 311.00 ,65.20, 307.80, 51.60}, 
{'c', 303.94, 35.17, 301.40 ,28.80, 301.40, 28.80}, 
{'c', 301.40, 28.80, 313.00 ,33.60, 286.20, -6.00}, 
{'l', 295.00, -2.40, 0, 0, 0, 0}, 
{'c', 295.00, -2.40, 275.40 ,-42.00, 253.80, -47.20}, 
{'l', 245.80, -53.20, 0, 0, 0, 0}, 
{'c', 245.80, -53.20, 284.20 ,-91.20, 271.40, -128.00}, 
{'c', 271.40, -128.00, 264.60 ,-133.20, 255.00, -124.00}, 
{'c', 255.00, -124.00, 248.60 ,-119.20, 242.60, -120.80}, 
{'c', 242.60, -120.80, 211.80 ,-119.60, 209.80, -119.60}, 
{'c', 207.80, -119.60, 173.00 ,-156.80, 107.40, -139.20}, 
{'c', 107.40, -139.20, 102.20 ,-137.20, 97.80, -138.40}, 
{'c', 97.80, -138.40, 79.40 ,-154.40, 30.60, -131.60}, 
{'c', 30.60, -131.60, 20.60 ,-129.60, 19.00, -129.60}, 
{'c', 17.40, -129.60, 14.60 ,-129.60, 6.60, -123.20}, 
{'c', -1.40, -116.80, -1.80 ,-116.00, -3.80, -114.40}, 
{'c', -3.80, -114.40, -20.20 ,-103.20, -25.00, -102.40}, 
{'c', -25.00, -102.40, -36.60 ,-96.00, -41.00, -86.00}, 
{'l', -44.60, -84.80, 0, 0, 0, 0}, 
{'c', -44.60, -84.80, -46.20 ,-77.60, -46.60, -76.40}, 
{'c', -46.60, -76.40, -51.40 ,-72.80, -52.20, -67.20}, 
{'c', -52.20, -67.20, -61.00 ,-61.20, -60.60, -56.80}, 
{'c', -60.60, -56.80, -62.20 ,-51.60, -63.00, -46.80}, 
{'c', -63.00, -46.80, -70.20 ,-42.00, -69.40, -39.20}, 
{'c', -69.40, -39.20, -77.00 ,-25.20, -75.80, -18.40}, 
{'c', -75.80, -18.40, -82.20 ,-18.80, -85.00, -16.40}, 
{'c', -85.00, -16.40, -85.80 ,-11.60, -87.40, -11.20}, 
{'c', -87.40, -11.20, -90.20 ,-10.00, -87.80, -6.00}, 
{'c', -87.80, -6.00, -89.40 ,-3.20, -89.80, -1.60}, 
{'c', -89.80, -1.60, -89.00 ,1.20, -93.40, 6.80}, 
{'c', -93.40, 6.80, -99.80 ,25.60, -97.80, 30.80}, 
{'c', -97.80, 30.80, -97.40 ,35.60, -100.20, 37.20}, 
{'c', -100.20, 37.20, -103.80 ,36.80, -95.40, 48.80}, 
{'c', -95.40, 48.80, -94.60 ,50.00, -97.80, 52.40}, 
{'c', -97.80, 52.40, -115.00 ,56.00, -117.40, 72.40}, 
{'c', -117.40, 72.40, -131.00 ,87.20, -131.00, 92.40}, 
{'c', -131.00, 94.70, -130.73 ,97.85, -130.03, 102.47}, 
{'c', -130.03, 102.47, -130.60 ,110.80, -103.00, 111.60}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', -115.60, 102.60, 0, 0, 0, 0}, 
{'c', -140.60, 63.20, -126.20 ,119.60, -126.20, 119.60}, 
{'c', -117.40, 154.00, 12.20 ,116.40, 12.20, 116.40}, 
{'c', 12.20, 116.40, 181.00 ,86.00, 192.20, 82.00}, 
{'c', 203.40, 78.00, 298.60 ,84.40, 298.60, 84.40}, 
{'l', 293.00, 67.60, 0, 0, 0, 0}, 
{'c', 228.20, 21.20, 209.00 ,44.40, 195.40, 40.40}, 
{'c', 181.80, 36.40, 184.20 ,46.00, 181.00, 46.80}, 
{'c', 177.80, 47.60, 138.60 ,22.80, 132.20, 23.60}, 
{'c', 125.80, 24.40, 100.46 ,0.65, 115.40, 32.40}, 
{'c', 131.40, 66.40, 57.00 ,71.60, 40.20, 60.40}, 
{'c', 23.40, 49.20, 47.40 ,78.80, 47.40, 78.80}, 
{'c', 65.80, 98.80, 31.40 ,82.00, 31.40, 82.00}, 
{'c', -3.00, 69.20, -27.00 ,94.80, -30.20, 95.60}, 
{'c', -33.40, 96.40, -38.20 ,99.60, -39.00, 93.20}, 
{'c', -39.80, 86.80, -47.31 ,70.10, -79.00, 96.40}, 
{'c', -99.00, 113.00, -112.80 ,91.00, -112.80, 91.00}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', 133.51, 25.35, 0, 0, 0, 0}, 
{'c', 127.11, 26.15, 101.74 ,2.41, 116.71, 34.15}, 
{'c', 133.31, 69.35, 58.31 ,73.35, 41.51, 62.15}, 
{'c', 24.71, 50.95, 48.71 ,80.55, 48.71, 80.55}, 
{'c', 67.11, 100.55, 32.71 ,83.75, 32.71, 83.75}, 
{'c', -1.69, 70.95, -25.69 ,96.55, -28.89, 97.35}, 
{'c', -32.09, 98.15, -36.89 ,101.35, -37.69, 94.95}, 
{'c', -38.49, 88.55, -45.87 ,72.01, -77.69, 98.15}, 
{'c', -98.93, 115.49, -112.42 ,94.04, -112.42, 94.04}, 
{'l', -115.62, 104.15, 0, 0, 0, 0}, 
{'c', -140.62, 64.35, -125.55 ,122.66, -125.55, 122.66}, 
{'c', -116.75, 157.06, 13.51 ,118.15, 13.51, 118.15}, 
{'c', 13.51, 118.15, 182.31 ,87.75, 193.51, 83.75}, 
{'c', 204.71, 79.75, 299.04 ,86.07, 299.04, 86.07}, 
{'l', 293.51, 68.76, 0, 0, 0, 0}, 
{'c', 228.71, 22.36, 210.31 ,46.15, 196.71, 42.15}, 
{'c', 183.11, 38.15, 185.51 ,47.75, 182.31, 48.55}, 
{'f', 0.938000,0.469000,0.201000,1.000000,0,0 }, 
{'m', 134.82, 27.09, 0, 0, 0, 0}, 
{'c', 128.42, 27.89, 103.69 ,3.86, 118.02, 35.89}, 
{'c', 134.22, 72.09, 59.62 ,75.09, 42.82, 63.89}, 
{'c', 26.02, 52.69, 50.02 ,82.29, 50.02, 82.29}, 
{'c', 68.42, 102.29, 34.02 ,85.49, 34.02, 85.49}, 
{'c', -0.38, 72.69, -24.38 ,98.29, -27.58, 99.09}, 
{'c', -30.78, 99.89, -35.58 ,103.09, -36.38, 96.69}, 
{'c', -37.18, 90.29, -44.43 ,73.92, -76.38, 99.89}, 
{'c', -98.86, 117.98, -112.04 ,97.07, -112.04, 97.07}, 
{'l', -115.64, 105.69, 0, 0, 0, 0}, 
{'c', -139.44, 66.69, -124.89 ,125.71, -124.89, 125.71}, 
{'c', -116.09, 160.11, 14.82 ,119.89, 14.82, 119.89}, 
{'c', 14.82, 119.89, 183.62 ,89.49, 194.82, 85.49}, 
{'c', 206.02, 81.49, 299.47 ,87.75, 299.47, 87.75}, 
{'l', 294.02, 69.93, 0, 0, 0, 0}, 
{'c', 229.22, 23.53, 211.62 ,47.89, 198.02, 43.89}, 
{'c', 184.42, 39.89, 186.82 ,49.49, 183.62, 50.29}, 
{'f', 0.938000,0.536000,0.268000,1.000000,0,0 }, 
{'m', 136.13, 28.84, 0, 0, 0, 0}, 
{'c', 129.73, 29.64, 105.00 ,5.61, 119.33, 37.64}, 
{'c', 136.13, 75.19, 60.39 ,76.48, 44.13, 65.64}, 
{'c', 27.33, 54.44, 51.33 ,84.04, 51.33, 84.04}, 
{'c', 69.73, 104.04, 35.33 ,87.24, 35.33, 87.24}, 
{'c', 0.93, 74.44, -23.07 ,100.04, -26.27, 100.84}, 
{'c', -29.47, 101.64, -34.27 ,104.84, -35.07, 98.44}, 
{'c', -35.87, 92.04, -42.99 ,75.84, -75.07, 101.64}, 
{'c', -98.78, 120.47, -111.66 ,100.11, -111.66, 100.11}, 
{'l', -115.66, 107.24, 0, 0, 0, 0}, 
{'c', -137.46, 70.44, -124.24 ,128.76, -124.24, 128.76}, 
{'c', -115.44, 163.16, 16.13 ,121.64, 16.13, 121.64}, 
{'c', 16.13, 121.64, 184.93 ,91.24, 196.13, 87.24}, 
{'c', 207.33, 83.24, 299.91 ,89.42, 299.91, 89.42}, 
{'l', 294.53, 71.09, 0, 0, 0, 0}, 
{'c', 229.73, 24.69, 212.93 ,49.64, 199.33, 45.64}, 
{'c', 185.73, 41.64, 188.13 ,51.24, 184.93, 52.04}, 
{'f', 0.938000,0.603000,0.402000,1.000000,0,0 }, 
{'m', 137.44, 30.58, 0, 0, 0, 0}, 
{'c', 131.04, 31.38, 106.81 ,7.13, 120.64, 39.38}, 
{'c', 137.44, 78.58, 62.24 ,78.58, 45.44, 67.38}, 
{'c', 28.64, 56.18, 52.64 ,85.78, 52.64, 85.78}, 
{'c', 71.04, 105.78, 36.64 ,88.98, 36.64, 88.98}, 
{'c', 2.24, 76.18, -21.76 ,101.78, -24.96, 102.58}, 
{'c', -28.16, 103.38, -32.96 ,106.58, -33.76, 100.18}, 
{'c', -34.56, 93.78, -41.55 ,77.75, -73.76, 103.38}, 
{'c', -98.71, 122.97, -111.27 ,103.15, -111.27, 103.15}, 
{'l', -115.67, 108.78, 0, 0, 0, 0}, 
{'c', -135.47, 73.98, -123.58 ,131.82, -123.58, 131.82}, 
{'c', -114.78, 166.22, 17.44 ,123.38, 17.44, 123.38}, 
{'c', 17.44, 123.38, 186.24 ,92.98, 197.44, 88.98}, 
{'c', 208.64, 84.98, 300.35 ,91.09, 300.35, 91.09}, 
{'l', 295.04, 72.25, 0, 0, 0, 0}, 
{'c', 230.24, 25.86, 214.24 ,51.38, 200.64, 47.38}, 
{'c', 187.04, 43.38, 189.44 ,52.98, 186.24, 53.78}, 
{'f', 0.938000,0.670000,0.469000,1.000000,0,0 }, 
{'m', 138.75, 32.33, 0, 0, 0, 0}, 
{'c', 132.35, 33.13, 106.38 ,9.68, 121.95, 41.13}, 
{'c', 141.15, 79.93, 63.55 ,80.33, 46.75, 69.13}, 
{'c', 29.95, 57.93, 53.95 ,87.53, 53.95, 87.53}, 
{'c', 72.35, 107.53, 37.95 ,90.73, 37.95, 90.73}, 
{'c', 3.55, 77.93, -20.45 ,103.53, -23.65, 104.33}, 
{'c', -26.85, 105.13, -31.65 ,108.33, -32.45, 101.93}, 
{'c', -33.25, 95.53, -40.11 ,79.67, -72.45, 105.13}, 
{'c', -98.64, 125.46, -110.89 ,106.18, -110.89, 106.18}, 
{'l', -115.69, 110.33, 0, 0, 0, 0}, 
{'c', -133.69, 77.13, -122.93 ,134.87, -122.93, 134.87}, 
{'c', -114.13, 169.27, 18.75 ,125.13, 18.75, 125.13}, 
{'c', 18.75, 125.13, 187.55 ,94.73, 198.75, 90.73}, 
{'c', 209.95, 86.73, 300.78 ,92.76, 300.78, 92.76}, 
{'l', 295.55, 73.42, 0, 0, 0, 0}, 
{'c', 230.75, 27.02, 215.55 ,53.13, 201.95, 49.13}, 
{'c', 188.35, 45.13, 190.75 ,54.73, 187.55, 55.53}, 
{'f', 1.000000,0.737000,0.536000,1.000000,0,0 }, 
{'m', 140.06, 34.07, 0, 0, 0, 0}, 
{'c', 133.66, 34.87, 107.31 ,11.61, 123.25, 42.87}, 
{'c', 143.66, 82.87, 64.86 ,82.07, 48.05, 70.87}, 
{'c', 31.25, 59.67, 55.26 ,89.27, 55.26, 89.27}, 
{'c', 73.66, 109.27, 39.26 ,92.47, 39.26, 92.47}, 
{'c', 4.86, 79.67, -19.14 ,105.27, -22.34, 106.07}, 
{'c', -25.55, 106.87, -30.34 ,110.07, -31.14, 103.67}, 
{'c', -31.95, 97.27, -38.67 ,81.58, -71.14, 106.87}, 
{'c', -98.56, 127.95, -110.51 ,109.22, -110.51, 109.22}, 
{'l', -115.71, 111.87, 0, 0, 0, 0}, 
{'c', -131.71, 81.67, -122.27 ,137.93, -122.27, 137.93}, 
{'c', -113.47, 172.33, 20.05 ,126.87, 20.05, 126.87}, 
{'c', 20.05, 126.87, 188.86 ,96.47, 200.06, 92.47}, 
{'c', 211.26, 88.47, 301.22 ,94.44, 301.22, 94.44}, 
{'l', 296.06, 74.58, 0, 0, 0, 0}, 
{'c', 231.26, 28.18, 216.86 ,54.87, 203.26, 50.87}, 
{'c', 189.66, 46.87, 192.06 ,56.47, 188.86, 57.27}, 
{'f', 1.000000,0.737000,0.603000,1.000000,0,0 }, 
{'m', 141.37, 35.82, 0, 0, 0, 0}, 
{'c', 134.97, 36.62, 107.52 ,13.94, 124.56, 44.62}, 
{'c', 146.56, 84.22, 66.16 ,83.82, 49.36, 72.62}, 
{'c', 32.56, 61.42, 56.56 ,91.02, 56.56, 91.02}, 
{'c', 74.96, 111.02, 40.56 ,94.22, 40.56, 94.22}, 
{'c', 6.16, 81.42, -17.84 ,107.02, -21.04, 107.82}, 
{'c', -24.24, 108.62, -29.04 ,111.82, -29.84, 105.42}, 
{'c', -30.64, 99.02, -37.23 ,83.49, -69.84, 108.62}, 
{'c', -98.49, 130.44, -110.13 ,112.26, -110.13, 112.26}, 
{'l', -115.73, 113.42, 0, 0, 0, 0}, 
{'c', -130.13, 85.02, -121.62 ,140.98, -121.62, 140.98}, 
{'c', -112.82, 175.38, 21.36 ,128.62, 21.36, 128.62}, 
{'c', 21.36, 128.62, 190.16 ,98.22, 201.37, 94.22}, 
{'c', 212.56, 90.22, 301.66 ,96.11, 301.66, 96.11}, 
{'l', 296.56, 75.75, 0, 0, 0, 0}, 
{'c', 231.76, 29.35, 218.16 ,56.62, 204.56, 52.62}, 
{'c', 190.97, 48.62, 193.37 ,58.22, 190.16, 59.02}, 
{'f', 1.000000,0.804000,0.737000,1.000000,0,0 }, 
{'m', 142.67, 37.56, 0, 0, 0, 0}, 
{'c', 136.27, 38.37, 108.83 ,15.69, 125.87, 46.37}, 
{'c', 147.87, 85.97, 67.47 ,85.56, 50.67, 74.36}, 
{'c', 33.87, 63.16, 57.87 ,92.77, 57.87, 92.77}, 
{'c', 76.27, 112.77, 41.87 ,95.97, 41.87, 95.97}, 
{'c', 7.47, 83.17, -16.53 ,108.77, -19.73, 109.56}, 
{'c', -22.93, 110.36, -27.73 ,113.56, -28.53, 107.17}, 
{'c', -29.33, 100.77, -35.79 ,85.41, -68.53, 110.36}, 
{'c', -98.42, 132.93, -109.75 ,115.29, -109.75, 115.29}, 
{'l', -115.75, 114.97, 0, 0, 0, 0}, 
{'c', -129.35, 88.56, -120.96 ,144.04, -120.96, 144.04}, 
{'c', -112.16, 178.44, 22.67 ,130.37, 22.67, 130.37}, 
{'c', 22.67, 130.37, 191.47 ,99.97, 202.67, 95.97}, 
{'c', 213.87, 91.97, 302.09 ,97.78, 302.09, 97.78}, 
{'l', 297.07, 76.91, 0, 0, 0, 0}, 
{'c', 232.27, 30.51, 219.47 ,58.37, 205.87, 54.37}, 
{'c', 192.27, 50.37, 194.67 ,59.97, 191.47, 60.77}, 
{'f', 1.000000,0.871000,0.804000,1.000000,0,0 }, 
{'m', 143.98, 39.31, 0, 0, 0, 0}, 
{'c', 137.58, 40.11, 110.53 ,17.22, 127.18, 48.11}, 
{'c', 149.18, 88.91, 68.78 ,87.31, 51.98, 76.11}, 
{'c', 35.18, 64.91, 59.18 ,94.51, 59.18, 94.51}, 
{'c', 77.58, 114.51, 43.18 ,97.71, 43.18, 97.71}, 
{'c', 8.78, 84.91, -15.22 ,110.51, -18.42, 111.31}, 
{'c', -21.62, 112.11, -26.42 ,115.31, -27.22, 108.91}, 
{'c', -28.02, 102.51, -34.35 ,87.32, -67.22, 112.11}, 
{'c', -98.34, 135.42, -109.36 ,118.33, -109.36, 118.33}, 
{'l', -115.76, 116.51, 0, 0, 0, 0}, 
{'c', -128.76, 92.51, -120.31 ,147.09, -120.31, 147.09}, 
{'c', -111.51, 181.49, 23.98 ,132.11, 23.98, 132.11}, 
{'c', 23.98, 132.11, 192.78 ,101.71, 203.98, 97.71}, 
{'c', 215.18, 93.71, 302.53 ,99.46, 302.53, 99.46}, 
{'l', 297.58, 78.07, 0, 0, 0, 0}, 
{'c', 232.78, 31.67, 220.78 ,60.11, 207.18, 56.11}, 
{'c', 193.58, 52.11, 195.98 ,61.71, 192.78, 62.51}, 
{'f', 1.000000,0.938000,0.871000,1.000000,0,0 }, 
{'m', 145.29, 41.05, 0, 0, 0, 0}, 
{'c', 138.89, 41.85, 112.92 ,18.41, 128.49, 49.85}, 
{'c', 149.69, 92.66, 70.09 ,89.06, 53.29, 77.86}, 
{'c', 36.49, 66.66, 60.49 ,96.26, 60.49, 96.26}, 
{'c', 78.89, 116.26, 44.49 ,99.46, 44.49, 99.46}, 
{'c', 10.09, 86.66, -13.91 ,112.26, -17.11, 113.06}, 
{'c', -20.31, 113.86, -25.11 ,117.06, -25.91, 110.66}, 
{'c', -26.71, 104.26, -32.91 ,89.23, -65.91, 113.86}, 
{'c', -98.27, 137.91, -108.98 ,121.36, -108.98, 121.36}, 
{'l', -115.78, 118.06, 0, 0, 0, 0}, 
{'c', -128.58, 94.86, -119.65 ,150.15, -119.65, 150.15}, 
{'c', -110.85, 184.55, 25.29 ,133.86, 25.29, 133.86}, 
{'c', 25.29, 133.86, 194.09 ,103.46, 205.29, 99.46}, 
{'c', 216.49, 95.46, 302.96 ,101.13, 302.96, 101.13}, 
{'l', 298.09, 79.24, 0, 0, 0, 0}, 
{'c', 233.29, 32.84, 222.09 ,61.86, 208.49, 57.86}, 
{'c', 194.89, 53.85, 197.29 ,63.46, 194.09, 64.26}, 
{'f', 1.000000,1.000000,0.938000,1.000000,0,0 }, 
{'m', -115.80, 119.60, 0, 0, 0, 0}, 
{'c', -128.60, 97.60, -119.00 ,153.20, -119.00, 153.20}, 
{'c', -110.20, 187.60, 26.60 ,135.60, 26.60, 135.60}, 
{'c', 26.60, 135.60, 195.40 ,105.20, 206.60, 101.20}, 
{'c', 217.80, 97.20, 303.40 ,102.80, 303.40, 102.80}, 
{'l', 298.60, 80.40, 0, 0, 0, 0}, 
{'c', 233.80, 34.00, 223.40 ,63.60, 209.80, 59.60}, 
{'c', 196.20, 55.60, 198.60 ,65.20, 195.40, 66.00}, 
{'c', 192.20, 66.80, 153.00 ,42.00, 146.60, 42.80}, 
{'c', 140.20, 43.60, 114.98 ,19.79, 129.80, 51.60}, 
{'c', 152.03, 99.31, 69.04 ,89.23, 54.60, 79.60}, 
{'c', 37.80, 68.40, 61.80 ,98.00, 61.80, 98.00}, 
{'c', 80.20, 118.00, 45.80 ,101.20, 45.80, 101.20}, 
{'c', 11.40, 88.40, -12.60 ,114.00, -15.80, 114.80}, 
{'c', -19.00, 115.60, -23.80 ,118.80, -24.60, 112.40}, 
{'c', -25.40, 106.00, -31.46 ,91.14, -64.60, 115.60}, 
{'c', -98.20, 140.40, -108.60 ,124.40, -108.60, 124.40}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -74.20, 149.60, 0, 0, 0, 0}, 
{'c', -74.20, 149.60, -81.40 ,161.20, -60.60, 174.40}, 
{'c', -60.60, 174.40, -59.20 ,175.80, -77.20, 171.60}, 
{'c', -77.20, 171.60, -83.40 ,169.60, -85.00, 159.20}, 
{'c', -85.00, 159.20, -89.80 ,154.80, -94.60, 149.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 65.80, 102.00, 0, 0, 0, 0}, 
{'c', 65.80, 102.00, 83.50 ,128.82, 82.90, 133.60}, 
{'c', 81.60, 144.00, 81.40 ,153.60, 84.60, 157.60}, 
{'c', 87.80, 161.60, 96.60 ,194.80, 96.60, 194.80}, 
{'c', 96.60, 194.80, 96.20 ,196.00, 108.60, 158.00}, 
{'c', 108.60, 158.00, 120.20 ,142.00, 100.20, 123.60}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -54.20, 176.40, 0, 0, 0, 0}, 
{'c', -54.20, 176.40, -43.00 ,183.60, -57.40, 214.80}, 
{'l', -51.00, 212.40, 0, 0, 0, 0}, 
{'c', -51.00, 212.40, -51.80 ,223.60, -55.00, 226.00}, 
{'l', -47.80, 222.80, 0, 0, 0, 0}, 
{'c', -47.80, 222.80, -43.00 ,230.80, -47.00, 235.60}, 
{'c', -47.00, 235.60, -30.20 ,243.60, -31.00, 250.00}, 
{'c', -31.00, 250.00, -24.60 ,242.00, -28.60, 235.60}, 
{'c', -32.60, 229.20, -39.80 ,233.20, -39.00, 214.80}, 
{'l', -47.80, 218.00, 0, 0, 0, 0}, 
{'c', -47.80, 218.00, -42.20 ,209.20, -42.20, 202.80}, 
{'l', -50.20, 205.20, 0, 0, 0, 0}, 
{'c', -50.20, 205.20, -34.73 ,178.62, -45.40, 177.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -21.80, 193.20, 0, 0, 0, 0}, 
{'c', -21.80, 193.20, -19.00 ,188.80, -21.80, 189.60}, 
{'c', -24.60, 190.40, -55.80 ,205.20, -61.80, 214.80}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -11.40, 201.20, 0, 0, 0, 0}, 
{'c', -11.40, 201.20, -8.60 ,196.80, -11.40, 197.60}, 
{'c', -14.20, 198.40, -45.40 ,213.20, -51.40, 222.80}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 1.80, 186.00, 0, 0, 0, 0}, 
{'c', 1.80, 186.00, 4.60 ,181.60, 1.80, 182.40}, 
{'c', -1.00, 183.20, -32.20 ,198.00, -38.20, 207.60}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -21.40, 229.60, 0, 0, 0, 0}, 
{'c', -21.40, 229.60, -21.40 ,223.60, -24.20, 224.40}, 
{'c', -27.00, 225.20, -63.00 ,242.80, -69.00, 252.40}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -20.20, 218.80, 0, 0, 0, 0}, 
{'c', -20.20, 218.80, -19.00 ,214.00, -21.80, 214.80}, 
{'c', -23.80, 214.80, -50.20 ,226.40, -56.20, 236.00}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -34.60, 266.40, 0, 0, 0, 0}, 
{'l', -44.60, 274.00, 0, 0, 0, 0}, 
{'c', -44.60, 274.00, -34.20 ,266.40, -30.60, 267.60}, 
{'c', -30.60, 267.60, -37.40 ,278.80, -38.20, 284.00}, 
{'c', -38.20, 284.00, -27.80 ,271.20, -22.20, 271.60}, 
{'c', -22.20, 271.60, -14.60 ,272.00, -14.60, 282.80}, 
{'c', -14.60, 282.80, -9.00 ,272.40, -5.80, 272.80}, 
{'c', -5.80, 272.80, -4.60 ,279.20, -5.80, 286.00}, 
{'c', -5.80, 286.00, -1.80 ,278.40, 2.20, 280.00}, 
{'c', 2.20, 280.00, 8.60 ,278.00, 7.80, 289.60}, 
{'c', 7.80, 289.60, 7.80 ,300.00, 7.00, 302.80}, 
{'c', 7.00, 302.80, 12.60 ,276.40, 15.00, 276.00}, 
{'c', 15.00, 276.00, 23.00 ,274.80, 27.80, 283.60}, 
{'c', 27.80, 283.60, 23.80 ,276.00, 28.60, 278.00}, 
{'c', 28.60, 278.00, 39.40 ,279.60, 42.60, 286.40}, 
{'c', 42.60, 286.40, 35.80 ,274.40, 41.40, 277.60}, 
{'c', 41.40, 277.60, 48.20 ,277.60, 49.40, 284.00}, 
{'c', 49.40, 284.00, 57.80 ,305.20, 59.80, 306.80}, 
{'c', 59.80, 306.80, 52.20 ,285.20, 53.80, 285.20}, 
{'c', 53.80, 285.20, 51.80 ,273.20, 57.00, 288.00}, 
{'c', 57.00, 288.00, 53.80 ,274.00, 59.40, 274.80}, 
{'c', 65.00, 275.60, 69.40 ,285.60, 77.80, 283.20}, 
{'c', 77.80, 283.20, 87.40 ,288.80, 89.40, 219.60}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -29.80, 173.60, 0, 0, 0, 0}, 
{'c', -29.80, 173.60, -15.00 ,167.60, 25.00, 173.60}, 
{'c', 25.00, 173.60, 32.20 ,174.00, 39.00, 165.20}, 
{'c', 45.80, 156.40, 72.60 ,149.20, 79.00, 151.20}, 
{'l', 88.60, 157.60, 0, 0, 0, 0}, 
{'l', 89.40, 158.80, 0, 0, 0, 0}, 
{'c', 89.40, 158.80, 101.80 ,169.20, 102.20, 176.80}, 
{'c', 102.60, 184.40, 87.80 ,232.40, 78.20, 248.40}, 
{'c', 68.60, 264.40, 59.00 ,276.80, 39.80, 274.40}, 
{'c', 39.80, 274.40, 19.00 ,270.40, -6.60, 274.40}, 
{'c', -6.60, 274.40, -35.80 ,272.80, -38.60, 264.80}, 
{'c', -41.40, 256.80, -27.40 ,241.60, -27.40, 241.60}, 
{'c', -27.40, 241.60, -23.00 ,233.20, -24.20, 218.80}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -7.80, 175.60, 0, 0, 0, 0}, 
{'c', 0.60, 194.00, -29.00 ,259.20, -29.00, 259.20}, 
{'c', -31.00, 260.80, -16.34 ,266.85, -6.20, 264.40}, 
{'c', 4.75, 261.76, 45.00 ,266.00, 45.00, 266.00}, 
{'c', 68.60, 250.40, 81.40 ,206.00, 81.40, 206.00}, 
{'c', 81.40, 206.00, 91.80 ,182.00, 74.20, 178.80}, 
{'f', 0.938000,0.402000,0.536000,1.000000,0,0 }, 
{'m', -9.83, 206.50, 0, 0, 0, 0}, 
{'c', -6.50, 193.71, -4.92 ,181.91, -7.80, 175.60}, 
{'c', -7.80, 175.60, 54.60 ,182.00, 65.80, 161.20}, 
{'c', 70.04, 153.33, 84.80 ,184.00, 84.40, 193.60}, 
{'c', 84.40, 193.60, 21.40 ,208.00, 6.60, 196.80}, 
{'f', 0.737000,0.201000,0.335000,1.000000,0,0 }, 
{'m', -5.40, 222.80, 0, 0, 0, 0}, 
{'c', -5.40, 222.80, -3.40 ,230.00, -5.80, 234.00}, 
{'c', -5.80, 234.00, -7.40 ,234.80, -8.60, 235.20}, 
{'c', -8.60, 235.20, -7.40 ,238.80, -1.40, 240.40}, 
{'c', -1.40, 240.40, 0.60 ,244.80, 3.00, 245.20}, 
{'c', 5.40, 245.60, 10.20 ,251.20, 14.20, 250.00}, 
{'c', 18.20, 248.80, 29.40 ,244.80, 29.40, 244.80}, 
{'c', 29.40, 244.80, 35.00 ,241.60, 43.80, 245.20}, 
{'c', 43.80, 245.20, 46.17 ,244.40, 46.60, 240.40}, 
{'c', 47.10, 235.70, 50.20 ,232.00, 52.20, 230.00}, 
{'c', 54.20, 228.00, 63.80 ,215.20, 62.60, 214.80}, 
{'f', 0.670000,0.134000,0.268000,1.000000,0,0 }, 
{'m', -9.80, 174.40, 0, 0, 0, 0}, 
{'c', -9.80, 174.40, -12.60 ,196.80, -9.40, 205.20}, 
{'c', -6.20, 213.60, -7.00 ,215.60, -7.80, 219.60}, 
{'c', -8.60, 223.60, -4.20 ,233.60, 1.40, 239.60}, 
{'l', 13.40, 241.20, 0, 0, 0, 0}, 
{'c', 13.40, 241.20, 28.60 ,237.60, 37.80, 240.40}, 
{'c', 37.80, 240.40, 46.79 ,241.74, 50.20, 226.80}, 
{'c', 50.20, 226.80, 55.00 ,220.40, 62.20, 217.60}, 
{'c', 69.40, 214.80, 76.60 ,173.20, 72.60, 165.20}, 
{'c', 68.60, 157.20, 54.20 ,152.80, 38.20, 168.40}, 
{'f', 1.000000,0.469000,0.469000,1.000000,0,0 }, 
{'m', -8.20, 249.20, 0, 0, 0, 0}, 
{'c', -8.20, 249.20, -9.00 ,247.20, -13.40, 246.80}, 
{'c', -13.40, 246.80, -35.80 ,243.20, -44.20, 230.80}, 
{'c', -44.20, 230.80, -51.00 ,225.20, -46.60, 236.80}, 
{'c', -46.60, 236.80, -36.20 ,257.20, -29.40, 260.00}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', 71.74, 185.23, 0, 0, 0, 0}, 
{'c', 72.40, 177.32, 74.35 ,168.71, 72.60, 165.20}, 
{'c', 66.15, 152.31, 49.18 ,157.69, 38.20, 168.40}, 
{'c', 22.20, 184.00, 20.20 ,167.20, -9.80, 174.40}, 
{'c', -9.80, 174.40, -11.54 ,188.36, -10.71, 198.38}, 
{'c', -10.71, 198.38, 26.60 ,186.80, 27.40, 192.40}, 
{'c', 27.40, 192.40, 29.00 ,189.20, 38.20, 189.20}, 
{'f', 0.804000,0.201000,0.268000,1.000000,0,0 }, 
{'m', 28.60, 175.20, 0, 0, 0, 0}, 
{'c', 28.60, 175.20, 33.40 ,180.00, 29.80, 189.60}, 
{'f', 0.804000,0.201000,0.268000,1.000000,0,0 }, 
{'m', -19.40, 260.00, 0, 0, 0, 0}, 
{'c', -19.40, 260.00, -23.80 ,247.20, -15.00, 254.00}, 
{'c', -15.00, 254.00, -10.20 ,256.00, -11.40, 257.60}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -14.36, 261.20, 0, 0, 0, 0}, 
{'c', -14.36, 261.20, -17.88 ,250.96, -10.84, 256.40}, 
{'c', -10.84, 256.40, -6.42 ,258.85, -7.96, 259.28}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -9.56, 261.20, 0, 0, 0, 0}, 
{'c', -9.56, 261.20, -13.08 ,250.96, -6.04, 256.40}, 
{'c', -6.04, 256.40, -1.67 ,258.71, -3.16, 259.28}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -2.96, 261.40, 0, 0, 0, 0}, 
{'c', -2.96, 261.40, -6.48 ,251.16, 0.56, 256.60}, 
{'c', 0.56, 256.60, 4.94 ,258.93, 3.44, 259.48}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', 3.52, 261.32, 0, 0, 0, 0}, 
{'c', 3.52, 261.32, 0.00 ,251.08, 7.04, 256.52}, 
{'c', 7.04, 256.52, 10.88 ,258.12, 9.92, 259.40}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', 10.20, 262.00, 0, 0, 0, 0}, 
{'c', 10.20, 262.00, 5.40 ,249.60, 14.60, 256.00}, 
{'c', 14.60, 256.00, 19.40 ,258.00, 18.20, 259.60}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -18.20, 244.80, 0, 0, 0, 0}, 
{'c', -18.20, 244.80, -5.00 ,242.00, 1.00, 245.20}, 
{'c', 1.00, 245.20, 7.00 ,246.40, 8.20, 246.00}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', 15.80, 253.60, 0, 0, 0, 0}, 
{'c', 15.80, 253.60, 27.80 ,240.00, 39.80, 244.40}, 
{'c', 46.82, 246.97, 45.80 ,243.60, 46.60, 240.80}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', 33.00, 237.60, 0, 0, 0, 0}, 
{'c', 33.00, 237.60, 29.00 ,226.80, 26.20, 239.60}, 
{'c', 23.40, 252.40, 20.20 ,256.00, 18.60, 258.80}, 
{'c', 18.60, 258.80, 18.60 ,264.00, 27.00, 263.60}, 
{'c', 27.00, 263.60, 37.80 ,263.20, 38.20, 260.40}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', 47.00, 244.80, 0, 0, 0, 0}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', 53.50, 228.40, 0, 0, 0, 0}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -25.80, 265.20, 0, 0, 0, 0}, 
{'c', -25.80, 265.20, -7.80 ,268.40, -3.40, 266.80}, 
{'c', -3.40, 266.80, 5.40 ,266.80, -3.00, 268.80}, 
{'c', -3.00, 268.80, -15.80 ,268.80, -23.80, 267.60}, 
{'f', 0.737000,0.737000,0.737000,1.000000,0,0 }, 
{'m', -11.80, 172.00, 0, 0, 0, 0}, 
{'c', -11.80, 172.00, 5.80 ,172.00, 7.80, 172.80}, 
{'c', 7.80, 172.80, 15.00 ,203.60, 11.40, 211.20}, 
{'c', 11.40, 211.20, 10.20 ,214.00, 7.40, 208.40}, 
{'c', 7.40, 208.40, -11.00 ,175.60, -14.20, 173.60}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -88.90, 169.30, 0, 0, 0, 0}, 
{'c', -88.90, 169.30, -80.00 ,171.00, -67.40, 173.60}, 
{'c', -67.40, 173.60, -62.60 ,196.00, -59.40, 200.80}, 
{'c', -56.20, 205.60, -59.80 ,205.60, -63.40, 202.80}, 
{'c', -67.00, 200.00, -81.80 ,186.00, -83.80, 181.60}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -67.04, 173.82, 0, 0, 0, 0}, 
{'c', -67.04, 173.82, -61.24 ,175.37, -60.23, 177.58}, 
{'c', -59.22, 179.79, -61.43 ,183.09, -61.43, 183.09}, 
{'c', -61.43, 183.09, -62.43 ,186.40, -63.63, 184.24}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -67.00, 173.60, 0, 0, 0, 0}, 
{'c', -67.00, 173.60, -63.40 ,178.80, -59.80, 178.80}, 
{'c', -56.20, 178.80, -55.82 ,178.39, -53.00, 179.00}, 
{'c', -48.40, 180.00, -48.80 ,178.00, -42.20, 179.20}, 
{'c', -39.56, 179.68, -37.00 ,178.80, -34.20, 180.00}, 
{'c', -31.40, 181.20, -28.20 ,180.40, -27.00, 178.40}, 
{'c', -25.80, 176.40, -21.00 ,172.20, -21.00, 172.20}, 
{'c', -21.00, 172.20, -33.80 ,174.00, -36.60, 174.80}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -22.40, 173.80, 0, 0, 0, 0}, 
{'c', -22.40, 173.80, -28.85 ,177.30, -29.25, 179.70}, 
{'c', -29.65, 182.10, -24.00 ,185.80, -24.00, 185.80}, 
{'c', -24.00, 185.80, -21.25 ,190.40, -20.65, 188.00}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -59.88, 179.26, 0, 0, 0, 0}, 
{'c', -59.88, 179.26, -52.88 ,190.45, -52.66, 179.24}, 
{'c', -52.66, 179.24, -52.10 ,177.98, -53.86, 177.96}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -52.71, 179.51, 0, 0, 0, 0}, 
{'c', -52.71, 179.51, -44.79 ,190.70, -45.42, 179.42}, 
{'c', -45.42, 179.42, -45.41 ,179.09, -47.17, 178.94}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -45.49, 179.52, 0, 0, 0, 0}, 
{'c', -45.49, 179.52, -37.53 ,190.15, -38.20, 180.48}, 
{'c', -38.20, 180.48, -38.08 ,179.25, -39.74, 178.95}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -38.62, 179.60, 0, 0, 0, 0}, 
{'c', -38.62, 179.60, -30.72 ,191.16, -30.37, 181.38}, 
{'c', -30.37, 181.38, -28.73 ,180.00, -30.47, 179.78}, 
{'f', 1.000000,1.000000,0.804000,1.000000,0,0 }, 
{'m', -74.79, 183.13, 0, 0, 0, 0}, 
{'l', -82.45, 181.60, 0, 0, 0, 0}, 
{'c', -85.05, 176.60, -87.15 ,170.45, -87.15, 170.45}, 
{'c', -87.15, 170.45, -80.80 ,171.45, -68.30, 174.25}, 
{'c', -68.30, 174.25, -67.42 ,177.57, -65.95, 183.36}, 
{'f', 0.938000,0.938000,0.737000,1.000000,0,0 }, 
{'m', -9.72, 178.47, 0, 0, 0, 0}, 
{'c', -11.39, 175.96, -12.71 ,174.21, -13.36, 173.80}, 
{'c', -16.37, 171.92, -12.23 ,172.29, -11.10, 172.29}, 
{'c', -11.10, 172.29, 5.47 ,172.29, 7.36, 173.05}, 
{'c', 7.36, 173.05, 7.88 ,175.29, 8.56, 178.68}, 
{'f', 0.938000,0.938000,0.737000,1.000000,0,0 }, 
{'m', 43.88, 40.32, 0, 0, 0, 0}, 
{'c', 71.60, 44.28, 97.12 ,8.64, 98.88, -1.04}, 
{'c', 100.64, -10.72, 90.52 ,-22.60, 90.52, -22.60}, 
{'c', 91.84, -25.68, 87.00 ,-39.76, 81.72, -49.00}, 
{'c', 76.44, -58.24, 60.54 ,-57.27, 43.00, -58.24}, 
{'c', 27.16, -59.12, 8.68 ,-35.80, 7.36, -34.04}, 
{'c', 6.04, -32.28, 12.20 ,6.00, 13.52, 11.72}, 
{'c', 14.84, 17.44, 12.20 ,43.84, 12.20, 43.84}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', 8.09, -33.39, 0, 0, 0, 0}, 
{'c', 6.79, -31.66, 12.84 ,5.92, 14.14, 11.54}, 
{'c', 15.43, 17.15, 12.84 ,43.07, 12.84, 43.07}, 
{'c', 45.51, 34.19, 16.73 ,35.73, 43.94, 39.62}, 
{'c', 71.16, 43.51, 96.22 ,8.51, 97.94, -0.99}, 
{'c', 99.67, -10.50, 89.74 ,-22.16, 89.74, -22.16}, 
{'c', 91.03, -25.18, 86.28 ,-39.01, 81.10, -48.08}, 
{'c', 75.91, -57.15, 60.30 ,-56.20, 43.08, -57.15}, 
{'f', 0.938000,0.536000,0.335000,1.000000,0,0 }, 
{'m', 8.82, -32.74, 0, 0, 0, 0}, 
{'c', 7.54, -31.05, 13.48 ,5.84, 14.75, 11.35}, 
{'c', 16.02, 16.86, 13.48 ,42.30, 13.48, 42.30}, 
{'c', 44.88, 33.15, 17.30 ,35.10, 44.01, 38.91}, 
{'c', 70.72, 42.73, 95.31 ,8.38, 97.01, -0.94}, 
{'c', 98.70, -10.27, 88.95 ,-21.72, 88.95, -21.72}, 
{'c', 90.22, -24.69, 85.56 ,-38.26, 80.47, -47.16}, 
{'c', 75.39, -56.06, 60.06 ,-55.12, 43.16, -56.06}, 
{'f', 0.938000,0.670000,0.469000,1.000000,0,0 }, 
{'m', 9.54, -32.10, 0, 0, 0, 0}, 
{'c', 8.30, -30.43, 14.12 ,5.76, 15.37, 11.17}, 
{'c', 16.62, 16.58, 14.12 ,41.54, 14.12, 41.54}, 
{'c', 43.56, 32.50, 17.86 ,34.47, 44.07, 38.21}, 
{'c', 70.28, 41.95, 94.41 ,8.26, 96.07, -0.90}, 
{'c', 97.74, -10.05, 88.17 ,-21.28, 88.17, -21.28}, 
{'c', 89.42, -24.19, 84.84 ,-37.50, 79.85, -46.24}, 
{'c', 74.86, -54.98, 59.82 ,-54.05, 43.24, -54.98}, 
{'f', 1.000000,0.804000,0.670000,1.000000,0,0 }, 
{'m', 10.27, -31.45, 0, 0, 0, 0}, 
{'c', 9.05, -29.82, 14.76 ,5.68, 15.98, 10.98}, 
{'c', 17.21, 16.29, 14.76 ,40.77, 14.76, 40.77}, 
{'c', 42.63, 31.85, 18.43 ,33.83, 44.14, 37.51}, 
{'c', 69.84, 41.18, 93.50 ,8.13, 95.14, -0.85}, 
{'c', 96.77, -9.82, 87.39 ,-20.84, 87.39, -20.84}, 
{'c', 88.61, -23.70, 84.12 ,-36.75, 79.22, -45.32}, 
{'c', 74.33, -53.89, 59.59 ,-52.98, 43.32, -53.89}, 
{'f', 1.000000,0.938000,0.871000,1.000000,0,0 }, 
{'m', 44.20, 36.80, 0, 0, 0, 0}, 
{'c', 69.40, 40.40, 92.60 ,8.00, 94.20, -0.80}, 
{'c', 95.80, -9.60, 86.60 ,-20.40, 86.60, -20.40}, 
{'c', 87.80, -23.20, 83.40 ,-36.00, 78.60, -44.40}, 
{'c', 73.80, -52.80, 59.35 ,-51.91, 43.40, -52.80}, 
{'c', 29.00, -53.60, 12.20 ,-32.40, 11.00, -30.80}, 
{'c', 9.80, -29.20, 15.40 ,5.60, 16.60, 10.80}, 
{'c', 17.80, 16.00, 15.40 ,40.00, 15.40, 40.00}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 90.60, 2.80, 0, 0, 0, 0}, 
{'c', 90.60, 2.80, 62.80 ,10.40, 51.20, 8.80}, 
{'c', 51.20, 8.80, 35.40 ,2.20, 26.60, 24.00}, 
{'c', 26.60, 24.00, 23.00 ,31.20, 21.00, 33.20}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 94.40, 0.60, 0, 0, 0, 0}, 
{'c', 94.40, 0.60, 65.40 ,12.80, 55.40, 12.40}, 
{'c', 55.40, 12.40, 39.00 ,7.80, 30.60, 22.40}, 
{'c', 30.60, 22.40, 22.20 ,31.60, 19.00, 33.20}, 
{'c', 19.00, 33.20, 18.60 ,34.80, 25.00, 30.80}, 
{'l', 35.40, 36.00, 0, 0, 0, 0}, 
{'c', 35.40, 36.00, 50.20 ,45.60, 59.80, 29.60}, 
{'c', 59.80, 29.60, 63.80 ,18.40, 63.80, 16.40}, 
{'c', 63.80, 14.40, 85.00 ,8.80, 86.60, 8.40}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 47.00, 36.51, 0, 0, 0, 0}, 
{'c', 40.13, 36.51, 31.75 ,32.65, 31.75, 26.40}, 
{'c', 31.75, 20.15, 40.13 ,13.89, 47.00, 13.89}, 
{'c', 53.87, 13.89, 59.45 ,18.95, 59.45, 25.20}, 
{'f', 0.603000,0.804000,0.201000,1.000000,0,0 }, 
{'m', 43.38, 19.83, 0, 0, 0, 0}, 
{'c', 38.53, 20.55, 33.44 ,22.05, 33.51, 21.84}, 
{'c', 35.05, 17.22, 41.41 ,13.89, 47.00, 13.89}, 
{'c', 51.30, 13.89, 55.08 ,15.87, 57.32, 18.88}, 
{'f', 0.402000,0.603000,0.000000,1.000000,0,0 }, 
{'m', 55.40, 19.60, 0, 0, 0, 0}, 
{'c', 55.40, 19.60, 51.00 ,16.40, 51.00, 18.60}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 45.40, 27.73, 0, 0, 0, 0}, 
{'c', 42.90, 27.73, 40.88 ,25.70, 40.88, 23.20}, 
{'c', 40.88, 20.70, 42.90 ,18.68, 45.40, 18.68}, 
{'c', 47.90, 18.68, 49.93 ,20.70, 49.93, 23.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -58.60, 14.40, 0, 0, 0, 0}, 
{'c', -58.60, 14.40, -61.80 ,-6.80, -59.40, -11.20}, 
{'c', -59.40, -11.20, -48.60 ,-21.20, -49.00, -24.80}, 
{'c', -49.00, -24.80, -49.40 ,-42.80, -50.60, -43.60}, 
{'c', -51.80, -44.40, -59.40 ,-50.40, -65.40, -44.00}, 
{'c', -65.40, -44.00, -75.80 ,-26.00, -75.00, -19.60}, 
{'l', -75.00, -17.60, 0, 0, 0, 0}, 
{'c', -75.00, -17.60, -82.60 ,-18.00, -84.20, -16.00}, 
{'c', -84.20, -16.00, -85.40 ,-10.80, -86.60, -10.40}, 
{'c', -86.60, -10.40, -89.40 ,-8.00, -87.40, -5.20}, 
{'c', -87.40, -5.20, -89.40 ,-2.80, -89.00, 1.20}, 
{'l', -81.40, 5.20, 0, 0, 0, 0}, 
{'c', -81.40, 5.20, -79.40 ,19.60, -68.60, 24.80}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', -59.60, 12.56, 0, 0, 0, 0}, 
{'c', -59.60, 12.56, -62.48 ,-6.52, -60.32, -10.48}, 
{'c', -60.32, -10.48, -50.60 ,-19.48, -50.96, -22.72}, 
{'c', -50.96, -22.72, -51.32 ,-38.92, -52.40, -39.64}, 
{'c', -53.48, -40.36, -60.32 ,-45.76, -65.72, -40.00}, 
{'c', -65.72, -40.00, -75.08 ,-23.80, -74.36, -18.04}, 
{'l', -74.36, -16.24, 0, 0, 0, 0}, 
{'c', -74.36, -16.24, -81.20 ,-16.60, -82.64, -14.80}, 
{'c', -82.64, -14.80, -83.72 ,-10.12, -84.80, -9.76}, 
{'c', -84.80, -9.76, -87.32 ,-7.60, -85.52, -5.08}, 
{'c', -85.52, -5.08, -87.32 ,-2.92, -86.96, 0.68}, 
{'l', -80.12, 4.28, 0, 0, 0, 0}, 
{'c', -80.12, 4.28, -78.32 ,17.24, -68.60, 21.92}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -51.05, -42.61, 0, 0, 0, 0}, 
{'c', -52.14, -43.47, -59.63 ,-49.24, -65.48, -43.00}, 
{'c', -65.48, -43.00, -75.62 ,-25.45, -74.84, -19.21}, 
{'l', -74.84, -17.26, 0, 0, 0, 0}, 
{'c', -74.84, -17.26, -82.25 ,-17.65, -83.81, -15.70}, 
{'c', -83.81, -15.70, -84.98 ,-10.63, -86.15, -10.24}, 
{'c', -86.15, -10.24, -88.88 ,-7.90, -86.93, -5.17}, 
{'c', -86.93, -5.17, -88.88 ,-2.83, -88.49, 1.07}, 
{'l', -81.08, 4.97, 0, 0, 0, 0}, 
{'c', -81.08, 4.97, -79.13 ,19.01, -68.60, 24.08}, 
{'c', -63.89, 26.35, -60.80 ,19.79, -58.85, 13.94}, 
{'c', -58.85, 13.94, -61.97 ,-6.73, -59.63, -11.02}, 
{'c', -59.63, -11.02, -49.10 ,-20.77, -49.49, -24.28}, 
{'f', 0.938000,0.603000,0.335000,1.000000,0,0 }, 
{'m', -51.50, -41.62, 0, 0, 0, 0}, 
{'c', -52.48, -42.54, -59.86 ,-48.08, -65.56, -42.00}, 
{'c', -65.56, -42.00, -75.44 ,-24.90, -74.68, -18.82}, 
{'l', -74.68, -16.92, 0, 0, 0, 0}, 
{'c', -74.68, -16.92, -81.90 ,-17.30, -83.42, -15.40}, 
{'c', -83.42, -15.40, -84.56 ,-10.46, -85.70, -10.08}, 
{'c', -85.70, -10.08, -88.36 ,-7.80, -86.46, -5.14}, 
{'c', -86.46, -5.14, -88.36 ,-2.86, -87.98, 0.94}, 
{'l', -80.76, 4.74, 0, 0, 0, 0}, 
{'c', -80.76, 4.74, -78.86 ,18.42, -68.60, 23.36}, 
{'c', -64.01, 25.57, -61.00 ,19.18, -59.10, 13.48}, 
{'c', -59.10, 13.48, -62.14 ,-6.66, -59.86, -10.84}, 
{'c', -59.86, -10.84, -49.60 ,-20.34, -49.98, -23.76}, 
{'f', 1.000000,0.737000,0.603000,1.000000,0,0 }, 
{'m', -51.95, -40.63, 0, 0, 0, 0}, 
{'c', -52.82, -41.61, -60.09 ,-46.92, -65.64, -41.00}, 
{'c', -65.64, -41.00, -75.26 ,-24.35, -74.52, -18.43}, 
{'l', -74.52, -16.58, 0, 0, 0, 0}, 
{'c', -74.52, -16.58, -81.55 ,-16.95, -83.03, -15.10}, 
{'c', -83.03, -15.10, -84.14 ,-10.29, -85.25, -9.92}, 
{'c', -85.25, -9.92, -87.84 ,-7.70, -85.99, -5.11}, 
{'c', -85.99, -5.11, -87.84 ,-2.89, -87.47, 0.81}, 
{'l', -80.44, 4.51, 0, 0, 0, 0}, 
{'c', -80.44, 4.51, -78.59 ,17.83, -68.60, 22.64}, 
{'c', -64.13, 24.79, -61.20 ,18.57, -59.35, 13.02}, 
{'c', -59.35, 13.02, -62.31 ,-6.59, -60.09, -10.66}, 
{'c', -60.09, -10.66, -50.10 ,-19.91, -50.47, -23.24}, 
{'f', 1.000000,0.871000,0.804000,1.000000,0,0 }, 
{'m', -59.60, 12.46, 0, 0, 0, 0}, 
{'c', -59.60, 12.46, -62.48 ,-6.52, -60.32, -10.48}, 
{'c', -60.32, -10.48, -50.60 ,-19.48, -50.96, -22.72}, 
{'c', -50.96, -22.72, -51.32 ,-38.92, -52.40, -39.64}, 
{'c', -53.16, -40.68, -60.32 ,-45.76, -65.72, -40.00}, 
{'c', -65.72, -40.00, -75.08 ,-23.80, -74.36, -18.04}, 
{'l', -74.36, -16.24, 0, 0, 0, 0}, 
{'c', -74.36, -16.24, -81.20 ,-16.60, -82.64, -14.80}, 
{'c', -82.64, -14.80, -83.72 ,-10.12, -84.80, -9.76}, 
{'c', -84.80, -9.76, -87.32 ,-7.60, -85.52, -5.08}, 
{'c', -85.52, -5.08, -87.32 ,-2.92, -86.96, 0.68}, 
{'l', -80.12, 4.28, 0, 0, 0, 0}, 
{'c', -80.12, 4.28, -78.32 ,17.24, -68.60, 21.92}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -62.70, 6.20, 0, 0, 0, 0}, 
{'c', -62.70, 6.20, -84.30 ,-4.00, -85.20, -4.80}, 
{'c', -85.20, -4.80, -76.10 ,3.40, -75.30, 3.40}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -79.80, 0.00, 0, 0, 0, 0}, 
{'c', -79.80, 0.00, -61.40 ,3.60, -61.40, 8.00}, 
{'c', -61.40, 10.91, -61.64 ,24.33, -67.00, 22.80}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -71.40, 3.80, 0, 0, 0, 0}, 
{'c', -71.40, 3.80, -62.42 ,5.27, -61.40, 8.00}, 
{'c', -60.80, 9.60, -60.14 ,17.91, -65.60, 19.00}, 
{'f', 0.603000,0.804000,0.201000,1.000000,0,0 }, 
{'m', 14.60, 46.35, 0, 0, 0, 0}, 
{'c', 14.10, 44.61, 15.41 ,44.74, 17.20, 44.20}, 
{'c', 19.20, 43.60, 31.40 ,39.80, 32.20, 37.20}, 
{'c', 33.00, 34.60, 46.20 ,39.00, 46.20, 39.00}, 
{'c', 48.00, 39.80, 52.40 ,42.40, 52.40, 42.40}, 
{'c', 57.20, 43.60, 63.80 ,44.00, 63.80, 44.00}, 
{'c', 66.20, 45.00, 69.60 ,47.80, 69.60, 47.80}, 
{'c', 84.20, 58.00, 96.60 ,50.80, 96.60, 50.80}, 
{'c', 116.60, 44.20, 110.60 ,27.00, 110.60, 27.00}, 
{'c', 107.60, 18.00, 110.80 ,14.60, 110.80, 14.60}, 
{'c', 111.00, 10.80, 118.20 ,17.20, 118.20, 17.20}, 
{'c', 120.80, 21.40, 121.60 ,26.40, 121.60, 26.40}, 
{'c', 129.60, 37.60, 126.20 ,19.80, 126.20, 19.80}, 
{'c', 126.40, 18.80, 123.60 ,15.20, 123.60, 14.00}, 
{'c', 123.60, 12.80, 121.80 ,9.40, 121.80, 9.40}, 
{'c', 118.80, 6.00, 121.20 ,-1.00, 121.20, -1.00}, 
{'c', 123.00, -14.80, 120.80 ,-13.00, 120.80, -13.00}, 
{'c', 119.60, -14.80, 110.40 ,-4.80, 110.40, -4.80}, 
{'c', 108.20, -1.40, 102.20 ,0.20, 102.20, 0.20}, 
{'c', 99.40, 2.00, 96.00 ,0.60, 96.00, 0.60}, 
{'c', 93.40, 0.20, 87.80 ,7.20, 87.80, 7.20}, 
{'c', 90.60, 7.00, 93.00 ,11.40, 95.40, 11.60}, 
{'c', 97.80, 11.80, 99.60 ,9.20, 101.20, 8.60}, 
{'c', 102.80, 8.00, 105.60 ,13.80, 105.60, 13.80}, 
{'c', 106.00, 16.40, 100.40 ,21.20, 100.40, 21.20}, 
{'c', 100.00, 25.80, 98.40 ,24.20, 98.40, 24.20}, 
{'c', 95.40, 23.60, 94.20 ,27.40, 93.20, 32.00}, 
{'c', 92.20, 36.60, 88.00 ,37.00, 88.00, 37.00}, 
{'c', 86.40, 44.40, 85.20 ,41.40, 85.20, 41.40}, 
{'c', 85.00, 35.80, 79.00 ,41.60, 79.00, 41.60}, 
{'c', 77.80, 43.60, 73.20 ,41.40, 73.20, 41.40}, 
{'c', 66.40, 39.40, 68.80 ,37.40, 68.80, 37.40}, 
{'c', 70.60, 35.20, 81.80 ,37.40, 81.80, 37.40}, 
{'c', 84.00, 35.80, 76.00 ,31.80, 76.00, 31.80}, 
{'c', 75.40, 30.00, 76.40 ,25.60, 76.40, 25.60}, 
{'c', 77.60, 22.40, 84.40 ,16.80, 84.40, 16.80}, 
{'c', 93.80, 15.60, 91.00 ,14.00, 91.00, 14.00}, 
{'c', 84.80, 8.80, 79.00 ,16.40, 79.00, 16.40}, 
{'c', 76.80, 22.60, 59.40 ,37.60, 59.40, 37.60}, 
{'c', 54.60, 41.00, 57.20 ,34.20, 53.20, 37.60}, 
{'c', 49.20, 41.00, 28.60 ,32.00, 28.60, 32.00}, 
{'c', 17.04, 30.81, 14.31 ,46.55, 10.78, 43.43}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 209.40, -120.00, 0, 0, 0, 0}, 
{'c', 209.40, -120.00, 183.80 ,-112.00, 181.00, -93.20}, 
{'c', 181.00, -93.20, 178.60 ,-70.40, 199.00, -52.80}, 
{'c', 199.00, -52.80, 199.40 ,-46.40, 201.40, -43.20}, 
{'c', 201.40, -43.20, 199.80 ,-38.40, 218.60, -46.00}, 
{'l', 245.80, -54.40, 0, 0, 0, 0}, 
{'c', 245.80, -54.40, 252.20 ,-56.80, 257.40, -65.60}, 
{'c', 262.60, -74.40, 277.80 ,-93.20, 274.20, -118.40}, 
{'c', 274.20, -118.40, 275.40 ,-129.60, 269.40, -130.00}, 
{'c', 269.40, -130.00, 261.00 ,-131.60, 253.80, -124.00}, 
{'c', 253.80, -124.00, 247.00 ,-120.80, 244.60, -121.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 264.02, -120.99, 0, 0, 0, 0}, 
{'c', 264.02, -120.99, 266.12 ,-129.92, 261.28, -125.08}, 
{'c', 261.28, -125.08, 254.24 ,-119.36, 246.76, -119.36}, 
{'c', 246.76, -119.36, 232.24 ,-117.16, 227.84, -103.96}, 
{'c', 227.84, -103.96, 223.88 ,-77.12, 231.80, -71.40}, 
{'c', 231.80, -71.40, 236.64 ,-63.92, 243.68, -70.52}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 263.65, -120.63, 0, 0, 0, 0}, 
{'c', 263.65, -120.63, 265.74 ,-129.38, 260.99, -124.62}, 
{'c', 260.99, -124.62, 254.07 ,-119.01, 246.73, -119.01}, 
{'c', 246.73, -119.01, 232.47 ,-116.85, 228.15, -103.89}, 
{'c', 228.15, -103.89, 224.26 ,-77.54, 232.04, -71.92}, 
{'c', 232.04, -71.92, 236.79 ,-64.58, 243.71, -71.06}, 
{'f', 0.201000,0.201000,0.201000,1.000000,0,0 }, 
{'m', 263.27, -120.27, 0, 0, 0, 0}, 
{'c', 263.27, -120.27, 265.35 ,-128.83, 260.69, -124.17}, 
{'c', 260.69, -124.17, 253.91 ,-118.66, 246.70, -118.66}, 
{'c', 246.70, -118.66, 232.71 ,-116.54, 228.47, -103.82}, 
{'c', 228.47, -103.82, 224.65 ,-77.95, 232.28, -72.44}, 
{'c', 232.28, -72.44, 236.94 ,-65.23, 243.73, -71.59}, 
{'f', 0.402000,0.402000,0.402000,1.000000,0,0 }, 
{'m', 262.90, -119.92, 0, 0, 0, 0}, 
{'c', 262.90, -119.92, 264.97 ,-128.29, 260.39, -123.71}, 
{'c', 260.39, -123.71, 253.74 ,-118.30, 246.66, -118.30}, 
{'c', 246.66, -118.30, 232.94 ,-116.22, 228.78, -103.74}, 
{'c', 228.78, -103.74, 225.03 ,-78.37, 232.52, -72.96}, 
{'c', 232.52, -72.96, 237.10 ,-65.89, 243.75, -72.13}, 
{'f', 0.603000,0.603000,0.603000,1.000000,0,0 }, 
{'m', 262.53, -119.56, 0, 0, 0, 0}, 
{'c', 262.53, -119.56, 264.59 ,-127.74, 260.10, -123.26}, 
{'c', 260.10, -123.26, 253.57 ,-117.95, 246.63, -117.95}, 
{'c', 246.63, -117.95, 233.17 ,-115.91, 229.09, -103.67}, 
{'c', 229.09, -103.67, 225.42 ,-78.78, 232.76, -73.48}, 
{'c', 232.76, -73.48, 237.25 ,-66.54, 243.78, -72.66}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 262.15, -119.20, 0, 0, 0, 0}, 
{'c', 262.15, -119.20, 264.20 ,-127.20, 259.80, -122.80}, 
{'c', 259.80, -122.80, 253.40 ,-117.60, 246.60, -117.60}, 
{'c', 246.60, -117.60, 233.40 ,-115.60, 229.40, -103.60}, 
{'c', 229.40, -103.60, 225.80 ,-79.20, 233.00, -74.00}, 
{'c', 233.00, -74.00, 237.40 ,-67.20, 243.80, -73.20}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 50.60, 84.00, 0, 0, 0, 0}, 
{'c', 50.60, 84.00, 30.20 ,64.80, 22.20, 64.00}, 
{'c', 22.20, 64.00, -12.20 ,60.00, -27.00, 78.00}, 
{'c', -27.00, 78.00, -9.40 ,57.60, 18.20, 63.20}, 
{'c', 18.20, 63.20, -3.40 ,58.80, -15.80, 62.00}, 
{'c', -15.80, 62.00, -32.60 ,62.00, -42.20, 76.00}, 
{'l', -45.00, 80.80, 0, 0, 0, 0}, 
{'c', -45.00, 80.80, -41.00 ,66.00, -22.60, 60.00}, 
{'c', -22.60, 60.00, 0.20 ,55.20, 11.00, 60.00}, 
{'c', 11.00, 60.00, -10.60 ,53.20, -20.60, 55.20}, 
{'c', -20.60, 55.20, -51.00 ,52.80, -63.80, 79.20}, 
{'c', -63.80, 79.20, -59.80 ,64.80, -45.00, 57.60}, 
{'c', -45.00, 57.60, -31.40 ,48.80, -11.00, 51.60}, 
{'c', -11.00, 51.60, 3.40 ,54.80, 8.60, 57.20}, 
{'c', 13.80, 59.60, 12.60 ,56.80, 4.20, 52.00}, 
{'c', 4.20, 52.00, -1.40 ,42.00, -15.40, 42.40}, 
{'c', -15.40, 42.40, -58.20 ,46.00, -68.60, 58.00}, 
{'c', -68.60, 58.00, -55.00 ,46.80, -44.60, 44.00}, 
{'c', -44.60, 44.00, -22.20 ,36.00, -13.80, 36.80}, 
{'c', -13.80, 36.80, 11.00 ,37.80, 18.60, 33.80}, 
{'c', 18.60, 33.80, 7.40 ,38.80, 10.60, 42.00}, 
{'c', 13.80, 45.20, 20.60 ,52.80, 20.60, 54.00}, 
{'c', 20.60, 55.20, 44.80 ,77.30, 48.40, 81.70}, 
{'f', 0.603000,0.134000,0.000000,1.000000,0,0 }, 
{'m', 189.00, 278.00, 0, 0, 0, 0}, 
{'c', 189.00, 278.00, 173.50 ,241.50, 161.00, 232.00}, 
{'c', 161.00, 232.00, 187.00 ,248.00, 190.50, 266.00}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 236.00, 285.50, 0, 0, 0, 0}, 
{'c', 236.00, 285.50, 209.50 ,230.50, 191.00, 206.50}, 
{'c', 191.00, 206.50, 234.50 ,244.00, 239.50, 270.50}, 
{'l', 240.00, 276.00, 0, 0, 0, 0}, 
{'l', 237.00, 273.50, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 292.50, 237.00, 0, 0, 0, 0}, 
{'c', 292.50, 237.00, 230.00 ,177.50, 228.50, 175.00}, 
{'c', 228.50, 175.00, 289.00 ,241.00, 292.00, 248.50}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 104.00, 280.50, 0, 0, 0, 0}, 
{'c', 104.00, 280.50, 123.50 ,228.50, 142.50, 251.00}, 
{'c', 142.50, 251.00, 157.50 ,261.00, 157.00, 264.00}, 
{'c', 157.00, 264.00, 153.00 ,257.50, 135.00, 258.00}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 294.50, 153.00, 0, 0, 0, 0}, 
{'c', 294.50, 153.00, 249.50 ,124.50, 242.00, 123.00}, 
{'c', 230.19, 120.64, 291.50 ,152.00, 296.50, 162.50}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 143.80, 259.60, 0, 0, 0, 0}, 
{'c', 143.80, 259.60, 164.20 ,257.60, 171.00, 250.80}, 
{'l', 175.40, 254.40, 0, 0, 0, 0}, 
{'l', 193.00, 216.00, 0, 0, 0, 0}, 
{'l', 196.60, 221.20, 0, 0, 0, 0}, 
{'c', 196.60, 221.20, 211.00 ,206.40, 210.20, 198.40}, 
{'c', 209.40, 190.40, 223.00 ,204.40, 223.00, 204.40}, 
{'c', 223.00, 204.40, 222.20 ,192.80, 229.40, 199.60}, 
{'c', 229.40, 199.60, 227.00 ,184.00, 235.40, 192.00}, 
{'c', 235.40, 192.00, 224.86 ,161.84, 247.40, 187.60}, 
{'c', 253.00, 194.00, 248.60 ,187.20, 248.60, 187.20}, 
{'c', 248.60, 187.20, 222.60 ,139.20, 244.20, 153.60}, 
{'c', 244.20, 153.60, 246.20 ,130.80, 245.00, 126.40}, 
{'c', 243.80, 122.00, 241.80 ,99.60, 237.00, 94.40}, 
{'c', 232.20, 89.20, 237.40 ,87.60, 243.00, 92.80}, 
{'c', 243.00, 92.80, 231.80 ,68.80, 245.00, 80.80}, 
{'c', 245.00, 80.80, 241.40 ,65.60, 237.00, 62.80}, 
{'c', 237.00, 62.80, 231.40 ,45.60, 246.60, 56.40}, 
{'c', 246.60, 56.40, 242.20 ,44.00, 239.00, 40.80}, 
{'c', 239.00, 40.80, 227.40 ,13.20, 234.60, 18.00}, 
{'l', 239.00, 21.60, 0, 0, 0, 0}, 
{'c', 239.00, 21.60, 232.20 ,7.60, 238.60, 12.00}, 
{'c', 245.00, 16.40, 245.00 ,16.00, 245.00, 16.00}, 
{'c', 245.00, 16.00, 223.80 ,-17.20, 244.20, 0.40}, 
{'c', 244.20, 0.40, 236.04 ,-13.52, 232.60, -20.40}, 
{'c', 232.60, -20.40, 213.80 ,-40.80, 228.20, -34.40}, 
{'l', 233.00, -32.80, 0, 0, 0, 0}, 
{'c', 233.00, -32.80, 224.20 ,-42.80, 216.20, -44.40}, 
{'c', 208.20, -46.00, 218.60 ,-52.40, 225.00, -50.40}, 
{'c', 231.40, -48.40, 247.00 ,-40.80, 247.00, -40.80}, 
{'c', 247.00, -40.80, 259.80 ,-22.00, 263.80, -21.60}, 
{'c', 263.80, -21.60, 243.80 ,-29.20, 249.80, -21.20}, 
{'c', 249.80, -21.20, 264.20 ,-7.20, 257.00, -7.60}, 
{'c', 257.00, -7.60, 251.00 ,-0.40, 255.80, 8.40}, 
{'c', 255.80, 8.40, 237.34 ,-9.99, 252.20, 15.60}, 
{'l', 259.00, 32.00, 0, 0, 0, 0}, 
{'c', 259.00, 32.00, 234.60 ,7.20, 245.80, 29.20}, 
{'c', 245.80, 29.20, 263.00 ,52.80, 265.00, 53.20}, 
{'c', 267.00, 53.60, 271.40 ,62.40, 271.40, 62.40}, 
{'l', 267.00, 60.40, 0, 0, 0, 0}, 
{'l', 272.20, 69.20, 0, 0, 0, 0}, 
{'c', 272.20, 69.20, 261.00 ,57.20, 267.00, 70.40}, 
{'l', 272.60, 84.80, 0, 0, 0, 0}, 
{'c', 272.60, 84.80, 252.20 ,62.80, 265.80, 92.40}, 
{'c', 265.80, 92.40, 249.40 ,87.20, 258.20, 104.40}, 
{'c', 258.20, 104.40, 256.60 ,120.40, 257.00, 125.60}, 
{'c', 257.40, 130.80, 258.60 ,159.20, 254.20, 167.20}, 
{'c', 249.80, 175.20, 260.20 ,194.40, 262.20, 198.40}, 
{'c', 264.20, 202.40, 267.80 ,213.20, 259.00, 204.00}, 
{'c', 250.20, 194.80, 254.60 ,200.40, 256.60, 209.20}, 
{'c', 258.60, 218.00, 264.60 ,233.60, 263.80, 239.20}, 
{'c', 263.80, 239.20, 262.60 ,240.40, 259.40, 236.80}, 
{'c', 259.40, 236.80, 244.60 ,214.00, 246.20, 228.40}, 
{'c', 246.20, 228.40, 245.00 ,236.40, 241.80, 245.20}, 
{'c', 241.80, 245.20, 238.60 ,256.00, 238.60, 247.20}, 
{'c', 238.60, 247.20, 235.40 ,230.40, 232.60, 238.00}, 
{'c', 229.80, 245.60, 226.20 ,251.60, 223.40, 254.00}, 
{'c', 220.60, 256.40, 215.40 ,233.60, 214.20, 244.00}, 
{'c', 214.20, 244.00, 202.20 ,231.60, 197.40, 248.00}, 
{'l', 185.80, 264.40, 0, 0, 0, 0}, 
{'c', 185.80, 264.40, 185.40 ,252.00, 184.20, 258.00}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 109.40, -97.20, 0, 0, 0, 0}, 
{'c', 109.40, -97.20, 97.80 ,-105.20, 93.80, -104.80}, 
{'c', 89.80, -104.40, 121.40 ,-113.60, 162.60, -86.00}, 
{'c', 162.60, -86.00, 167.40 ,-83.20, 171.00, -83.60}, 
{'c', 171.00, -83.60, 174.20 ,-81.20, 171.40, -77.60}, 
{'c', 171.40, -77.60, 162.60 ,-68.00, 173.80, -56.80}, 
{'c', 173.80, -56.80, 192.20 ,-50.00, 186.60, -58.80}, 
{'c', 186.60, -58.80, 197.40 ,-54.80, 199.80, -50.80}, 
{'c', 202.20, -46.80, 201.00 ,-50.80, 201.00, -50.80}, 
{'c', 201.00, -50.80, 194.60 ,-58.00, 188.60, -63.20}, 
{'c', 188.60, -63.20, 183.40 ,-65.20, 180.60, -73.60}, 
{'c', 177.80, -82.00, 175.40 ,-92.00, 179.80, -95.20}, 
{'c', 179.80, -95.20, 175.80 ,-90.80, 176.60, -94.80}, 
{'c', 177.40, -98.80, 181.00 ,-102.40, 182.60, -102.80}, 
{'c', 184.20, -103.20, 200.60 ,-119.00, 207.40, -119.40}, 
{'c', 207.40, -119.40, 198.20 ,-118.00, 195.20, -119.00}, 
{'c', 192.20, -120.00, 165.60 ,-131.40, 159.60, -132.60}, 
{'c', 159.60, -132.60, 142.80 ,-139.20, 154.80, -137.20}, 
{'c', 154.80, -137.20, 190.60 ,-133.40, 208.80, -120.20}, 
{'c', 208.80, -120.20, 201.60 ,-128.60, 183.20, -135.60}, 
{'c', 183.20, -135.60, 161.00 ,-148.20, 125.80, -143.20}, 
{'c', 125.80, -143.20, 108.00 ,-140.00, 100.20, -138.20}, 
{'c', 100.20, -138.20, 97.60 ,-138.80, 97.00, -139.20}, 
{'c', 96.40, -139.60, 84.60 ,-148.60, 57.00, -141.60}, 
{'c', 57.00, -141.60, 40.00 ,-137.00, 31.40, -132.20}, 
{'c', 31.40, -132.20, 16.20 ,-131.00, 12.60, -127.80}, 
{'c', 12.60, -127.80, -6.00 ,-113.20, -8.00, -112.40}, 
{'c', -10.00, -111.60, -21.40 ,-104.00, -22.20, -103.60}, 
{'c', -22.20, -103.60, 2.40 ,-110.20, 4.80, -112.60}, 
{'c', 7.20, -115.00, 24.60 ,-117.60, 27.00, -116.20}, 
{'c', 29.40, -114.80, 37.80 ,-115.40, 28.20, -114.80}, 
{'c', 28.20, -114.80, 103.80 ,-100.00, 104.60, -98.00}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 180.80, -106.40, 0, 0, 0, 0}, 
{'c', 180.80, -106.40, 170.60 ,-113.80, 168.60, -113.80}, 
{'c', 166.60, -113.80, 154.20 ,-124.00, 150.00, -123.60}, 
{'c', 145.80, -123.20, 133.60 ,-133.20, 106.20, -125.00}, 
{'c', 106.20, -125.00, 105.60 ,-127.00, 109.20, -127.80}, 
{'c', 109.20, -127.80, 115.60 ,-130.00, 116.00, -130.60}, 
{'c', 116.00, -130.60, 136.20 ,-134.80, 143.40, -131.20}, 
{'c', 143.40, -131.20, 152.60 ,-128.60, 158.80, -122.40}, 
{'c', 158.80, -122.40, 170.00 ,-119.20, 173.20, -120.20}, 
{'c', 173.20, -120.20, 182.00 ,-118.00, 182.40, -116.20}, 
{'c', 182.40, -116.20, 188.20 ,-113.20, 186.40, -110.60}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', 168.33, -108.51, 0, 0, 0, 0}, 
{'c', 169.14, -107.88, 170.16 ,-107.78, 170.76, -106.97}, 
{'c', 171.00, -106.66, 170.71 ,-106.33, 170.39, -106.23}, 
{'c', 169.35, -105.92, 168.29 ,-106.49, 167.15, -105.90}, 
{'c', 166.75, -105.69, 166.11 ,-105.87, 165.55, -106.02}, 
{'c', 163.92, -106.46, 162.09 ,-106.49, 160.40, -105.80}, 
{'c', 158.42, -106.93, 156.06 ,-106.34, 153.97, -107.35}, 
{'c', 153.92, -107.37, 153.69 ,-107.03, 153.62, -107.05}, 
{'c', 150.57, -108.20, 146.83 ,-107.92, 144.40, -110.20}, 
{'c', 141.97, -110.61, 139.62 ,-111.07, 137.19, -111.75}, 
{'c', 135.37, -112.26, 133.96 ,-113.25, 132.34, -114.08}, 
{'c', 130.96, -114.79, 129.51 ,-115.31, 127.97, -115.69}, 
{'c', 126.11, -116.14, 124.28 ,-116.03, 122.39, -116.55}, 
{'c', 122.29, -116.57, 122.10 ,-116.23, 122.02, -116.25}, 
{'c', 121.69, -116.36, 121.41 ,-116.94, 121.23, -116.89}, 
{'c', 119.55, -116.37, 118.06 ,-117.34, 116.40, -117.00}, 
{'c', 115.22, -118.22, 113.50 ,-117.98, 111.95, -118.42}, 
{'c', 108.98, -119.27, 105.83 ,-118.00, 102.80, -119.00}, 
{'c', 106.91, -120.84, 111.60 ,-119.61, 115.66, -121.68}, 
{'c', 117.99, -122.86, 120.65 ,-121.76, 123.22, -122.52}, 
{'c', 123.71, -122.67, 124.40 ,-122.87, 124.80, -122.20}, 
{'c', 124.94, -122.33, 125.12 ,-122.57, 125.17, -122.55}, 
{'c', 127.62, -121.39, 129.94 ,-120.11, 132.42, -119.05}, 
{'c', 132.76, -118.90, 133.29 ,-119.14, 133.55, -118.93}, 
{'c', 135.07, -117.72, 137.01 ,-117.82, 138.40, -116.60}, 
{'c', 140.10, -117.10, 141.89 ,-116.72, 143.62, -117.35}, 
{'c', 143.70, -117.37, 143.93 ,-117.03, 143.97, -117.05}, 
{'c', 145.09, -117.80, 146.25 ,-117.53, 147.14, -117.23}, 
{'c', 147.48, -117.11, 148.14 ,-116.86, 148.45, -116.79}, 
{'c', 149.57, -116.52, 150.43 ,-116.03, 151.61, -115.85}, 
{'c', 151.72, -115.83, 151.91 ,-116.17, 151.98, -116.15}, 
{'c', 153.10, -115.71, 154.15 ,-115.76, 154.80, -114.60}, 
{'c', 154.94, -114.73, 155.10 ,-114.97, 155.18, -114.95}, 
{'c', 156.21, -114.61, 156.86 ,-113.85, 157.96, -113.61}, 
{'c', 158.44, -113.51, 159.06 ,-112.88, 159.63, -112.70}, 
{'c', 162.03, -111.97, 163.87 ,-110.44, 166.06, -109.55}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', 91.70, -122.74, 0, 0, 0, 0}, 
{'c', 89.18, -124.46, 86.81 ,-125.57, 84.37, -127.36}, 
{'c', 84.19, -127.49, 83.83 ,-127.32, 83.62, -127.44}, 
{'c', 82.62, -128.05, 81.73 ,-128.63, 80.75, -129.33}, 
{'c', 80.21, -129.71, 79.39 ,-129.70, 78.88, -129.96}, 
{'c', 76.34, -131.25, 73.71 ,-131.81, 71.20, -133.00}, 
{'c', 71.88, -133.64, 73.00 ,-133.39, 73.60, -134.20}, 
{'c', 73.80, -133.92, 74.03 ,-133.64, 74.39, -133.83}, 
{'c', 76.06, -134.73, 77.91 ,-134.88, 79.59, -134.79}, 
{'c', 81.29, -134.70, 83.01 ,-134.40, 84.79, -134.12}, 
{'c', 85.10, -134.08, 85.30 ,-133.56, 85.62, -133.46}, 
{'c', 87.85, -132.79, 90.23 ,-133.32, 92.35, -132.48}, 
{'c', 93.94, -131.85, 95.52 ,-131.03, 96.75, -129.75}, 
{'c', 97.01, -129.50, 96.68 ,-129.19, 96.40, -129.00}, 
{'c', 96.79, -129.11, 97.06 ,-128.90, 97.17, -128.59}, 
{'c', 97.26, -128.35, 97.26 ,-128.05, 97.17, -127.81}, 
{'c', 97.06, -127.50, 96.78 ,-127.40, 96.41, -127.35}, 
{'c', 95.00, -127.16, 96.77 ,-128.54, 96.07, -128.09}, 
{'c', 94.80, -127.27, 95.55 ,-125.87, 94.80, -124.60}, 
{'c', 94.52, -124.79, 94.29 ,-125.01, 94.40, -125.40}, 
{'c', 94.64, -124.88, 94.03 ,-124.59, 93.86, -124.27}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', 59.20, -115.39, 0, 0, 0, 0}, 
{'c', 56.04, -116.19, 52.99 ,-116.07, 49.98, -117.35}, 
{'c', 49.91, -117.37, 49.69 ,-117.03, 49.62, -117.05}, 
{'c', 48.26, -117.65, 47.34 ,-118.61, 46.26, -119.66}, 
{'c', 45.35, -120.55, 43.69 ,-120.16, 42.42, -120.65}, 
{'c', 42.09, -120.77, 41.89 ,-121.28, 41.59, -121.32}, 
{'c', 40.37, -121.48, 39.45 ,-122.43, 38.40, -123.00}, 
{'c', 40.74, -123.80, 43.15 ,-123.76, 45.61, -124.15}, 
{'c', 45.72, -124.17, 45.87 ,-123.84, 46.00, -123.84}, 
{'c', 46.14, -123.84, 46.27 ,-124.07, 46.40, -124.20}, 
{'c', 46.59, -123.92, 46.90 ,-123.59, 47.15, -123.85}, 
{'c', 47.70, -124.39, 48.26 ,-124.20, 48.80, -124.16}, 
{'c', 48.94, -124.15, 49.07 ,-123.84, 49.20, -123.84}, 
{'c', 49.34, -123.84, 49.47 ,-124.16, 49.60, -124.16}, 
{'c', 49.74, -124.16, 49.87 ,-123.84, 50.00, -123.84}, 
{'c', 50.14, -123.84, 50.27 ,-124.07, 50.40, -124.20}, 
{'c', 51.09, -123.42, 51.98 ,-123.97, 52.80, -123.79}, 
{'c', 53.84, -123.57, 54.10 ,-122.42, 55.18, -122.12}, 
{'c', 59.89, -120.82, 64.03 ,-118.67, 68.39, -116.58}, 
{'c', 68.70, -116.44, 68.91 ,-116.19, 68.80, -115.80}, 
{'c', 69.07, -115.80, 69.38 ,-115.89, 69.57, -115.76}, 
{'c', 70.63, -115.02, 71.67 ,-114.48, 72.37, -113.38}, 
{'c', 72.58, -113.04, 72.25 ,-112.63, 72.02, -112.68}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', 45.34, -71.18, 0, 0, 0, 0}, 
{'c', 43.75, -72.40, 43.16 ,-74.43, 42.03, -76.22}, 
{'c', 41.82, -76.56, 42.09 ,-76.88, 42.41, -76.96}, 
{'c', 42.97, -77.12, 43.51 ,-76.64, 43.92, -76.44}, 
{'c', 45.67, -75.58, 47.20 ,-74.34, 49.20, -74.20}, 
{'c', 51.19, -71.97, 55.45 ,-71.58, 55.46, -68.20}, 
{'c', 55.46, -67.34, 54.03 ,-68.26, 53.60, -67.40}, 
{'c', 51.15, -68.40, 48.76 ,-68.30, 46.38, -69.77}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', 17.80, -123.76, 0, 0, 0, 0}, 
{'c', 17.93, -123.75, 24.97 ,-123.52, 24.95, -123.41}, 
{'c', 24.90, -123.10, 17.17 ,-122.05, 16.81, -122.22}, 
{'c', 16.65, -122.30, 9.13 ,-119.87, 9.00, -120.00}, 
{'f', 0.804000,0.469000,0.134000,1.000000,0,0 }, 
{'m', 33.20, -114.00, 0, 0, 0, 0}, 
{'c', 33.20, -114.00, 18.40 ,-112.20, 14.00, -111.00}, 
{'c', 9.60, -109.80, -9.00 ,-102.20, -12.00, -100.20}, 
{'c', -12.00, -100.20, -25.40 ,-94.80, -42.40, -74.80}, 
{'c', -42.40, -74.80, -34.80 ,-78.20, -32.60, -81.00}, 
{'c', -32.60, -81.00, -19.00 ,-93.60, -19.20, -91.00}, 
{'c', -19.20, -91.00, -7.00 ,-99.60, -7.60, -97.40}, 
{'c', -7.60, -97.40, 16.80 ,-108.60, 14.80, -105.40}, 
{'c', 14.80, -105.40, 36.40 ,-110.00, 35.40, -108.00}, 
{'c', 35.40, -108.00, 54.20 ,-103.60, 51.40, -103.40}, 
{'c', 51.40, -103.40, 45.60 ,-102.20, 52.00, -98.60}, 
{'c', 52.00, -98.60, 48.60 ,-94.20, 43.20, -98.20}, 
{'c', 37.80, -102.20, 40.80 ,-100.00, 35.80, -99.00}, 
{'c', 35.80, -99.00, 33.20 ,-98.20, 28.60, -102.20}, 
{'c', 28.60, -102.20, 23.00 ,-106.80, 14.20, -103.20}, 
{'c', 14.20, -103.20, -16.40 ,-90.60, -18.40, -90.00}, 
{'c', -18.40, -90.00, -22.00 ,-87.20, -24.40, -83.60}, 
{'c', -24.40, -83.60, -30.20 ,-79.20, -33.20, -77.80}, 
{'c', -33.20, -77.80, -46.00 ,-66.20, -47.20, -64.80}, 
{'c', -47.20, -64.80, -50.60 ,-59.60, -51.40, -59.20}, 
{'c', -51.40, -59.20, -45.00 ,-63.00, -43.00, -65.00}, 
{'c', -43.00, -65.00, -29.00 ,-75.00, -23.60, -75.80}, 
{'c', -23.60, -75.80, -19.20 ,-78.80, -18.40, -80.20}, 
{'c', -18.40, -80.20, -4.00 ,-89.40, 0.20, -89.40}, 
{'c', 0.20, -89.40, 9.40 ,-84.20, 11.80, -91.20}, 
{'c', 11.80, -91.20, 17.60 ,-93.00, 23.20, -91.80}, 
{'c', 23.20, -91.80, 26.40 ,-94.40, 25.60, -96.60}, 
{'c', 25.60, -96.60, 27.20 ,-98.40, 28.20, -94.60}, 
{'c', 28.20, -94.60, 31.60 ,-91.00, 36.40, -93.00}, 
{'c', 36.40, -93.00, 40.40 ,-93.20, 38.40, -90.80}, 
{'c', 38.40, -90.80, 34.00 ,-87.00, 22.20, -86.80}, 
{'c', 22.20, -86.80, 9.80 ,-86.20, -6.60, -78.60}, 
{'c', -6.60, -78.60, -36.40 ,-68.20, -45.60, -57.80}, 
{'c', -45.60, -57.80, -52.00 ,-49.00, -57.40, -47.80}, 
{'c', -57.40, -47.80, -63.20 ,-47.00, -69.20, -39.60}, 
{'c', -69.20, -39.60, -59.40 ,-45.40, -50.40, -45.40}, 
{'c', -50.40, -45.40, -46.40 ,-47.80, -50.20, -44.20}, 
{'c', -50.20, -44.20, -53.80 ,-36.60, -52.20, -31.20}, 
{'c', -52.20, -31.20, -52.80 ,-26.00, -53.60, -24.40}, 
{'c', -53.60, -24.40, -61.40 ,-11.60, -61.40, -9.20}, 
{'c', -61.40, -6.80, -60.20 ,3.00, -59.80, 3.60}, 
{'c', -59.40, 4.20, -60.80 ,2.00, -57.00, 4.40}, 
{'c', -53.20, 6.80, -50.40 ,8.40, -49.60, 11.20}, 
{'c', -48.80, 14.00, -51.60 ,5.80, -51.80, 4.00}, 
{'c', -52.00, 2.20, -56.20 ,-5.00, -55.40, -7.40}, 
{'c', -55.40, -7.40, -54.40 ,-6.40, -53.60, -5.00}, 
{'c', -53.60, -5.00, -54.20 ,-5.60, -53.60, -9.20}, 
{'c', -53.60, -9.20, -52.80 ,-14.40, -51.40, -17.60}, 
{'c', -50.00, -20.80, -48.00 ,-24.60, -47.60, -25.40}, 
{'c', -47.20, -26.20, -47.20 ,-32.00, -45.80, -29.40}, 
{'l', -42.40, -26.80, 0, 0, 0, 0}, 
{'c', -42.40, -26.80, -45.20 ,-29.40, -43.00, -31.60}, 
{'c', -43.00, -31.60, -44.00 ,-37.20, -42.20, -39.80}, 
{'c', -42.20, -39.80, -35.20 ,-48.20, -33.60, -49.20}, 
{'c', -32.00, -50.20, -33.40 ,-49.80, -33.40, -49.80}, 
{'c', -33.40, -49.80, -27.40 ,-54.00, -33.20, -52.40}, 
{'c', -33.20, -52.40, -37.20 ,-50.80, -40.20, -50.80}, 
{'c', -40.20, -50.80, -47.80 ,-48.80, -43.80, -53.00}, 
{'c', -39.80, -57.20, -29.80 ,-62.60, -26.00, -62.40}, 
{'l', -25.20, -60.80, 0, 0, 0, 0}, 
{'l', -14.00, -63.20, 0, 0, 0, 0}, 
{'l', -15.20, -62.40, 0, 0, 0, 0}, 
{'c', -15.20, -62.40, -15.40 ,-62.60, -11.20, -63.00}, 
{'c', -7.00, -63.40, -1.20 ,-62.00, 0.20, -63.80}, 
{'c', 1.60, -65.60, 5.00 ,-66.60, 4.60, -65.20}, 
{'c', 4.20, -63.80, 4.00 ,-61.80, 4.00, -61.80}, 
{'c', 4.00, -61.80, 9.00 ,-67.60, 8.40, -65.40}, 
{'c', 7.80, -63.20, -0.40 ,-58.00, -1.80, -51.80}, 
{'l', 8.60, -60.00, 0, 0, 0, 0}, 
{'l', 12.20, -63.00, 0, 0, 0, 0}, 
{'c', 12.20, -63.00, 15.80 ,-60.80, 16.00, -62.40}, 
{'c', 16.20, -64.00, 20.80 ,-69.80, 22.00, -69.60}, 
{'c', 23.20, -69.40, 25.20 ,-72.20, 25.00, -69.60}, 
{'c', 24.80, -67.00, 32.40 ,-61.60, 32.40, -61.60}, 
{'c', 32.40, -61.60, 35.60 ,-63.40, 37.00, -62.00}, 
{'c', 38.40, -60.60, 42.60 ,-81.80, 42.60, -81.80}, 
{'l', 67.60, -92.40, 0, 0, 0, 0}, 
{'l', 111.20, -95.80, 0, 0, 0, 0}, 
{'l', 94.20, -102.60, 0, 0, 0, 0}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 51.40, 85.00, 0, 0, 0, 0}, 
{'c', 51.40, 85.00, 36.40 ,68.20, 28.00, 65.60}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 24.80, 64.20, 0, 0, 0, 0}, 
{'c', 24.80, 64.20, -0.40 ,56.20, -15.80, 60.40}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 21.20, 63.00, 0, 0, 0, 0}, 
{'c', 21.20, 63.00, 4.20 ,55.80, -10.60, 53.60}, 
{'c', -10.60, 53.60, -27.20 ,51.00, -43.80, 58.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 22.20, 63.40, 0, 0, 0, 0}, 
{'c', 22.20, 63.40, 6.80 ,52.40, 5.80, 51.00}, 
{'c', 5.80, 51.00, -1.20 ,40.00, -14.20, 39.60}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 20.89, 54.41, 0, 0, 0, 0}, 
{'c', 22.44, 55.87, 49.40 ,84.80, 49.40, 84.80}, 
{'c', 84.60, 121.40, 56.60 ,87.20, 56.60, 87.20}, 
{'c', 49.00, 82.40, 39.80 ,63.60, 39.80, 63.60}, 
{'c', 38.60, 60.80, 53.80 ,70.80, 53.80, 70.80}, 
{'c', 57.80, 71.60, 71.40 ,90.80, 71.40, 90.80}, 
{'c', 64.60, 88.40, 69.40 ,95.60, 69.40, 95.60}, 
{'c', 72.20, 97.60, 92.60 ,113.20, 92.60, 113.20}, 
{'c', 96.20, 117.20, 100.20 ,118.80, 100.20, 118.80}, 
{'c', 114.20, 113.60, 107.80 ,126.80, 107.80, 126.80}, 
{'c', 110.20, 133.60, 115.80 ,122.00, 115.80, 122.00}, 
{'c', 127.00, 105.20, 110.60 ,107.60, 110.60, 107.60}, 
{'c', 80.60, 110.40, 73.80 ,94.40, 73.80, 94.40}, 
{'c', 71.40, 92.00, 80.20 ,94.40, 80.20, 94.40}, 
{'c', 88.60, 96.40, 73.00 ,82.00, 73.00, 82.00}, 
{'c', 75.40, 82.00, 84.60 ,88.80, 84.60, 88.80}, 
{'c', 95.00, 98.00, 97.00 ,96.00, 97.00, 96.00}, 
{'c', 115.00, 87.20, 125.40 ,94.80, 125.40, 94.80}, 
{'c', 127.40, 96.40, 121.80 ,103.20, 123.40, 108.40}, 
{'c', 125.00, 113.60, 129.80 ,126.00, 129.80, 126.00}, 
{'c', 127.40, 127.60, 127.80 ,138.40, 127.80, 138.40}, 
{'c', 144.60, 161.60, 135.00 ,159.60, 135.00, 159.60}, 
{'c', 119.40, 159.20, 134.20 ,166.80, 134.20, 166.80}, 
{'c', 137.40, 168.80, 146.20 ,176.00, 146.20, 176.00}, 
{'c', 143.40, 174.80, 141.80 ,180.00, 141.80, 180.00}, 
{'c', 146.60, 184.00, 143.80 ,188.80, 143.80, 188.80}, 
{'c', 137.80, 190.00, 136.60 ,194.00, 136.60, 194.00}, 
{'c', 143.40, 202.00, 133.40 ,202.40, 133.40, 202.40}, 
{'c', 137.00, 206.80, 132.20 ,218.80, 132.20, 218.80}, 
{'c', 127.40, 218.80, 121.00 ,224.40, 121.00, 224.40}, 
{'c', 123.40, 229.20, 113.00 ,234.80, 113.00, 234.80}, 
{'c', 104.60, 236.40, 107.40 ,243.20, 107.40, 243.20}, 
{'c', 99.40, 249.20, 97.00 ,265.20, 97.00, 265.20}, 
{'c', 96.20, 275.60, 93.80 ,278.80, 99.00, 276.80}, 
{'c', 104.20, 274.80, 103.40 ,262.40, 103.40, 262.40}, 
{'c', 98.60, 246.80, 141.40 ,230.80, 141.40, 230.80}, 
{'c', 145.40, 229.20, 146.20 ,224.00, 146.20, 224.00}, 
{'c', 148.20, 224.40, 157.00 ,232.00, 157.00, 232.00}, 
{'c', 164.60, 243.20, 165.00 ,234.00, 165.00, 234.00}, 
{'c', 166.20, 230.40, 164.60 ,224.40, 164.60, 224.40}, 
{'c', 170.60, 202.80, 156.60 ,196.40, 156.60, 196.40}, 
{'c', 146.60, 162.80, 160.60 ,171.20, 160.60, 171.20}, 
{'c', 163.40, 176.80, 174.20 ,182.00, 174.20, 182.00}, 
{'l', 177.80, 179.60, 0, 0, 0, 0}, 
{'c', 176.20, 174.80, 184.60 ,168.80, 184.60, 168.80}, 
{'c', 187.40, 175.20, 193.40 ,167.20, 193.40, 167.20}, 
{'c', 197.00, 142.80, 209.40 ,157.20, 209.40, 157.20}, 
{'c', 213.40, 158.40, 214.60 ,151.60, 214.60, 151.60}, 
{'c', 218.20, 141.20, 214.60 ,127.60, 214.60, 127.60}, 
{'c', 218.20, 127.20, 227.80 ,133.20, 227.80, 133.20}, 
{'c', 230.60, 129.60, 221.40 ,112.80, 225.40, 115.20}, 
{'c', 229.40, 117.60, 233.80 ,119.20, 233.80, 119.20}, 
{'c', 234.60, 117.20, 224.60 ,104.80, 224.60, 104.80}, 
{'c', 220.20, 102.00, 215.00 ,81.60, 215.00, 81.60}, 
{'c', 222.20, 85.20, 212.20 ,70.00, 212.20, 70.00}, 
{'c', 212.20, 66.80, 218.20 ,55.60, 218.20, 55.60}, 
{'c', 217.40, 48.80, 218.20 ,49.20, 218.20, 49.20}, 
{'c', 221.00, 50.40, 229.00 ,52.00, 222.20, 45.60}, 
{'c', 215.40, 39.20, 223.00 ,34.40, 223.00, 34.40}, 
{'c', 227.40, 31.60, 213.80 ,32.00, 213.80, 32.00}, 
{'c', 208.60, 27.60, 209.00 ,23.60, 209.00, 23.60}, 
{'c', 217.00, 25.60, 202.60 ,11.20, 200.20, 7.60}, 
{'c', 197.80, 4.00, 207.40 ,-1.20, 207.40, -1.20}, 
{'c', 220.60, -4.80, 209.00 ,-8.00, 209.00, -8.00}, 
{'c', 189.40, -7.60, 200.20 ,-18.40, 200.20, -18.40}, 
{'c', 206.20, -18.00, 204.60 ,-20.40, 204.60, -20.40}, 
{'c', 199.40, -21.60, 189.80 ,-28.00, 189.80, -28.00}, 
{'c', 185.80, -31.60, 189.40 ,-30.80, 189.40, -30.80}, 
{'c', 206.20, -29.60, 177.40 ,-40.80, 177.40, -40.80}, 
{'c', 185.40, -40.80, 167.40 ,-51.20, 167.40, -51.20}, 
{'c', 165.40, -52.80, 162.20 ,-60.40, 162.20, -60.40}, 
{'c', 156.20, -65.60, 151.40 ,-72.40, 151.40, -72.40}, 
{'c', 151.00, -76.80, 146.20 ,-81.60, 146.20, -81.60}, 
{'c', 134.60, -95.20, 129.00 ,-94.80, 129.00, -94.80}, 
{'c', 114.20, -98.40, 109.00 ,-97.60, 109.00, -97.60}, 
{'l', 56.20, -93.20, 0, 0, 0, 0}, 
{'c', 29.80, -80.40, 37.60 ,-59.40, 37.60, -59.40}, 
{'c', 44.00, -51.00, 53.20 ,-54.80, 53.20, -54.80}, 
{'c', 57.80, -61.00, 69.40 ,-58.80, 69.40, -58.80}, 
{'c', 89.80, -55.60, 87.20 ,-59.20, 87.20, -59.20}, 
{'c', 84.80, -63.80, 68.60 ,-70.00, 68.40, -70.60}, 
{'c', 68.20, -71.20, 59.40 ,-74.60, 59.40, -74.60}, 
{'c', 56.40, -75.80, 52.00 ,-85.00, 52.00, -85.00}, 
{'c', 48.80, -88.40, 64.60 ,-82.60, 64.60, -82.60}, 
{'c', 63.40, -81.60, 70.80 ,-77.60, 70.80, -77.60}, 
{'c', 88.20, -78.60, 98.80 ,-67.80, 98.80, -67.80}, 
{'c', 109.60, -51.20, 109.80 ,-59.40, 109.80, -59.40}, 
{'c', 112.60, -68.80, 100.80 ,-90.00, 100.80, -90.00}, 
{'c', 101.20, -92.00, 109.40 ,-85.40, 109.40, -85.40}, 
{'c', 110.80, -87.40, 111.60 ,-81.60, 111.60, -81.60}, 
{'c', 111.80, -79.20, 115.60 ,-71.20, 115.60, -71.20}, 
{'c', 118.40, -58.20, 122.00 ,-65.60, 122.00, -65.60}, 
{'l', 126.60, -56.20, 0, 0, 0, 0}, 
{'c', 128.00, -53.60, 122.00 ,-46.00, 122.00, -46.00}, 
{'c', 121.80, -43.20, 122.60 ,-43.40, 117.00, -35.80}, 
{'c', 111.40, -28.20, 114.80 ,-23.80, 114.80, -23.80}, 
{'c', 113.40, -17.20, 122.20 ,-17.60, 122.20, -17.60}, 
{'c', 124.80, -15.40, 128.20 ,-15.40, 128.20, -15.40}, 
{'c', 130.00, -13.40, 132.40 ,-14.00, 132.40, -14.00}, 
{'c', 134.00, -17.80, 140.20 ,-15.80, 140.20, -15.80}, 
{'c', 141.60, -18.20, 149.80 ,-18.60, 149.80, -18.60}, 
{'c', 150.80, -21.20, 151.20 ,-22.80, 154.60, -23.40}, 
{'c', 158.00, -24.00, 133.40 ,-67.00, 133.40, -67.00}, 
{'c', 139.80, -67.80, 131.60 ,-80.20, 131.60, -80.20}, 
{'c', 129.40, -86.80, 140.80 ,-72.20, 143.00, -70.80}, 
{'c', 145.20, -69.40, 146.20 ,-67.20, 144.60, -67.40}, 
{'c', 143.00, -67.60, 141.20 ,-65.40, 142.60, -65.20}, 
{'c', 144.00, -65.00, 157.00 ,-50.00, 160.40, -39.80}, 
{'c', 163.80, -29.60, 169.80 ,-25.60, 176.00, -19.60}, 
{'c', 182.20, -13.60, 181.40 ,10.60, 181.40, 10.60}, 
{'c', 181.00, 19.40, 187.00 ,30.00, 187.00, 30.00}, 
{'c', 189.00, 33.80, 184.80 ,52.00, 184.80, 52.00}, 
{'c', 182.80, 54.20, 184.20 ,55.00, 184.20, 55.00}, 
{'c', 185.20, 56.20, 192.00 ,69.40, 192.00, 69.40}, 
{'c', 190.20, 69.20, 193.80 ,72.80, 193.80, 72.80}, 
{'c', 199.00, 78.80, 192.60 ,75.80, 192.60, 75.80}, 
{'c', 186.60, 74.20, 193.60 ,84.00, 193.60, 84.00}, 
{'c', 194.80, 85.80, 185.80 ,81.20, 185.80, 81.20}, 
{'c', 176.60, 80.60, 188.20 ,87.80, 188.20, 87.80}, 
{'c', 196.80, 95.00, 185.40 ,90.60, 185.40, 90.60}, 
{'c', 180.80, 88.80, 184.00 ,95.60, 184.00, 95.60}, 
{'c', 187.20, 97.20, 204.40 ,104.20, 204.40, 104.20}, 
{'c', 204.80, 108.00, 201.80 ,113.00, 201.80, 113.00}, 
{'c', 202.20, 117.00, 200.00 ,120.40, 200.00, 120.40}, 
{'c', 198.80, 128.60, 198.20 ,129.40, 198.20, 129.40}, 
{'c', 194.00, 129.60, 186.60 ,143.40, 186.60, 143.40}, 
{'c', 184.80, 146.00, 174.60 ,158.00, 174.60, 158.00}, 
{'c', 172.60, 165.00, 154.60 ,157.80, 154.60, 157.80}, 
{'c', 148.00, 161.20, 150.00 ,157.80, 150.00, 157.80}, 
{'c', 149.60, 155.60, 154.40 ,149.60, 154.40, 149.60}, 
{'c', 161.40, 147.00, 158.80 ,136.20, 158.80, 136.20}, 
{'c', 162.80, 134.80, 151.60 ,132.00, 151.80, 130.80}, 
{'c', 152.00, 129.60, 157.80 ,128.20, 157.80, 128.20}, 
{'c', 165.80, 126.20, 161.40 ,123.80, 161.40, 123.80}, 
{'c', 160.80, 119.80, 163.80 ,114.20, 163.80, 114.20}, 
{'c', 175.40, 113.40, 163.80 ,97.20, 163.80, 97.20}, 
{'c', 153.00, 89.60, 152.00 ,83.80, 152.00, 83.80}, 
{'c', 164.60, 75.60, 156.40 ,63.20, 156.60, 59.60}, 
{'c', 156.80, 56.00, 158.00 ,34.40, 158.00, 34.40}, 
{'c', 156.00, 28.20, 153.00 ,14.60, 153.00, 14.60}, 
{'c', 155.20, 9.40, 162.60 ,-3.20, 162.60, -3.20}, 
{'c', 165.40, -7.40, 174.20 ,-12.20, 172.00, -15.20}, 
{'c', 169.80, -18.20, 162.00 ,-16.40, 162.00, -16.40}, 
{'c', 154.20, -17.80, 154.80 ,-12.60, 154.80, -12.60}, 
{'c', 153.20, -11.60, 152.40 ,-6.60, 152.40, -6.60}, 
{'c', 151.68, 1.33, 142.80 ,7.60, 142.80, 7.60}, 
{'c', 131.60, 13.80, 140.80 ,17.80, 140.80, 17.80}, 
{'c', 146.80, 24.40, 137.00 ,24.60, 137.00, 24.60}, 
{'c', 126.00, 22.80, 134.20 ,33.00, 134.20, 33.00}, 
{'c', 145.00, 45.80, 142.00 ,48.60, 142.00, 48.60}, 
{'c', 131.80, 49.60, 144.40 ,58.80, 144.40, 58.80}, 
{'c', 144.40, 58.80, 143.60 ,56.80, 143.80, 58.60}, 
{'c', 144.00, 60.40, 147.00 ,64.60, 147.80, 66.60}, 
{'c', 148.60, 68.60, 144.60 ,68.80, 144.60, 68.80}, 
{'c', 145.20, 78.40, 129.80 ,74.20, 129.80, 74.20}, 
{'c', 129.80, 74.20, 129.80 ,74.20, 128.20, 74.40}, 
{'c', 126.60, 74.60, 115.40 ,73.80, 109.60, 71.60}, 
{'c', 103.80, 69.40, 97.00 ,69.40, 97.00, 69.40}, 
{'c', 97.00, 69.40, 93.00 ,71.20, 85.40, 71.00}, 
{'c', 77.80, 70.80, 69.80 ,73.60, 69.80, 73.60}, 
{'c', 65.40, 73.20, 74.00 ,68.80, 74.20, 69.00}, 
{'c', 74.40, 69.20, 80.00 ,63.60, 72.00, 64.20}, 
{'c', 50.20, 65.83, 39.40 ,55.60, 39.40, 55.60}, 
{'c', 37.40, 54.20, 34.80 ,51.40, 34.80, 51.40}, 
{'c', 24.80, 49.40, 36.20 ,63.80, 36.20, 63.80}, 
{'c', 37.40, 65.20, 36.00 ,66.20, 36.00, 66.20}, 
{'c', 35.20, 64.60, 27.40 ,59.20, 27.40, 59.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -3.00, 42.80, 0, 0, 0, 0}, 
{'c', -3.00, 42.80, 8.60 ,48.40, 11.20, 51.20}, 
{'c', 13.80, 54.00, 27.80 ,65.40, 27.80, 65.40}, 
{'c', 27.80, 65.40, 22.40 ,63.40, 19.80, 61.60}, 
{'c', 17.20, 59.80, 6.40 ,51.60, 6.40, 51.60}, 
{'f', 0.268000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -61.01, 11.60, 0, 0, 0, 0}, 
{'c', -60.67, 11.46, -61.20 ,8.74, -61.40, 8.20}, 
{'c', -62.42, 5.47, -71.40 ,4.00, -71.40, 4.00}, 
{'c', -71.63, 5.37, -71.68 ,6.96, -71.58, 8.60}, 
{'f', 0.603000,0.804000,0.201000,1.000000,0,0 }, 
{'m', -61.01, 11.40, 0, 0, 0, 0}, 
{'c', -61.46, 11.56, -61.02 ,8.67, -61.20, 8.20}, 
{'c', -62.22, 5.47, -71.40 ,3.90, -71.40, 3.90}, 
{'c', -71.63, 5.26, -71.68 ,6.86, -71.58, 8.50}, 
{'f', 0.402000,0.603000,0.000000,1.000000,0,0 }, 
{'m', -65.40, 11.55, 0, 0, 0, 0}, 
{'c', -66.03, 11.55, -66.53 ,10.41, -66.53, 9.00}, 
{'c', -66.53, 7.59, -66.03 ,6.46, -65.40, 6.46}, 
{'c', -64.78, 6.46, -64.27 ,7.59, -64.27, 9.00}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -111.00, 109.60, 0, 0, 0, 0}, 
{'c', -111.00, 109.60, -116.60 ,119.60, -91.80, 113.60}, 
{'c', -91.80, 113.60, -77.80 ,112.40, -75.40, 110.00}, 
{'c', -74.20, 110.80, -65.83 ,113.73, -63.00, 114.40}, 
{'c', -56.20, 116.00, -47.80 ,106.00, -47.80, 106.00}, 
{'c', -47.80, 106.00, -43.20 ,95.50, -40.40, 95.50}, 
{'c', -37.60, 95.50, -40.80 ,97.10, -40.80, 97.10}, 
{'c', -40.80, 97.10, -47.40 ,107.20, -47.00, 108.80}, 
{'c', -47.00, 108.80, -52.20 ,128.80, -68.20, 129.60}, 
{'c', -68.20, 129.60, -84.35 ,130.55, -83.00, 136.40}, 
{'c', -83.00, 136.40, -74.20 ,134.00, -71.80, 136.40}, 
{'c', -71.80, 136.40, -61.00 ,136.00, -69.00, 142.40}, 
{'l', -75.80, 154.00, 0, 0, 0, 0}, 
{'c', -75.80, 154.00, -75.66 ,157.92, -85.80, 154.40}, 
{'c', -95.60, 151.00, -105.90 ,138.10, -105.90, 138.10}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -112.20, 113.60, 0, 0, 0, 0}, 
{'c', -112.20, 113.60, -114.20 ,123.20, -77.40, 112.80}, 
{'c', -77.40, 112.80, -73.00 ,112.80, -70.60, 113.60}, 
{'c', -68.20, 114.40, -56.20 ,117.20, -54.20, 116.00}, 
{'c', -54.20, 116.00, -61.40 ,129.60, -73.00, 128.00}, 
{'c', -73.00, 128.00, -86.20 ,129.60, -85.80, 134.40}, 
{'c', -85.80, 134.40, -81.80 ,141.60, -77.00, 144.00}, 
{'c', -77.00, 144.00, -74.20 ,146.40, -74.60, 149.60}, 
{'c', -75.00, 152.80, -77.80 ,154.40, -79.80, 155.20}, 
{'c', -81.80, 156.00, -85.00 ,152.80, -86.60, 152.80}, 
{'c', -88.20, 152.80, -96.60 ,146.40, -101.00, 141.60}, 
{'c', -105.40, 136.80, -113.80 ,124.80, -113.40, 122.00}, 
{'f', 0.938000,0.603000,0.603000,1.000000,0,0 }, 
{'m', -109.00, 131.05, 0, 0, 0, 0}, 
{'c', -106.40, 135.00, -103.20 ,139.20, -101.00, 141.60}, 
{'c', -96.60, 146.40, -88.20 ,152.80, -86.60, 152.80}, 
{'c', -85.00, 152.80, -81.80 ,156.00, -79.80, 155.20}, 
{'c', -77.80, 154.40, -75.00 ,152.80, -74.60, 149.60}, 
{'c', -74.20, 146.40, -77.00 ,144.00, -77.00, 144.00}, 
{'c', -80.07, 142.47, -82.81 ,138.98, -84.39, 136.65}, 
{'c', -84.39, 136.65, -84.20 ,139.20, -89.40, 138.40}, 
{'c', -94.60, 137.60, -99.80 ,134.80, -101.40, 131.60}, 
{'c', -103.00, 128.40, -105.40 ,126.00, -103.80, 129.60}, 
{'c', -102.20, 133.20, -99.80 ,136.80, -98.20, 137.20}, 
{'c', -96.60, 137.60, -97.00 ,138.80, -99.40, 138.40}, 
{'f', 0.737000,0.402000,0.402000,1.000000,0,0 }, 
{'m', -111.60, 110.00, 0, 0, 0, 0}, 
{'c', -111.60, 110.00, -109.80 ,96.40, -108.60, 92.40}, 
{'c', -108.60, 92.40, -109.40 ,85.60, -107.00, 81.40}, 
{'c', -104.60, 77.20, -102.60 ,71.00, -99.60, 65.60}, 
{'c', -96.60, 60.20, -96.40 ,56.20, -92.40, 54.60}, 
{'c', -88.40, 53.00, -82.40 ,44.40, -79.60, 43.40}, 
{'c', -76.80, 42.40, -77.00 ,43.20, -77.00, 43.20}, 
{'c', -77.00, 43.20, -70.20 ,28.40, -56.60, 32.40}, 
{'c', -56.60, 32.40, -72.80 ,29.60, -57.00, 20.20}, 
{'c', -57.00, 20.20, -61.80 ,21.30, -58.50, 14.30}, 
{'c', -56.30, 9.63, -56.80 ,16.40, -67.80, 28.20}, 
{'c', -67.80, 28.20, -72.80 ,36.80, -78.00, 39.80}, 
{'c', -83.20, 42.80, -95.20 ,49.80, -96.40, 53.60}, 
{'c', -97.60, 57.40, -100.80 ,63.20, -102.80, 64.80}, 
{'c', -104.80, 66.40, -107.60 ,70.60, -108.00, 74.00}, 
{'c', -108.00, 74.00, -109.20 ,78.00, -110.60, 79.20}, 
{'c', -112.00, 80.40, -112.20 ,83.60, -112.20, 85.60}, 
{'c', -112.20, 87.60, -114.20 ,90.40, -114.00, 92.80}, 
{'c', -114.00, 92.80, -113.20 ,111.80, -113.60, 113.80}, 
{'f', 0.603000,0.134000,0.000000,1.000000,0,0 }, 
{'m', -120.20, 114.60, 0, 0, 0, 0}, 
{'c', -120.20, 114.60, -122.20 ,113.20, -126.60, 119.20}, 
{'c', -126.60, 119.20, -119.30 ,152.20, -119.30, 153.60}, 
{'c', -119.30, 153.60, -118.20 ,151.50, -119.50, 144.30}, 
{'c', -120.80, 137.10, -121.70 ,124.40, -121.70, 124.40}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -98.60, 54.00, 0, 0, 0, 0}, 
{'c', -98.60, 54.00, -116.20 ,57.20, -115.80, 86.40}, 
{'l', -116.60, 111.20, 0, 0, 0, 0}, 
{'c', -116.60, 111.20, -117.80 ,85.60, -119.00, 84.00}, 
{'c', -120.20, 82.40, -116.20 ,71.20, -119.40, 77.20}, 
{'c', -119.40, 77.20, -133.40 ,91.20, -125.40, 112.40}, 
{'c', -125.40, 112.40, -123.90 ,115.70, -126.90, 111.10}, 
{'c', -126.90, 111.10, -131.50 ,98.50, -130.40, 92.10}, 
{'c', -130.40, 92.10, -130.20 ,89.90, -128.30, 87.10}, 
{'c', -128.30, 87.10, -119.70 ,75.40, -117.00, 73.10}, 
{'c', -117.00, 73.10, -115.20 ,58.70, -99.80, 53.50}, 
{'f', 0.603000,0.134000,0.000000,1.000000,0,0 }, 
{'m', 40.80, -12.20, 0, 0, 0, 0}, 
{'c', 41.46, -12.55, 41.45 ,-13.52, 42.03, -13.70}, 
{'c', 43.18, -14.04, 43.34 ,-15.11, 43.86, -15.89}, 
{'c', 44.73, -17.21, 44.93 ,-18.74, 45.51, -20.23}, 
{'c', 45.78, -20.93, 45.81 ,-21.89, 45.50, -22.55}, 
{'c', 44.32, -25.03, 43.62 ,-27.48, 42.18, -29.91}, 
{'c', 41.91, -30.36, 41.65 ,-31.15, 41.45, -31.75}, 
{'c', 40.98, -33.13, 39.73 ,-34.12, 38.87, -35.44}, 
{'c', 38.58, -35.88, 39.10 ,-36.81, 38.39, -36.89}, 
{'c', 37.49, -37.00, 36.04 ,-37.58, 35.81, -36.55}, 
{'c', 35.22, -33.97, 36.23 ,-31.44, 37.20, -29.00}, 
{'c', 36.42, -28.31, 36.75 ,-27.39, 36.90, -26.62}, 
{'c', 37.61, -23.01, 36.42 ,-19.66, 35.66, -16.19}, 
{'c', 35.63, -16.08, 35.97 ,-15.89, 35.95, -15.82}, 
{'c', 34.72, -13.14, 33.27 ,-10.69, 31.45, -8.31}, 
{'c', 30.70, -7.32, 29.82 ,-6.40, 29.33, -5.34}, 
{'c', 28.96, -4.55, 28.55 ,-3.59, 28.80, -2.60}, 
{'c', 25.36, 0.18, 23.11 ,4.03, 20.50, 7.87}, 
{'c', 20.04, 8.55, 20.33 ,9.76, 20.88, 10.03}, 
{'c', 21.70, 10.43, 22.65 ,9.40, 23.12, 8.56}, 
{'c', 23.51, 7.86, 23.86 ,7.21, 24.36, 6.57}, 
{'c', 24.49, 6.39, 24.31 ,5.97, 24.45, 5.85}, 
{'c', 27.08, 3.50, 28.75 ,0.57, 31.20, -1.80}, 
{'c', 33.15, -2.13, 34.69 ,-3.13, 36.44, -4.14}, 
{'c', 36.74, -4.32, 37.27 ,-4.07, 37.56, -4.26}, 
{'c', 39.31, -5.44, 39.31 ,-7.48, 39.41, -9.39}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 31.96, -16.67, 0, 0, 0, 0}, 
{'c', 32.08, -16.74, 31.93 ,-17.17, 32.04, -17.38}, 
{'c', 32.20, -17.71, 32.60 ,-17.89, 32.76, -18.22}, 
{'c', 32.87, -18.43, 32.71 ,-18.81, 32.85, -18.96}, 
{'c', 35.18, -21.40, 35.44 ,-24.43, 34.40, -27.40}, 
{'c', 35.42, -28.02, 35.48 ,-29.28, 35.06, -30.13}, 
{'c', 34.21, -31.83, 34.01 ,-33.76, 33.04, -35.30}, 
{'c', 32.24, -36.57, 30.66 ,-37.81, 29.29, -36.51}, 
{'c', 28.87, -36.11, 28.55 ,-35.32, 28.82, -34.61}, 
{'c', 28.89, -34.45, 29.17 ,-34.30, 29.15, -34.22}, 
{'c', 29.04, -33.89, 28.49 ,-33.67, 28.49, -33.40}, 
{'c', 28.46, -31.90, 27.50 ,-30.39, 28.13, -29.06}, 
{'c', 28.91, -27.43, 29.72 ,-25.58, 30.40, -23.80}, 
{'c', 29.17, -21.68, 30.20 ,-19.23, 28.45, -17.36}, 
{'c', 28.31, -17.21, 28.32 ,-16.83, 28.44, -16.62}, 
{'c', 28.73, -16.14, 29.14 ,-15.73, 29.62, -15.44}, 
{'c', 29.83, -15.32, 30.18 ,-15.32, 30.38, -15.44}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 94.77, -26.98, 0, 0, 0, 0}, 
{'c', 96.16, -25.18, 96.45 ,-22.39, 94.40, -21.00}, 
{'c', 94.95, -17.69, 98.30 ,-19.67, 100.40, -20.20}, 
{'c', 100.29, -20.59, 100.52 ,-20.93, 100.80, -20.94}, 
{'c', 101.86, -20.95, 102.54 ,-21.98, 103.60, -21.80}, 
{'c', 104.03, -23.36, 105.67 ,-24.06, 106.32, -25.44}, 
{'c', 108.04, -29.13, 107.45 ,-33.41, 104.87, -36.65}, 
{'c', 104.67, -36.91, 104.88 ,-37.42, 104.76, -37.79}, 
{'c', 104.00, -40.00, 101.94 ,-40.31, 100.00, -41.00}, 
{'c', 98.82, -44.88, 98.16 ,-48.91, 96.40, -52.60}, 
{'c', 94.79, -52.85, 94.09 ,-54.59, 92.75, -55.31}, 
{'c', 91.42, -56.03, 90.85 ,-54.45, 90.89, -53.40}, 
{'c', 90.90, -53.20, 91.35 ,-52.97, 91.18, -52.61}, 
{'c', 91.11, -52.45, 90.84 ,-52.33, 90.84, -52.20}, 
{'c', 90.85, -52.06, 91.07 ,-51.93, 91.20, -51.80}, 
{'c', 90.28, -50.98, 88.86 ,-50.50, 88.56, -49.36}, 
{'c', 87.61, -45.65, 90.18 ,-42.52, 91.85, -39.32}, 
{'c', 92.44, -38.19, 91.71 ,-36.92, 90.95, -35.71}, 
{'c', 90.51, -35.01, 90.62 ,-33.89, 90.89, -33.03}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 57.61, -8.59, 0, 0, 0, 0}, 
{'c', 56.12, -6.74, 52.71 ,-4.17, 55.63, -2.24}, 
{'c', 55.82, -2.11, 56.19 ,-2.11, 56.37, -2.24}, 
{'c', 58.39, -3.81, 60.39 ,-4.71, 62.83, -5.29}, 
{'c', 62.95, -5.32, 63.22 ,-4.86, 63.59, -5.02}, 
{'c', 65.21, -5.72, 67.22 ,-5.66, 68.40, -7.00}, 
{'c', 72.17, -6.78, 75.73 ,-7.89, 79.12, -9.20}, 
{'c', 80.28, -9.65, 81.55 ,-10.21, 82.75, -10.71}, 
{'c', 84.13, -11.29, 85.33 ,-12.21, 86.45, -13.35}, 
{'c', 86.58, -13.49, 86.93 ,-13.40, 87.20, -13.40}, 
{'c', 87.16, -14.26, 88.12 ,-14.39, 88.37, -15.01}, 
{'c', 88.46, -15.24, 88.31 ,-15.64, 88.44, -15.74}, 
{'c', 90.58, -17.37, 91.50 ,-19.39, 90.33, -21.77}, 
{'c', 90.05, -22.34, 89.80 ,-22.96, 89.23, -23.44}, 
{'c', 88.15, -24.35, 87.05 ,-23.50, 86.00, -23.80}, 
{'c', 85.84, -23.17, 85.11 ,-23.34, 84.73, -23.15}, 
{'c', 83.87, -22.71, 82.53 ,-23.29, 81.67, -22.85}, 
{'c', 80.31, -22.16, 79.07 ,-21.99, 77.65, -21.61}, 
{'c', 77.34, -21.53, 76.56 ,-21.63, 76.40, -21.00}, 
{'c', 76.27, -21.13, 76.12 ,-21.37, 76.01, -21.35}, 
{'c', 74.10, -20.95, 72.84 ,-20.74, 71.54, -19.04}, 
{'c', 71.44, -18.91, 71.00 ,-19.09, 70.84, -18.95}, 
{'c', 69.88, -18.15, 69.48 ,-16.91, 68.38, -16.24}, 
{'c', 68.17, -16.12, 67.82 ,-16.29, 67.63, -16.16}, 
{'c', 66.98, -15.73, 66.62 ,-15.09, 65.97, -14.64}, 
{'c', 65.64, -14.41, 65.25 ,-14.73, 65.28, -14.99}, 
{'c', 65.52, -16.94, 66.17 ,-18.72, 65.60, -20.60}, 
{'c', 67.68, -23.12, 70.19 ,-25.07, 72.00, -27.80}, 
{'c', 72.02, -29.97, 72.71 ,-32.11, 72.59, -34.19}, 
{'c', 72.58, -34.38, 72.30 ,-35.12, 72.17, -35.46}, 
{'c', 71.86, -36.32, 72.76 ,-37.38, 71.92, -38.11}, 
{'c', 70.52, -39.31, 69.22 ,-38.43, 68.40, -37.00}, 
{'c', 66.56, -36.61, 64.50 ,-35.92, 62.92, -37.15}, 
{'c', 61.91, -37.94, 61.33 ,-38.84, 60.53, -39.90}, 
{'c', 59.55, -41.20, 59.88 ,-42.64, 59.95, -44.20}, 
{'c', 59.96, -44.33, 59.65 ,-44.47, 59.65, -44.60}, 
{'c', 59.65, -44.73, 59.87 ,-44.87, 60.00, -45.00}, 
{'c', 59.29, -45.63, 59.02 ,-46.68, 58.00, -47.00}, 
{'c', 58.30, -48.09, 57.63 ,-48.98, 56.76, -49.28}, 
{'c', 54.76, -49.97, 53.09 ,-48.06, 51.19, -47.98}, 
{'c', 50.68, -47.97, 50.21 ,-49.00, 49.56, -49.33}, 
{'c', 49.13, -49.54, 48.43 ,-49.58, 48.07, -49.31}, 
{'c', 47.38, -48.81, 46.79 ,-48.69, 46.03, -48.49}, 
{'c', 44.41, -48.05, 43.14 ,-46.96, 41.66, -46.10}, 
{'c', 40.17, -45.25, 39.22 ,-43.81, 38.14, -42.49}, 
{'c', 37.20, -41.34, 37.06 ,-38.92, 38.48, -38.42}, 
{'c', 40.32, -37.77, 41.63 ,-40.48, 43.59, -40.15}, 
{'c', 43.90, -40.10, 44.11 ,-39.79, 44.00, -39.40}, 
{'c', 44.39, -39.29, 44.61 ,-39.52, 44.80, -39.80}, 
{'c', 45.66, -38.78, 46.82 ,-38.44, 47.76, -37.57}, 
{'c', 48.73, -36.67, 50.48 ,-37.09, 51.49, -36.09}, 
{'c', 53.02, -34.59, 52.46 ,-31.91, 54.40, -30.60}, 
{'c', 53.81, -29.29, 53.21 ,-28.01, 52.87, -26.58}, 
{'c', 52.59, -25.38, 53.58 ,-24.18, 54.80, -24.27}, 
{'c', 56.05, -24.36, 56.31 ,-25.12, 56.80, -26.20}, 
{'c', 57.07, -25.93, 57.54 ,-25.64, 57.49, -25.42}, 
{'c', 57.04, -23.03, 56.01 ,-21.04, 55.55, -18.61}, 
{'c', 55.49, -18.29, 55.19 ,-18.09, 54.80, -18.20}, 
{'c', 54.33, -14.05, 50.28 ,-11.66, 47.73, -8.49}, 
{'c', 47.33, -7.99, 47.33 ,-6.74, 47.74, -6.34}, 
{'c', 49.14, -4.95, 51.10 ,-6.50, 52.80, -7.00}, 
{'c', 53.01, -8.21, 53.87 ,-9.15, 55.20, -9.09}, 
{'c', 55.46, -9.08, 55.70 ,-9.62, 56.02, -9.75}, 
{'c', 56.37, -9.89, 56.87 ,-9.67, 57.16, -9.87}, 
{'c', 58.88, -11.06, 60.29 ,-12.17, 62.03, -13.36}, 
{'c', 62.22, -13.49, 62.57 ,-13.33, 62.78, -13.44}, 
{'c', 63.11, -13.60, 63.29 ,-13.98, 63.62, -14.17}, 
{'c', 63.97, -14.37, 64.21 ,-14.08, 64.40, -13.80}, 
{'c', 63.75, -13.45, 63.75 ,-12.49, 63.17, -12.29}, 
{'c', 62.39, -12.02, 61.83 ,-11.51, 61.16, -11.06}, 
{'c', 60.87, -10.87, 60.21 ,-11.12, 60.10, -10.94}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 2.20, -58.00, 0, 0, 0, 0}, 
{'c', 2.20, -58.00, -7.04 ,-60.87, -18.20, -35.20}, 
{'c', -18.20, -35.20, -20.60 ,-30.00, -23.00, -28.00}, 
{'c', -25.40, -26.00, -36.60 ,-22.40, -38.60, -18.40}, 
{'l', -49.00, -2.40, 0, 0, 0, 0}, 
{'c', -49.00, -2.40, -34.20 ,-18.40, -31.00, -20.80}, 
{'c', -31.00, -20.80, -23.00 ,-29.20, -26.20, -22.40}, 
{'c', -26.20, -22.40, -40.20 ,-11.60, -39.00, -2.40}, 
{'c', -39.00, -2.40, -44.60 ,12.00, -45.40, 14.00}, 
{'c', -45.40, 14.00, -29.40 ,-18.00, -27.00, -19.20}, 
{'c', -24.60, -20.40, -23.40 ,-20.40, -24.60, -16.80}, 
{'c', -25.80, -13.20, -26.20 ,3.20, -29.00, 5.20}, 
{'c', -29.00, 5.20, -21.00 ,-15.20, -21.80, -18.40}, 
{'c', -21.80, -18.40, -18.60 ,-22.00, -16.20, -16.80}, 
{'l', -17.40, -0.80, 0, 0, 0, 0}, 
{'l', -13.00, 11.20, 0, 0, 0, 0}, 
{'c', -13.00, 11.20, -15.40 ,0.00, -13.80, -15.60}, 
{'c', -13.80, -15.60, -15.80 ,-26.00, -11.80, -20.40}, 
{'c', -7.80, -14.80, 1.80 ,-8.80, 1.80, -4.00}, 
{'c', 1.80, -4.00, -3.40 ,-21.60, -12.60, -26.40}, 
{'l', -16.60, -20.40, 0, 0, 0, 0}, 
{'l', -17.80, -22.40, 0, 0, 0, 0}, 
{'c', -17.80, -22.40, -21.40 ,-23.20, -17.00, -30.00}, 
{'c', -12.60, -36.80, -13.00 ,-37.60, -13.00, -37.60}, 
{'c', -13.00, -37.60, -6.60 ,-30.40, -5.00, -30.40}, 
{'c', -5.00, -30.40, 8.20 ,-38.00, 9.40, -13.60}, 
{'c', 9.40, -13.60, 16.20 ,-28.00, 7.00, -34.80}, 
{'c', 7.00, -34.80, -7.80 ,-36.80, -6.60, -42.00}, 
{'l', 0.60, -54.40, 0, 0, 0, 0}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -17.80, -41.60, 0, 0, 0, 0}, 
{'c', -17.80, -41.60, -30.60 ,-41.60, -33.80, -36.40}, 
{'l', -41.00, -26.80, 0, 0, 0, 0}, 
{'c', -41.00, -26.80, -23.80 ,-36.80, -19.80, -38.00}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -57.80, -35.20, 0, 0, 0, 0}, 
{'c', -57.80, -35.20, -59.80 ,-34.00, -60.20, -31.20}, 
{'c', -60.60, -28.40, -63.00 ,-28.00, -62.20, -25.20}, 
{'c', -61.40, -22.40, -59.40 ,-20.00, -59.40, -24.00}, 
{'c', -59.40, -28.00, -57.80 ,-30.00, -57.00, -31.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -66.60, 26.00, 0, 0, 0, 0}, 
{'c', -66.60, 26.00, -75.00 ,22.00, -78.20, 18.40}, 
{'c', -81.40, 14.80, -80.95 ,19.97, -85.80, 19.60}, 
{'c', -91.65, 19.16, -90.60 ,3.20, -90.60, 3.20}, 
{'l', -94.60, 10.80, 0, 0, 0, 0}, 
{'c', -94.60, 10.80, -95.80 ,25.20, -87.80, 22.80}, 
{'c', -83.89, 21.63, -82.60 ,23.20, -84.20, 24.00}, 
{'c', -85.80, 24.80, -78.60 ,25.20, -81.40, 26.80}, 
{'c', -84.20, 28.40, -69.80 ,23.20, -72.20, 33.60}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -79.20, 40.40, 0, 0, 0, 0}, 
{'c', -79.20, 40.40, -94.60 ,44.80, -98.20, 35.20}, 
{'c', -98.20, 35.20, -103.00 ,37.60, -100.80, 40.60}, 
{'c', -98.60, 43.60, -97.40 ,44.00, -97.40, 44.00}, 
{'c', -97.40, 44.00, -92.00 ,45.20, -92.60, 46.00}, 
{'c', -93.20, 46.80, -95.60 ,50.20, -95.60, 50.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 149.20, 118.60, 0, 0, 0, 0}, 
{'c', 148.77, 120.73, 147.10 ,121.54, 145.20, 122.20}, 
{'c', 143.28, 121.24, 140.69 ,118.14, 138.80, 120.20}, 
{'c', 138.33, 119.72, 137.55 ,119.66, 137.20, 119.00}, 
{'c', 136.74, 118.10, 137.01 ,117.06, 136.67, 116.26}, 
{'c', 136.12, 114.98, 135.41 ,113.62, 135.60, 112.20}, 
{'c', 137.41, 111.49, 138.00 ,109.58, 137.53, 107.82}, 
{'c', 137.46, 107.56, 137.03 ,107.37, 137.23, 107.02}, 
{'c', 137.42, 106.69, 137.73 ,106.47, 138.00, 106.20}, 
{'c', 137.87, 106.33, 137.72 ,106.57, 137.61, 106.55}, 
{'c', 137.00, 106.44, 137.12 ,105.81, 137.25, 105.42}, 
{'c', 137.84, 103.67, 139.85 ,103.41, 141.20, 104.60}, 
{'c', 141.46, 104.03, 141.97 ,104.23, 142.40, 104.20}, 
{'c', 142.35, 103.62, 142.76 ,103.09, 142.96, 102.67}, 
{'c', 143.47, 101.58, 145.10 ,102.68, 145.90, 102.07}, 
{'c', 146.98, 101.25, 148.04 ,100.55, 149.12, 101.15}, 
{'c', 150.93, 102.16, 152.64 ,103.37, 153.84, 105.11}, 
{'c', 154.41, 105.95, 154.65 ,107.23, 154.59, 108.19}, 
{'c', 154.55, 108.83, 153.17 ,108.48, 152.83, 109.41}, 
{'c', 152.19, 111.16, 154.02 ,111.68, 154.77, 113.02}, 
{'c', 154.97, 113.37, 154.71 ,113.67, 154.39, 113.77}, 
{'c', 153.98, 113.90, 153.20 ,113.71, 153.33, 114.16}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 139.60, 138.20, 0, 0, 0, 0}, 
{'c', 139.59, 136.46, 137.99 ,134.71, 139.20, 133.00}, 
{'c', 139.34, 133.13, 139.47 ,133.36, 139.60, 133.36}, 
{'c', 139.74, 133.36, 139.87 ,133.13, 140.00, 133.00}, 
{'c', 141.50, 135.22, 145.15 ,136.15, 145.01, 138.99}, 
{'c', 144.98, 139.44, 143.90 ,140.36, 144.80, 141.00}, 
{'c', 142.99, 142.35, 142.93 ,144.72, 142.00, 146.60}, 
{'c', 140.76, 146.31, 139.55 ,145.95, 138.40, 145.40}, 
{'c', 138.75, 143.91, 138.64 ,142.23, 139.46, 140.91}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -26.60, 129.20, 0, 0, 0, 0}, 
{'c', -26.60, 129.20, -43.46 ,139.34, -29.40, 124.00}, 
{'c', -20.60, 114.40, -10.60 ,108.80, -10.60, 108.80}, 
{'c', -10.60, 108.80, -0.20 ,104.40, 3.40, 103.20}, 
{'c', 7.00, 102.00, 22.20 ,96.80, 25.40, 96.40}, 
{'c', 28.60, 96.00, 38.20 ,92.00, 45.00, 96.00}, 
{'c', 51.80, 100.00, 59.80 ,104.40, 59.80, 104.40}, 
{'c', 59.80, 104.40, 43.40 ,96.00, 39.80, 98.40}, 
{'c', 36.20, 100.80, 29.00 ,100.40, 23.00, 103.60}, 
{'c', 23.00, 103.60, 8.20 ,108.00, 5.00, 110.00}, 
{'c', 1.80, 112.00, -8.60 ,123.60, -10.20, 122.80}, 
{'c', -11.80, 122.00, -9.80 ,121.60, -8.60, 118.80}, 
{'c', -7.40, 116.00, -9.40 ,114.40, -17.40, 120.80}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -19.20, 123.23, 0, 0, 0, 0}, 
{'c', -19.20, 123.23, -17.79 ,110.19, -9.31, 111.86}, 
{'c', -9.31, 111.86, -1.08 ,107.69, 1.64, 105.72}, 
{'c', 1.64, 105.72, 9.78 ,104.02, 11.09, 103.40}, 
{'c', 29.57, 94.70, 44.29 ,99.22, 44.84, 98.10}, 
{'c', 45.38, 96.98, 65.01 ,104.10, 68.61, 108.19}, 
{'c', 69.01, 108.63, 58.38 ,102.59, 48.69, 100.70}, 
{'c', 40.41, 99.08, 18.81 ,100.94, 7.91, 106.48}, 
{'c', 4.93, 107.99, -4.01 ,113.77, -6.54, 113.66}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -23.00, 148.80, 0, 0, 0, 0}, 
{'c', -23.00, 148.80, -38.20 ,146.40, -21.40, 144.80}, 
{'c', -21.40, 144.80, -3.40 ,142.80, 0.60, 137.60}, 
{'c', 0.60, 137.60, 14.20 ,128.40, 17.00, 128.00}, 
{'c', 19.80, 127.60, 49.80 ,120.40, 50.20, 118.00}, 
{'c', 50.60, 115.60, 56.20 ,115.60, 57.80, 116.40}, 
{'c', 59.40, 117.20, 58.60 ,118.40, 55.80, 119.20}, 
{'c', 53.00, 120.00, 21.80 ,136.40, 15.40, 137.60}, 
{'c', 9.00, 138.80, -2.60 ,146.40, -7.40, 147.60}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -3.48, 141.40, 0, 0, 0, 0}, 
{'c', -3.48, 141.40, -12.06 ,140.57, -3.46, 139.75}, 
{'c', -3.46, 139.75, 5.36 ,136.33, 7.40, 133.67}, 
{'c', 7.40, 133.67, 14.37 ,128.96, 15.80, 128.75}, 
{'c', 17.23, 128.55, 31.19 ,124.86, 31.40, 123.63}, 
{'c', 31.60, 122.40, 65.67 ,109.82, 70.09, 113.01}, 
{'c', 73.00, 115.11, 63.10 ,113.44, 53.47, 117.85}, 
{'c', 52.11, 118.47, 18.26 ,133.05, 14.98, 133.67}, 
{'c', 11.70, 134.28, 5.76 ,138.17, 3.31, 138.79}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -11.40, 143.60, 0, 0, 0, 0}, 
{'c', -11.40, 143.60, -6.20 ,143.20, -7.40, 144.80}, 
{'c', -8.60, 146.40, -11.00 ,145.60, -11.00, 145.60}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -18.60, 145.20, 0, 0, 0, 0}, 
{'c', -18.60, 145.20, -13.40 ,144.80, -14.60, 146.40}, 
{'c', -15.80, 148.00, -18.20 ,147.20, -18.20, 147.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -29.00, 146.80, 0, 0, 0, 0}, 
{'c', -29.00, 146.80, -23.80 ,146.40, -25.00, 148.00}, 
{'c', -26.20, 149.60, -28.60 ,148.80, -28.60, 148.80}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -36.60, 147.60, 0, 0, 0, 0}, 
{'c', -36.60, 147.60, -31.40 ,147.20, -32.60, 148.80}, 
{'c', -33.80, 150.40, -36.20 ,149.60, -36.20, 149.60}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 1.80, 108.00, 0, 0, 0, 0}, 
{'c', 1.80, 108.00, 6.20 ,108.00, 5.00, 109.60}, 
{'c', 3.80, 111.20, 0.60 ,110.80, 0.60, 110.80}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -8.20, 113.60, 0, 0, 0, 0}, 
{'c', -8.20, 113.60, -1.69 ,111.46, -4.20, 114.80}, 
{'c', -5.40, 116.40, -7.80 ,115.60, -7.80, 115.60}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -19.40, 118.40, 0, 0, 0, 0}, 
{'c', -19.40, 118.40, -14.20 ,118.00, -15.40, 119.60}, 
{'c', -16.60, 121.20, -19.00 ,120.40, -19.00, 120.40}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -27.00, 124.40, 0, 0, 0, 0}, 
{'c', -27.00, 124.40, -21.80 ,124.00, -23.00, 125.60}, 
{'c', -24.20, 127.20, -26.60 ,126.40, -26.60, 126.40}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -33.80, 129.20, 0, 0, 0, 0}, 
{'c', -33.80, 129.20, -28.60 ,128.80, -29.80, 130.40}, 
{'c', -31.00, 132.00, -33.40 ,131.20, -33.40, 131.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 5.28, 135.60, 0, 0, 0, 0}, 
{'c', 5.28, 135.60, 12.20 ,135.07, 10.61, 137.19}, 
{'c', 9.01, 139.32, 5.81 ,138.26, 5.81, 138.26}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 15.68, 130.80, 0, 0, 0, 0}, 
{'c', 15.68, 130.80, 22.60 ,130.27, 21.01, 132.40}, 
{'c', 19.41, 134.53, 16.21 ,133.46, 16.21, 133.46}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 26.48, 126.40, 0, 0, 0, 0}, 
{'c', 26.48, 126.40, 33.40 ,125.87, 31.81, 128.00}, 
{'c', 30.21, 130.12, 27.01 ,129.06, 27.01, 129.06}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 36.88, 121.60, 0, 0, 0, 0}, 
{'c', 36.88, 121.60, 43.80 ,121.07, 42.21, 123.19}, 
{'c', 40.61, 125.33, 37.41 ,124.26, 37.41, 124.26}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 9.28, 103.60, 0, 0, 0, 0}, 
{'c', 9.28, 103.60, 16.20 ,103.07, 14.61, 105.19}, 
{'c', 13.01, 107.33, 9.01 ,107.06, 9.01, 107.06}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 19.28, 100.40, 0, 0, 0, 0}, 
{'c', 19.28, 100.40, 26.20 ,99.87, 24.61, 102.00}, 
{'c', 23.01, 104.12, 18.61 ,103.86, 18.61, 103.86}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -3.40, 140.40, 0, 0, 0, 0}, 
{'c', -3.40, 140.40, 1.80 ,140.00, 0.60, 141.60}, 
{'c', -0.60, 143.20, -3.00 ,142.40, -3.00, 142.40}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -76.60, 41.20, 0, 0, 0, 0}, 
{'c', -76.60, 41.20, -81.00 ,50.00, -81.40, 53.20}, 
{'c', -81.40, 53.20, -80.60 ,44.40, -79.40, 42.40}, 
{'f', 0.603000,0.134000,0.000000,1.000000,0,0 }, 
{'m', -95.00, 55.20, 0, 0, 0, 0}, 
{'c', -95.00, 55.20, -98.20 ,69.60, -97.80, 72.40}, 
{'c', -97.80, 72.40, -99.00 ,60.80, -98.60, 59.60}, 
{'f', 0.603000,0.134000,0.000000,1.000000,0,0 }, 
{'m', -74.20, -19.40, 0, 0, 0, 0}, 
{'l', -74.40, -16.20, 0, 0, 0, 0}, 
{'l', -76.60, -16.00, 0, 0, 0, 0}, 
{'c', -76.60, -16.00, -62.40 ,-3.40, -61.80, 4.20}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -70.22, -18.14, 0, 0, 0, 0}, 
{'c', -70.65, -18.55, -70.43 ,-19.30, -70.84, -19.56}, 
{'c', -71.64, -20.07, -69.54 ,-20.13, -69.77, -20.84}, 
{'c', -70.15, -22.05, -69.96 ,-22.07, -70.08, -23.35}, 
{'c', -70.14, -23.95, -69.55 ,-25.49, -69.17, -25.93}, 
{'c', -67.72, -27.58, -69.05 ,-30.51, -67.41, -32.06}, 
{'c', -67.10, -32.35, -66.73 ,-32.90, -66.44, -33.32}, 
{'c', -65.78, -34.28, -64.60 ,-34.77, -63.65, -35.60}, 
{'c', -63.33, -35.88, -63.53 ,-36.70, -62.96, -36.61}, 
{'c', -62.25, -36.49, -61.01 ,-36.62, -61.05, -35.78}, 
{'c', -61.16, -33.66, -62.49 ,-31.94, -63.77, -30.28}, 
{'c', -63.32, -29.57, -63.78 ,-28.94, -64.06, -28.38}, 
{'c', -65.40, -25.76, -65.21 ,-22.92, -65.39, -20.08}, 
{'c', -65.39, -19.99, -65.70 ,-19.92, -65.69, -19.86}, 
{'c', -65.34, -17.53, -64.75 ,-15.33, -63.87, -13.10}, 
{'c', -63.51, -12.17, -63.04 ,-11.28, -62.89, -10.35}, 
{'c', -62.77, -9.66, -62.67 ,-8.83, -63.08, -8.12}, 
{'c', -61.05, -5.23, -62.35 ,-2.58, -61.19, 0.95}, 
{'c', -60.98, 1.57, -59.29 ,3.49, -59.75, 3.33}, 
{'c', -62.26, 2.46, -62.37 ,2.06, -62.55, 1.30}, 
{'c', -62.70, 0.68, -63.03 ,-0.70, -63.26, -1.30}, 
{'c', -63.33, -1.46, -63.50 ,-3.35, -63.58, -3.47}, 
{'c', -65.09, -5.85, -63.73 ,-5.67, -65.10, -8.03}, 
{'c', -66.53, -8.71, -67.50 ,-9.82, -68.62, -10.98}, 
{'c', -68.82, -11.18, -67.67 ,-11.91, -67.86, -12.12}, 
{'c', -68.95, -13.41, -70.10 ,-14.18, -69.76, -15.67}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -73.80, -16.40, 0, 0, 0, 0}, 
{'c', -73.80, -16.40, -73.40 ,-9.60, -71.00, -8.00}, 
{'c', -68.60, -6.40, -69.80 ,-7.20, -73.00, -8.40}, 
{'c', -76.20, -9.60, -75.00 ,-10.40, -75.00, -10.40}, 
{'c', -75.00, -10.40, -77.80 ,-10.00, -75.40, -8.00}, 
{'c', -73.00, -6.00, -69.40 ,-3.60, -71.00, -3.60}, 
{'c', -72.60, -3.60, -80.20 ,-7.60, -80.20, -10.40}, 
{'c', -80.20, -13.20, -81.20 ,-17.30, -81.20, -17.30}, 
{'c', -81.20, -17.30, -80.10 ,-18.10, -75.30, -18.00}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -74.60, 2.20, 0, 0, 0, 0}, 
{'c', -74.60, 2.20, -83.12 ,-0.59, -101.60, 2.80}, 
{'c', -101.60, 2.80, -92.57 ,0.72, -73.80, 3.00}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -72.50, 2.13, 0, 0, 0, 0}, 
{'c', -72.50, 2.13, -80.75 ,-1.39, -99.45, 0.39}, 
{'c', -99.45, 0.39, -90.28 ,-0.90, -71.77, 3.00}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -70.71, 2.22, 0, 0, 0, 0}, 
{'c', -70.71, 2.22, -78.68 ,-1.90, -97.46, -1.51}, 
{'c', -97.46, -1.51, -88.21 ,-2.12, -70.05, 3.14}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -69.44, 2.44, 0, 0, 0, 0}, 
{'c', -69.44, 2.44, -76.27 ,-1.86, -93.14, -2.96}, 
{'c', -93.14, -2.96, -84.80 ,-2.79, -68.92, 3.32}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 45.84, 12.96, 0, 0, 0, 0}, 
{'c', 45.84, 12.96, 44.91 ,13.61, 45.12, 12.42}, 
{'c', 45.34, 11.24, 73.55 ,-1.93, 77.16, -1.68}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 42.45, 13.60, 0, 0, 0, 0}, 
{'c', 42.45, 13.60, 41.57 ,14.31, 41.69, 13.12}, 
{'c', 41.81, 11.93, 68.90 ,-3.42, 72.52, -3.45}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 39.16, 14.97, 0, 0, 0, 0}, 
{'c', 39.16, 14.97, 38.33 ,15.75, 38.37, 14.55}, 
{'c', 38.42, 13.35, 58.23 ,-2.15, 68.05, -4.02}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 36.28, 16.84, 0, 0, 0, 0}, 
{'c', 36.28, 16.84, 35.54 ,17.53, 35.58, 16.45}, 
{'c', 35.62, 15.37, 53.45 ,1.43, 62.28, -0.26}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 4.60, 164.80, 0, 0, 0, 0}, 
{'c', 4.60, 164.80, -10.60 ,162.40, 6.20, 160.80}, 
{'c', 6.20, 160.80, 24.20 ,158.80, 28.20, 153.60}, 
{'c', 28.20, 153.60, 41.80 ,144.40, 44.60, 144.00}, 
{'c', 47.40, 143.60, 63.80 ,140.00, 64.20, 137.60}, 
{'c', 64.60, 135.20, 70.60 ,132.80, 72.20, 133.60}, 
{'c', 73.80, 134.40, 73.80 ,143.60, 71.00, 144.40}, 
{'c', 68.20, 145.20, 49.40 ,152.40, 43.00, 153.60}, 
{'c', 36.60, 154.80, 25.00 ,162.40, 20.20, 163.60}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 77.60, 127.40, 0, 0, 0, 0}, 
{'c', 77.60, 127.40, 74.60 ,129.00, 73.40, 131.60}, 
{'c', 73.40, 131.60, 67.00 ,142.20, 52.80, 145.40}, 
{'c', 52.80, 145.40, 29.80 ,154.40, 22.00, 156.40}, 
{'c', 22.00, 156.40, 8.60 ,161.40, 1.20, 160.60}, 
{'c', 1.20, 160.60, -5.80 ,160.80, 0.40, 162.40}, 
{'c', 0.40, 162.40, 20.60 ,160.40, 24.00, 158.60}, 
{'c', 24.00, 158.60, 39.60 ,153.40, 42.60, 150.80}, 
{'c', 45.60, 148.20, 63.80 ,143.20, 66.00, 141.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 18.88, 158.91, 0, 0, 0, 0}, 
{'c', 18.88, 158.91, 24.11 ,158.69, 22.96, 160.23}, 
{'c', 21.80, 161.78, 19.36 ,160.91, 19.36, 160.91}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 11.68, 160.26, 0, 0, 0, 0}, 
{'c', 11.68, 160.26, 16.91 ,160.04, 15.76, 161.59}, 
{'c', 14.60, 163.14, 12.15 ,162.26, 12.15, 162.26}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 1.25, 161.51, 0, 0, 0, 0}, 
{'c', 1.25, 161.51, 6.48 ,161.28, 5.33, 162.83}, 
{'c', 4.17, 164.38, 1.73 ,163.51, 1.73, 163.51}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -6.38, 162.06, 0, 0, 0, 0}, 
{'c', -6.38, 162.06, -1.15 ,161.83, -2.31, 163.38}, 
{'c', -3.46, 164.93, -5.91 ,164.05, -5.91, 164.05}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 35.41, 151.51, 0, 0, 0, 0}, 
{'c', 35.41, 151.51, 42.38 ,151.21, 40.84, 153.27}, 
{'c', 39.31, 155.34, 36.05 ,154.17, 36.05, 154.17}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 45.73, 147.09, 0, 0, 0, 0}, 
{'c', 45.73, 147.09, 51.69 ,143.79, 51.16, 148.85}, 
{'c', 50.88, 151.41, 46.36 ,149.75, 46.36, 149.75}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 54.86, 144.27, 0, 0, 0, 0}, 
{'c', 54.86, 144.27, 62.02 ,140.57, 60.29, 146.03}, 
{'c', 59.51, 148.49, 55.49 ,146.94, 55.49, 146.94}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 64.38, 139.45, 0, 0, 0, 0}, 
{'c', 64.38, 139.45, 68.73 ,134.55, 69.80, 141.21}, 
{'c', 70.21, 143.75, 65.01 ,142.11, 65.01, 142.11}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 26.83, 156.00, 0, 0, 0, 0}, 
{'c', 26.83, 156.00, 32.06 ,155.77, 30.91, 157.32}, 
{'c', 29.76, 158.87, 27.31 ,158.00, 27.31, 158.00}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 62.43, 34.60, 0, 0, 0, 0}, 
{'c', 62.43, 34.60, 61.71 ,35.27, 61.71, 34.20}, 
{'c', 61.71, 33.13, 79.19 ,19.86, 88.03, 18.48}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 65.40, 98.40, 0, 0, 0, 0}, 
{'c', 65.40, 98.40, 87.40 ,120.80, 96.60, 124.40}, 
{'c', 96.60, 124.40, 105.80 ,135.60, 101.80, 161.60}, 
{'c', 101.80, 161.60, 98.60 ,169.20, 95.40, 148.40}, 
{'c', 95.40, 148.40, 98.60 ,123.20, 87.40, 139.20}, 
{'c', 87.40, 139.20, 79.00 ,129.30, 85.40, 129.60}, 
{'c', 85.40, 129.60, 88.60 ,131.60, 89.00, 130.00}, 
{'c', 89.40, 128.40, 81.40 ,114.80, 64.20, 100.40}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', 7.00, 137.20, 0, 0, 0, 0}, 
{'c', 7.00, 137.20, 6.80 ,135.40, 8.60, 136.20}, 
{'c', 10.40, 137.00, 104.60 ,143.20, 136.20, 167.20}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 17.40, 132.80, 0, 0, 0, 0}, 
{'c', 17.40, 132.80, 17.20 ,131.00, 19.00, 131.80}, 
{'c', 20.80, 132.60, 157.40 ,131.60, 181.00, 164.00}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 29.00, 128.80, 0, 0, 0, 0}, 
{'c', 29.00, 128.80, 28.80 ,127.00, 30.60, 127.80}, 
{'c', 32.40, 128.60, 205.80 ,115.60, 229.40, 148.00}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 39.00, 124.00, 0, 0, 0, 0}, 
{'c', 39.00, 124.00, 38.80 ,122.20, 40.60, 123.00}, 
{'c', 42.40, 123.80, 164.60 ,85.20, 188.20, 117.60}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -19.00, 146.80, 0, 0, 0, 0}, 
{'c', -19.00, 146.80, -19.20 ,145.00, -17.40, 145.80}, 
{'c', -15.60, 146.60, 2.20 ,148.80, 4.20, 187.60}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -27.80, 148.40, 0, 0, 0, 0}, 
{'c', -27.80, 148.40, -28.00 ,146.60, -26.20, 147.40}, 
{'c', -24.40, 148.20, -10.20 ,143.60, -13.00, 182.40}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -35.80, 148.80, 0, 0, 0, 0}, 
{'c', -35.80, 148.80, -36.00 ,147.00, -34.20, 147.80}, 
{'c', -32.40, 148.60, -17.00 ,149.20, -29.40, 171.60}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 11.53, 104.47, 0, 0, 0, 0}, 
{'c', 11.53, 104.47, 11.08 ,106.46, 12.63, 105.25}, 
{'c', 28.70, 92.62, 61.14 ,33.72, 116.83, 28.09}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 22.73, 102.67, 0, 0, 0, 0}, 
{'c', 22.73, 102.67, 21.36 ,101.47, 23.23, 100.85}, 
{'c', 25.10, 100.22, 137.54 ,27.72, 176.83, 35.69}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 1.89, 108.77, 0, 0, 0, 0}, 
{'c', 1.89, 108.77, 1.38 ,110.37, 3.09, 109.39}, 
{'c', 12.06, 104.27, 15.68 ,47.06, 59.25, 45.80}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -18.04, 119.79, 0, 0, 0, 0}, 
{'c', -18.04, 119.79, -19.11 ,121.08, -17.16, 120.83}, 
{'c', -6.92, 119.49, 14.49 ,78.22, 58.93, 83.30}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -6.80, 113.67, 0, 0, 0, 0}, 
{'c', -6.80, 113.67, -7.61 ,115.14, -5.74, 114.51}, 
{'c', 4.06, 111.24, 17.14 ,66.62, 61.73, 63.08}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -25.08, 124.91, 0, 0, 0, 0}, 
{'c', -25.08, 124.91, -25.95 ,125.95, -24.37, 125.75}, 
{'c', -16.07, 124.67, 1.27 ,91.24, 37.26, 95.35}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -32.68, 130.82, 0, 0, 0, 0}, 
{'c', -32.68, 130.82, -33.68 ,131.87, -32.09, 131.75}, 
{'c', -27.92, 131.44, 2.71 ,98.36, 21.18, 113.86}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 36.85, 98.90, 0, 0, 0, 0}, 
{'c', 36.85, 98.90, 35.65 ,97.54, 37.59, 97.16}, 
{'c', 39.52, 96.77, 160.22 ,39.06, 198.18, 51.93}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 3.40, 163.20, 0, 0, 0, 0}, 
{'c', 3.40, 163.20, 3.20 ,161.40, 5.00, 162.20}, 
{'c', 6.80, 163.00, 22.20 ,163.60, 9.80, 186.00}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 13.80, 161.60, 0, 0, 0, 0}, 
{'c', 13.80, 161.60, 13.60 ,159.80, 15.40, 160.60}, 
{'c', 17.20, 161.40, 35.00 ,163.60, 37.00, 202.40}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 20.60, 160.00, 0, 0, 0, 0}, 
{'c', 20.60, 160.00, 20.40 ,158.20, 22.20, 159.00}, 
{'c', 24.00, 159.80, 48.60 ,163.20, 72.20, 195.60}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 28.23, 157.97, 0, 0, 0, 0}, 
{'c', 28.23, 157.97, 27.79 ,156.21, 29.68, 156.77}, 
{'c', 31.57, 157.32, 52.00 ,155.42, 90.10, 189.60}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 38.62, 153.57, 0, 0, 0, 0}, 
{'c', 38.62, 153.57, 38.19 ,151.81, 40.08, 152.37}, 
{'c', 41.97, 152.92, 76.80 ,157.42, 128.50, 192.40}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -1.80, 142.00, 0, 0, 0, 0}, 
{'c', -1.80, 142.00, -2.00 ,140.20, -0.20, 141.00}, 
{'c', 1.60, 141.80, 55.00 ,144.40, 85.40, 171.20}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -11.80, 146.00, 0, 0, 0, 0}, 
{'c', -11.80, 146.00, -12.00 ,144.20, -10.20, 145.00}, 
{'c', -8.40, 145.80, 16.20 ,149.20, 39.80, 181.60}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 49.50, 148.96, 0, 0, 0, 0}, 
{'c', 49.50, 148.96, 48.94 ,147.24, 50.86, 147.66}, 
{'c', 52.79, 148.07, 87.86 ,150.00, 141.98, 181.10}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 57.90, 146.56, 0, 0, 0, 0}, 
{'c', 57.90, 146.56, 57.34 ,144.84, 59.26, 145.25}, 
{'c', 61.19, 145.67, 96.26 ,147.60, 150.38, 178.70}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', 67.50, 141.56, 0, 0, 0, 0}, 
{'c', 67.50, 141.56, 66.94 ,139.84, 68.86, 140.25}, 
{'c', 70.79, 140.67, 113.86 ,145.00, 203.58, 179.30}, 
{'f', 1.000000,1.000000,1.000000,1.000000,0,0 }, 
{'m', -43.80, 148.40, 0, 0, 0, 0}, 
{'c', -43.80, 148.40, -38.60 ,148.00, -39.80, 149.60}, 
{'c', -41.00, 151.20, -43.40 ,150.40, -43.40, 150.40}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -13.00, 162.40, 0, 0, 0, 0}, 
{'c', -13.00, 162.40, -7.80 ,162.00, -9.00, 163.60}, 
{'c', -10.20, 165.20, -12.60 ,164.40, -12.60, 164.40}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -21.80, 162.00, 0, 0, 0, 0}, 
{'c', -21.80, 162.00, -16.60 ,161.60, -17.80, 163.20}, 
{'c', -19.00, 164.80, -21.40 ,164.00, -21.40, 164.00}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -117.17, 150.18, 0, 0, 0, 0}, 
{'c', -117.17, 150.18, -112.12 ,151.50, -113.78, 152.62}, 
{'c', -115.44, 153.74, -117.45 ,152.20, -117.45, 152.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -115.17, 140.58, 0, 0, 0, 0}, 
{'c', -115.17, 140.58, -110.12 ,141.91, -111.78, 143.02}, 
{'c', -113.44, 144.14, -115.45 ,142.60, -115.45, 142.60}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -122.37, 136.18, 0, 0, 0, 0}, 
{'c', -122.37, 136.18, -117.32 ,137.50, -118.98, 138.62}, 
{'c', -120.64, 139.74, -122.65 ,138.20, -122.65, 138.20}, 
{'f', 0.000000,0.000000,0.000000,1.000000,0,0 }, 
{'m', -42.60, 211.20, 0, 0, 0, 0}, 
{'c', -42.60, 211.20, -44.20 ,211.20, -48.20, 213.20}, 
{'c', -50.20, 213.20, -61.40 ,216.80, -67.00, 226.80}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 45.12, 303.85, 0, 0, 0, 0}, 
{'c', 45.26, 304.11, 45.31 ,304.52, 45.60, 304.54}, 
{'c', 46.26, 304.58, 47.49 ,304.88, 47.37, 304.25}, 
{'c', 46.52, 299.94, 45.65 ,295.00, 41.52, 293.20}, 
{'c', 40.88, 292.92, 39.43 ,293.33, 39.36, 294.21}, 
{'c', 39.23, 295.74, 39.12 ,297.09, 39.42, 298.55}, 
{'c', 39.73, 299.98, 41.88 ,299.99, 42.80, 298.60}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 34.04, 308.58, 0, 0, 0, 0}, 
{'c', 34.79, 309.99, 34.66 ,311.85, 36.07, 312.42}, 
{'c', 36.81, 312.71, 38.66 ,311.74, 38.25, 310.66}, 
{'c', 37.44, 308.60, 37.06 ,306.36, 35.67, 304.55}, 
{'c', 35.47, 304.29, 35.71 ,303.75, 35.55, 303.43}, 
{'c', 34.95, 302.21, 33.81 ,301.47, 32.40, 301.80}, 
{'c', 31.29, 304.00, 32.43 ,306.13, 33.95, 307.84}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -5.56, 303.39, 0, 0, 0, 0}, 
{'c', -5.67, 303.01, -5.71 ,302.55, -5.54, 302.23}, 
{'c', -5.01, 301.20, -4.22 ,300.07, -4.56, 299.05}, 
{'c', -4.91, 298.00, -6.02 ,298.18, -6.67, 298.75}, 
{'c', -7.81, 299.74, -7.86 ,301.57, -8.55, 302.93}, 
{'c', -8.74, 303.31, -8.69 ,303.89, -9.13, 304.28}, 
{'c', -9.61, 304.70, -10.05 ,306.22, -9.95, 306.79}, 
{'c', -9.90, 307.11, -10.08 ,317.01, -9.86, 316.75}, 
{'c', -9.24, 316.02, -6.19 ,306.28, -6.12, 305.39}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -31.20, 296.60, 0, 0, 0, 0}, 
{'c', -28.57, 294.10, -25.78 ,291.14, -26.22, 287.43}, 
{'c', -26.34, 286.45, -28.11 ,286.98, -28.30, 287.82}, 
{'c', -29.10, 291.45, -31.14 ,294.11, -33.71, 296.50}, 
{'c', -35.90, 298.55, -37.77 ,304.89, -38.00, 305.40}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -44.78, 290.63, 0, 0, 0, 0}, 
{'c', -44.25, 290.26, -44.55 ,289.77, -44.34, 289.44}, 
{'c', -43.38, 287.98, -42.08 ,286.74, -42.07, 285.00}, 
{'c', -42.06, 284.72, -42.44 ,284.41, -42.78, 284.64}, 
{'c', -43.05, 284.82, -43.40 ,284.95, -43.50, 285.08}, 
{'c', -45.53, 287.53, -46.93 ,290.20, -48.38, 293.01}, 
{'c', -48.56, 293.37, -49.70 ,297.86, -49.39, 297.97}, 
{'c', -49.15, 298.06, -47.43 ,293.88, -47.22, 293.76}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -28.04, 310.18, 0, 0, 0, 0}, 
{'c', -27.60, 309.31, -26.02 ,308.11, -26.14, 307.22}, 
{'c', -26.25, 306.29, -25.79 ,304.85, -26.70, 305.54}, 
{'c', -27.95, 306.48, -31.40 ,307.83, -31.67, 313.64}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -13.60, 293.00, 0, 0, 0, 0}, 
{'c', -13.20, 292.33, -12.49 ,292.81, -12.03, 292.54}, 
{'c', -11.38, 292.17, -10.77 ,291.61, -10.48, 290.96}, 
{'c', -9.51, 288.81, -7.74 ,287.00, -7.60, 284.60}, 
{'c', -9.09, 283.20, -9.77 ,285.24, -10.40, 286.20}, 
{'c', -11.72, 284.55, -12.72 ,286.43, -14.02, 286.95}, 
{'c', -14.09, 286.98, -14.30 ,286.63, -14.38, 286.65}, 
{'c', -15.56, 287.10, -16.24 ,288.18, -17.23, 288.96}, 
{'c', -17.41, 289.09, -17.81 ,288.91, -17.96, 289.05}, 
{'c', -18.61, 289.65, -19.58 ,289.98, -19.86, 290.66}, 
{'c', -20.97, 293.36, -24.11 ,295.46, -26.00, 303.00}, 
{'c', -25.62, 303.91, -21.49 ,296.36, -21.00, 295.66}, 
{'c', -20.16, 294.46, -20.05 ,297.32, -18.77, 296.66}, 
{'c', -18.72, 296.63, -18.53 ,296.87, -18.40, 297.00}, 
{'c', -18.21, 296.72, -17.99 ,296.49, -17.60, 296.60}, 
{'c', -17.60, 296.20, -17.73 ,295.64, -17.53, 295.49}, 
{'c', -16.30, 294.51, -16.38 ,293.44, -15.60, 292.20}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 46.20, 347.40, 0, 0, 0, 0}, 
{'c', 46.20, 347.40, 53.60 ,327.00, 49.20, 315.80}, 
{'c', 49.20, 315.80, 60.60 ,337.40, 56.00, 348.60}, 
{'c', 56.00, 348.60, 55.60 ,338.20, 51.60, 333.20}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 31.40, 344.80, 0, 0, 0, 0}, 
{'c', 31.40, 344.80, 36.80 ,336.00, 28.80, 317.60}, 
{'c', 28.80, 317.60, 28.00 ,338.00, 21.20, 349.00}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 21.40, 342.80, 0, 0, 0, 0}, 
{'c', 21.40, 342.80, 21.20 ,322.80, 21.60, 319.80}, 
{'c', 21.60, 319.80, 17.80 ,336.40, 7.60, 346.00}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 11.80, 310.80, 0, 0, 0, 0}, 
{'c', 11.80, 310.80, 17.80 ,324.40, 7.80, 342.80}, 
{'c', 7.80, 342.80, 14.20 ,330.60, 9.40, 323.60}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -7.40, 342.40, 0, 0, 0, 0}, 
{'c', -7.40, 342.40, -8.40 ,326.80, -6.60, 324.60}, 
{'c', -6.60, 324.60, -6.40 ,318.20, -6.80, 317.20}, 
{'c', -6.80, 317.20, -2.80 ,311.00, -2.60, 318.40}, 
{'c', -2.60, 318.40, -1.20 ,326.20, 1.60, 330.80}, 
{'c', 1.60, 330.80, 5.20 ,336.20, 5.00, 342.60}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -11.00, 314.80, 0, 0, 0, 0}, 
{'c', -11.00, 314.80, -17.60 ,325.60, -19.40, 344.60}, 
{'c', -19.40, 344.60, -20.80 ,338.40, -17.00, 324.00}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -32.80, 334.60, 0, 0, 0, 0}, 
{'c', -32.80, 334.60, -27.80 ,329.20, -26.40, 324.20}, 
{'c', -26.40, 324.20, -22.80 ,308.40, -29.20, 317.00}, 
{'c', -29.20, 317.00, -29.00 ,325.00, -37.20, 332.40}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -38.60, 329.60, 0, 0, 0, 0}, 
{'c', -38.60, 329.60, -35.20 ,312.20, -34.40, 311.40}, 
{'c', -34.40, 311.40, -32.60 ,308.00, -35.40, 311.20}, 
{'c', -35.40, 311.20, -44.20 ,330.40, -48.20, 337.00}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -44.40, 313.00, 0, 0, 0, 0}, 
{'c', -44.40, 313.00, -32.80 ,290.60, -54.60, 316.40}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -59.80, 298.40, 0, 0, 0, 0}, 
{'c', -59.80, 298.40, -55.00 ,279.60, -52.40, 279.80}, 
{'c', -52.40, 279.80, -44.20 ,270.80, -50.80, 281.40}, 
{'c', -50.80, 281.40, -56.80 ,291.00, -56.20, 300.80}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 270.50, 287.00, 0, 0, 0, 0}, 
{'c', 270.50, 287.00, 258.50 ,277.00, 256.00, 273.50}, 
{'c', 256.00, 273.50, 269.50 ,292.00, 269.50, 299.00}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 276.00, 265.00, 0, 0, 0, 0}, 
{'c', 276.00, 265.00, 255.00 ,250.00, 251.50, 242.50}, 
{'c', 251.50, 242.50, 278.00 ,272.00, 278.00, 276.50}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 293.00, 111.00, 0, 0, 0, 0}, 
{'c', 293.00, 111.00, 281.00 ,103.00, 279.50, 105.00}, 
{'c', 279.50, 105.00, 290.00 ,111.50, 292.50, 120.00}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 301.50, 191.50, 0, 0, 0, 0}, 
{'l', 284.00, 179.50, 0, 0, 0, 0}, 
{'c', 284.00, 179.50, 303.00 ,196.50, 303.50, 200.50}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -89.25, 169.00, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'l', -67.25, 173.75, 0, 0, 0, 0}, 
{'m', -39.00, 331.00, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -33.50, 336.00, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 20.50, 344.50, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 301.50, 191.50, 0, 0, 0, 0}, 
{'l', 284.00, 179.50, 0, 0, 0, 0}, 
{'c', 284.00, 179.50, 303.00 ,196.50, 303.50, 200.50}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -89.25, 169.00, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'l', -67.25, 173.75, 0, 0, 0, 0}, 
{'m', -39.00, 331.00, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -33.50, 336.00, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 20.50, 344.50, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 301.50, 191.50, 0, 0, 0, 0}, 
{'l', 284.00, 179.50, 0, 0, 0, 0}, 
{'c', 284.00, 179.50, 303.00 ,196.50, 303.50, 200.50}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -89.25, 169.00, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'l', -67.25, 173.75, 0, 0, 0, 0}, 
{'m', -39.00, 331.00, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', -33.50, 336.00, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }, 
{'m', 20.50, 344.50, 0, 0, 0, 0}, 
{'f', 0.804000,0.804000,0.804000,1.000000,0,0 }};
