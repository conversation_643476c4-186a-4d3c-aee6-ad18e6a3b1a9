#!/usr/bin/python

# Print the difference between two results.txt files from cairo tests

import sys

old_filename = sys.argv[1]
new_filename = sys.argv[2]

results = { }

f = open(old_filename, "r")
for line in f.readlines():
    if 'RESULT:' not in line:
        continue

    items = line.split(" ")
    testcase = dict(zip(items[0::2], items[1::2]))

    try:
        k = "%s.%s.%s" %(
            testcase['TEST:'],
            testcase['TARGET:'],
            testcase.get('FORMAT:', ''))
        results[k] = testcase['RESULT:']
    except:
        print line
        raise

f = open(new_filename, "r")
for line in f.readlines():
    if 'RESULT:' not in line:
        # Not a results line.  Skip
        continue

    items = line.split(" ")
    testcase = dict(zip(items[0::2], items[1::2]))
    try:
        k = "%s.%s.%s" %(
            testcase['TEST:'],
            testcase['TARGET:'],
            testcase.get('FORMAT:',''))
    except:
        print line
        raise

    if k not in results.keys():
        # New test? Skip
        continue

    old_val = results[k].strip()
    new_val = testcase['RESULT:'].strip()
    if old_val == new_val:
        # Test didn't change.  Skip
        continue

    print("%s -> %s # %s" % (old_val, new_val, k))
