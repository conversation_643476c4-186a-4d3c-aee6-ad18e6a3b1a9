/*
 * Copyright © 2011 Intel Corporation
 *
 * Permission is hereby granted, free of charge, to any person
 * obtaining a copy of this software and associated documentation
 * files (the "Software"), to deal in the Software without
 * restriction, including without limitation the rights to use, copy,
 * modify, merge, publish, distribute, sublicense, and/or sell copies
 * of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
 * BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
 * ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *
 * Author: <PERSON> <<EMAIL>>
 */

#include "cairo-test.h"

/* The goal is exercise a bug that existed in the xlib backend, where
 * it assumed the rectangles generated by rectangular tessallator had
 * any sorting guarantees.
 */

#define WIDTH 300
#define HEIGHT 300

static cairo_surface_t *
create_source (cairo_surface_t *target)
{
    cairo_surface_t *surface;
    cairo_t *cr;

    surface = cairo_surface_create_similar (target,
					    CAIRO_CONTENT_COLOR,
					    WIDTH, HEIGHT);
    cr = cairo_create (surface);
    cairo_surface_destroy (surface);

    cairo_set_source_rgb (cr, 1, 1, 1);
    cairo_paint (cr);

    surface = cairo_surface_reference (cairo_get_target (cr));
    cairo_destroy (cr);

    return surface;
}

static cairo_test_status_t
draw (cairo_t *cr, int width, int height)
{
    cairo_surface_t *white;
    int x;

    /* black background */
    cairo_set_source_rgb (cr, 0, 0, 0);
    cairo_paint (cr);

    /* white rectangles */
    white = create_source (cairo_get_target (cr));
    cairo_set_source_surface (cr, white, 0, 0);
    cairo_surface_destroy (white);

    /* blit a set of rectangles that the rectangular tessellator
     * will not emit sorted. */
    for (x = 0; x < WIDTH - 10; x += 15)
	cairo_rectangle (cr, x, x, 10, HEIGHT - 2*x);
    cairo_fill (cr);

    return CAIRO_TEST_SUCCESS;
}

CAIRO_TEST (copy_disjoint,
	    "Tests copying unsorted rectangles.",
	    "fill", /* keywords */
	    NULL, /* requirements */
	    WIDTH, HEIGHT,
	    NULL, draw)
