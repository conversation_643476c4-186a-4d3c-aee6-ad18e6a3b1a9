<?xml version="1.0" encoding="UTF-8"?>
<ttFont sfntVersion="\x00\x01\x00\x00" ttLibVersion="4.19">

  <GlyphOrder>
    <!-- The 'id' attribute is only for humans; it is ignored when parsed. -->
    <GlyphID id="0" name=".notdef"/>
  </GlyphOrder>

  <head>
    <!-- Most of this table will be recalculated by the compiler -->
    <tableVersion value="1.0"/>
    <fontRevision value="1.0"/>
    <checkSumAdjustment value="0x3e7355ef"/>
    <magicNumber value="0x5f0f3cf5"/>
    <flags value="00000000 00000001"/>
    <unitsPerEm value="1000"/>
    <created value="Wed Jun 15 00:00:00 2022"/>
    <modified value="Wed Jun 15 00:00:00 2022"/>
    <xMin value="0"/>
    <yMin value="0"/>
    <xMax value="1000"/>
    <yMax value="1000"/>
    <macStyle value="00000000 00000000"/>
    <lowestRecPPEM value="6"/>
    <fontDirectionHint value="2"/>
    <indexToLocFormat value="0"/>
    <glyphDataFormat value="0"/>
  </head>

  <hhea>
    <tableVersion value="0x00010000"/>
    <ascent value="1000"/>
    <descent value="0"/>
    <lineGap value="200"/>
    <advanceWidthMax value="1100"/>
    <minLeftSideBearing value="0"/>
    <minRightSideBearing value="0"/>
    <xMaxExtent value="1000"/>
    <caretSlopeRise value="1"/>
    <caretSlopeRun value="0"/>
    <caretOffset value="0"/>
    <reserved0 value="0"/>
    <reserved1 value="0"/>
    <reserved2 value="0"/>
    <reserved3 value="0"/>
    <metricDataFormat value="0"/>
    <numberOfHMetrics value="2"/>
  </hhea>

  <maxp>
    <!-- Most of this table will be recalculated by the compiler -->
    <tableVersion value="0x10000"/>
    <numGlyphs value="2"/>
    <maxPoints value="1"/>
    <maxContours value="1"/>
    <maxCompositePoints value="1"/>
    <maxCompositeContours value="1"/>
    <maxZones value="1"/>
    <maxTwilightPoints value="0"/>
    <maxStorage value="0"/>
    <maxFunctionDefs value="0"/>
    <maxInstructionDefs value="0"/>
    <maxStackElements value="256"/>
    <maxSizeOfInstructions value="1"/>
    <maxComponentElements value="2"/>
    <maxComponentDepth value="1"/>
  </maxp>

  <OS_2>
    <!-- The fields 'usFirstCharIndex' and 'usLastCharIndex'
         will be recalculated by the compiler -->
    <version value="3"/>
    <xAvgCharWidth value="1000"/>
    <usWeightClass value="400"/>
    <usWidthClass value="5"/>
    <fsType value="00000000 00000001"/>
    <ySubscriptXSize value="600"/>
    <ySubscriptYSize value="600"/>
    <ySubscriptXOffset value="0"/>
    <ySubscriptYOffset value="75"/>
    <ySuperscriptXSize value="600"/>
    <ySuperscriptYSize value="600"/>
    <ySuperscriptXOffset value="0"/>
    <ySuperscriptYOffset value="300"/>
    <yStrikeoutSize value="0"/>
    <yStrikeoutPosition value="300"/>
    <sFamilyClass value="0"/>
    <panose>
      <bFamilyType value="0"/>
      <bSerifStyle value="0"/>
      <bWeight value="0"/>
      <bProportion value="0"/>
      <bContrast value="0"/>
      <bStrokeVariation value="0"/>
      <bArmStyle value="0"/>
      <bLetterForm value="0"/>
      <bMidline value="0"/>
      <bXHeight value="0"/>
    </panose>
    <ulUnicodeRange1 value="00000000 00000000 00000000 00000001"/>
    <ulUnicodeRange2 value="00000000 00000000 00000000 00000000"/>
    <ulUnicodeRange3 value="00000000 00000000 00000000 00000000"/>
    <ulUnicodeRange4 value="00000000 00000000 00000000 00000000"/>
    <achVendID value="djr "/>
    <fsSelection value="00000000 01000000"/>
    <usFirstCharIndex value="65"/>
    <usLastCharIndex value="65"/>
    <sTypoAscender value="1000"/>
    <sTypoDescender value="0"/>
    <sTypoLineGap value="200"/>
    <usWinAscent value="1000"/>
    <usWinDescent value="300"/>
    <ulCodePageRange1 value="00000000 00000000 00000000 00000001"/>
    <ulCodePageRange2 value="00000000 00000000 00000000 00000000"/>
    <sxHeight value="500"/>
    <sCapHeight value="720"/>
    <usDefaultChar value="0"/>
    <usBreakChar value="32"/>
    <usMaxContext value="3"/>
  </OS_2>

  <hmtx>
    <mtx name=".notdef" width="1100" lsb="0"/>
  </hmtx>

  <cmap>
    <tableVersion version="0"/>
    <cmap_format_4 platformID="0" platEncID="3" language="0">
    </cmap_format_4>
  </cmap>

  <loca>
    <!-- The 'loca' table will be calculated by the compiler -->
  </loca>

  <glyf>

    <!-- The xMin, yMin, xMax and yMax values
         will be recalculated by the compiler. -->

    <TTGlyph name=".notdef"/><!-- contains no outline data -->

  </glyf>

  <name>
    <namerecord nameID="1" platformID="3" platEncID="1" langID="0x409">
      Cairo Font Template
    </namerecord>
    <namerecord nameID="2" platformID="3" platEncID="1" langID="0x409">
      Regular
    </namerecord>
    <namerecord nameID="4" platformID="3" platEncID="1" langID="0x409">
      Cairo Font Template Regular
    </namerecord>
  </name>

  <post>
    <formatType value="3.0"/>
    <italicAngle value="0.0"/>
    <underlinePosition value="0"/>
    <underlineThickness value="0"/>
    <isFixedPitch value="0"/>
    <minMemType42 value="0"/>
    <maxMemType42 value="0"/>
    <minMemType1 value="0"/>
    <maxMemType1 value="0"/>
  </post>

  <CPAL>
    <version value="0"/>
    <numPaletteEntries value="2"/>
    <palette index="0">
      <color index="0" value="#C5A1D7FF"/>
      <color index="1" value="#80DFC8FF"/>
    </palette>
    <palette index="1">
      <color index="0" value="#6392A9FF"/>
      <color index="1" value="#7896B3FF"/>
    </palette>
  </CPAL>
  
  <SVG>
    <svgDoc endGlyphID="0" startGlyphID="0">
      <![CDATA[<svg xmlns="http://www.w3.org/2000/svg"></svg>]]>
    </svgDoc>
    <colorPalettes>
    </colorPalettes>
  </SVG>

</ttFont>
