libFuzzer based fuzzing for cairo-svg-glyph-render.c
====================================================

Build
-----
CC=clang CFLAGS="-DDEBUG_SVG_RENDER -g -fsanitize=fuzzer-no-link,address" meson -Db_lundef=false bld-fuzzer
ninja -C bld-fuzzer


Test
----
  ./bld-fuzzer/test/svg/fuzzer/svg-render-fuzzer <CORPUS DIR>

where <CORPUS DIR> is a directory containing SVG files.

If the fuzzer crashes, a crash-* file will be written. Run the
fuzzer with the crash file to reproduce the crash.

  ./bld-fuzzer/test/svg/fuzzer/svg-render-fuzzer <crash-file>
