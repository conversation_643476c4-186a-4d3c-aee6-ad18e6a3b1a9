build_ttx_fonts.py
==================
Is used to create the test/*.ttx files used for testing the SVG glyph renderer.

build_ttx_fonts.py will look for files of the form

<font-name>.<char>.<test-name>.svg

in the input directory, and using svg-font-template.ttx, create <font-name>.ttx
files that contain one glyph for each svg file. Each <font-name>.ttx will contain
a glyph for each svg with the matching <font-name> prefix. Each glyphs will be mapped
to the <char> i nthe svg filename. The <char> must be one of 0-9,A-F.

svg-render.c
============
svg-render renders SVG files using both librsvg and cairo-svg-glyph-render.c.
It is used for testing cairo-svg-glyph-render.c during development.

To use svg-render, cairo must be built with CFLAGS="-DDEBUG_SVG_RENDER" to enable the
_cairo_debug_svg_render() function.


