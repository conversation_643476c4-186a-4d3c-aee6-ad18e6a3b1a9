#! /bin/sh

set -e

if test $# -eq 0; then
    echo "$0: no input files." >&2
    exit 1
fi

cat <<HERE
/* WARNING: Autogenerated file - see $0! */

#include "cairo-test-private.h"

void _cairo_test_runner_register_tests (void);

HERE

cat "$@" |  sed '/^CAIRO_TEST/!d; s/CAIRO_TEST.*(\(.*\),.*/extern void _register_\1 (void);/'
cat <<HERE

void
_cairo_test_runner_register_tests (void)
{
HERE

cat "$@" |  sed '/^CAIRO_TEST/!d; s/CAIRO_TEST.*(\(.*\),.*/    _register_\1 ();/'

echo "}"


