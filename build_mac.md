## build for macos
mkdir build
cd build
### configure for debug
meson setup .. --reconfigure \
-Dfontconfig=enabled \
-Dfreetype=enabled \
-Dquartz=enabled \
-Dglib=disabled  \
-Dxcb=disabled \
-Dxlib=disabled \
-Dpng=enabled \
--buildtype=debug \
-Doptimization=0 

### configure for release
meson setup .. --reconfigure \
-Dfontconfig=enabled \
-Dfreetype=enabled \
-Dquartz=enabled \
-Dglib=disabled  \
-Dxcb=disabled \
-Dxlib=disabled \
-Dpng=enabled \
--buildtype=release \
-Doptimization=3

### build and install
ninja -j8
ninja install



# build static cairo and pixman
meson setup .. --reconfigure \
-Dfontconfig=enabled \
-Dfreetype=enabled \
-Dquartz=enabled \
-Dglib=disabled  \
-Dxcb=disabled \
-Dxlib=disabled \
-Dpng=enabled \
--buildtype=release \
-Doptimization=3 \
--default-library=static

# 下载并解压
curl -O https://cairographics.org/releases/pixman-0.42.2.tar.gz
tar -xzf pixman-0.42.2.tar.gz
cd pixman-0.42.2

# 创建独立构建目录
mkdir pixman-build
cd pixman-build

# 配置构建选项
meson setup .. \
  --buildtype=release \
  --default-library=static \
  --prefix=$PWD/install \
  -Dtests=disabled