/* Generated by <PERSON><PERSON><PERSON>.win32.features-h.  Do not edit. */
#ifndef CAIRO_FEATURES_H
#define CAIRO_FEATURES_H 1

#cmakedefine CAIRO_HAS_XLIB_SURFACE 1
#cmakedefine CAIRO_HAS_XLIB_XRENDER_SURFACE 1
#cmakedefine CAIRO_HAS_XCB_SURFACE 1
#cmakedefine CAIRO_HAS_XLIB_XCB_FUNCTIONS 1
#cmakedefine CAIRO_HAS_XCB_SHM_FUNCTIONS 1
#cmakedefine CAIRO_HAS_QT_SURFACE 1
#cmakedefine CAIRO_HAS_QUARTZ_SURFACE 1
#cmakedefine CAIRO_HAS_QUARTZ_FONT 1
#cmakedefine CAIRO_HAS_QUARTZ_IMAGE_SURFACE 1
#cmakedefine CAIRO_HAS_WIN32_SURFACE 1
#cmakedefine CAIRO_HAS_WIN32_FONT 1
#cmakedefine CAIRO_HAS_SKIA_SURFACE 1
#cmakedefine CAIRO_HAS_OS2_SURFACE 1
#cmakedefine CAIRO_HAS_BEOS_SURFACE 1
#cmakedefine CAIRO_HAS_DRM_SURFACE 1
#cmakedefine CAIRO_HAS_GALLIUM_SURFACE 1
#cmakedefine CAIRO_HAS_PNG_FUNCTIONS 1
#cmakedefine CAIRO_HAS_GL_SURFACE 1
#cmakedefine CAIRO_HAS_GLESV2_SURFACE 1
#cmakedefine CAIRO_HAS_COGL_SURFACE 1
#cmakedefine CAIRO_HAS_DIRECTFB_SURFACE 1
#cmakedefine CAIRO_HAS_VG_SURFACE 1
#cmakedefine CAIRO_HAS_EGL_FUNCTIONS 1
#cmakedefine CAIRO_HAS_GLX_FUNCTIONS 1
#cmakedefine CAIRO_HAS_WGL_FUNCTIONS 1
#cmakedefine CAIRO_HAS_SCRIPT_SURFACE 1
#cmakedefine CAIRO_HAS_FT_FONT 1
#cmakedefine CAIRO_HAS_FC_FONT 1
#cmakedefine CAIRO_HAS_PS_SURFACE 1
#cmakedefine CAIRO_HAS_PDF_SURFACE 1
#cmakedefine CAIRO_HAS_SVG_SURFACE 1
#cmakedefine CAIRO_HAS_TEST_SURFACES 1
#cmakedefine CAIRO_HAS_TEE_SURFACE 1
#cmakedefine CAIRO_HAS_XML_SURFACE 1
#cmakedefine CAIRO_HAS_PTHREAD 1
#cmakedefine CAIRO_HAS_GOBJECT_FUNCTIONS 1
#cmakedefine CAIRO_HAS_TRACE 1
#cmakedefine CAIRO_HAS_INTERPRETER 1
#cmakedefine CAIRO_HAS_SYMBOL_LOOKUP 1

#endif
