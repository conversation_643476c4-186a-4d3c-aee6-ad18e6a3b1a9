perf_micro_sources = [
  'cairo-perf-cover.c',
  'box-outline.c',
  'composite-checker.c',
  'disjoint.c',
  'fill.c',
  'hatching.c',
  'hash-table.c',
  'line.c',
  'a1-line.c',
  'long-lines.c',
  'mosaic.c',
  'paint.c',
  'paint-with-alpha.c',
  'mask.c',
  'pattern_create_radial.c',
  'rectangles.c',
  'rounded-rectangles.c',
  'stroke.c',
  'subimage_copy.c',
  'tessellate.c',
  'text.c',
  'tiger.c',
  'glyphs.c',
  'twin.c',
  'unaligned-clip.c',
  'wave.c',
  'world-map.c',
  'zrusin.c',
  'long-dashed-lines.c',
  'dragon.c',
  'pythagoras-tree.c',
  'intersections.c',
  'many-strokes.c',
  'wide-strokes.c',
  'many-fills.c',
  'wide-fills.c',
  'many-curves.c',
  'curve.c',
  'a1-curve.c',
  'spiral.c',
  'pixel.c',
  'sierpinski.c',
  'fill-clip.c',
]

perf_micro_headers = [
  'mosaic.h',
  'world-map.h',
  'zrusin-another.h',
]

libcairoperfmicro = static_library('cairo-perf-micro',
  perf_micro_sources + perf_micro_headers,
  include_directories: [incbase, incsrc, incmicro],
  dependencies: [pixman_dep, cairoboilerplate_dep],
)
libcairoperfmicro_dep = declare_dependency(
  link_with: libcairoperfmicro,
)
