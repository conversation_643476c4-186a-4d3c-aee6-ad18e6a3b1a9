a1-clip-fill-rule
big-empty-box
big-empty-triangle
big-little-box
bitmap-font
bug-361
bug-431
bug-source-cu
clip-device-offset
clip-fill-rule-pixel-aligned
clip-text
culled-glyphs
dash-zero-length
degenerate-path
device-offset
device-offset-positive
extended-blend-alpha-mask
fill-and-stroke
fill-empty
fill-missed-stop
finer-grained-fallbacks
ft-color-font
ft-show-glyphs-positioning
ft-text-vertical-layout-type1
ft-text-vertical-layout-type3
gradient-alpha
gradient-constant-alpha
gradient-zero-stops
gradient-zero-stops-mask
halo-transform
image-surface-source
linear-gradient-one-stop
mask-ctm
mask-surface-ctm
new-sub-path
nil-surface
overlapping-boxes
overlapping-glyphs
pass-through
pdf-surface-source
pixman-downscale-best-24
pixman-rotate
ps-surface-source
radial-gradient-source
record1414x-select-font-face
record1414x-text-transform
record-neg-extents-bounded
record-neg-extents-unbounded
record-replay-extend-none
record-replay-extend-pad
record-replay-extend-reflect
record-replay-extend-repeat
rel-path
scale-source-surface-paint
set-source
shifted-operator
show-glyphs-advance
source-surface-scale-paint
subsurface
subsurface-outside-target
subsurface-scale
text-antialias-subpixel
text-antialias-subpixel-bgr
text-antialias-subpixel-rgb
text-antialias-subpixel-vbgr
text-antialias-subpixel-vrgb
text-pattern
text-rotate
text-unhinted-metrics
thin-lines
user-font-mask
xcb-surface-source
xlib-surface-source
