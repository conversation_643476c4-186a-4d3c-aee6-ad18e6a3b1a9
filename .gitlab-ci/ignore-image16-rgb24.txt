a1-bug
a1-clip-fill-rule
a1-tiger
aliasing
arc-direction
arc-looping-dash
big-empty-box
big-empty-triangle
big-line
big-little-box
bitmap-font
bug-84115
bug-bo-ricotz
bug-extents
bug-image-compositor
bug-seams
bug-source-cu
bug-spline
caps
caps-05
caps-2
caps-joins-2
caps-joins-alpha
caps-joins-curve
caps-tails-curve
clear-source
clip-device-offset
clip-disjoint
clip-disjoint-quad
clip-fill
clip-fill-eo-unbounded
clip-fill-nz-unbounded
clip-fill-rule-pixel-aligned
clip-image
clip-intersect
clip-operator
clip-push-group
clip-shape
clip-stroke
clip-stroke-unbounded
clip-text
clip-twice
close-path-current-point
copy-path
coverage-abutting
coverage-column-triangles
coverage-intersecting-quads
coverage-intersecting-triangles
coverage-rectangles
coverage-rhombus
coverage-row-triangles
coverage-triangles
culled-glyphs
dash-caps-joins
dash-curve
dash-scale
dash-state
dash-zero-length
degenerate-arc
degenerate-curve-to
degenerate-path
degenerate-pen
degenerate-rel-curve-to
device-offset
device-offset-positive
drunkard-tails
extended-blend
extended-blend-alpha
extended-blend-alpha-mask
extended-blend-mask
extended-blend-solid
extended-blend-solid-alpha
extend-pad-border
fallback
fill-alpha
fill-alpha-pattern
fill-and-stroke
fill-and-stroke-alpha
fill-and-stroke-alpha-add
fill-degenerate-sort-order
fill-empty
fill-image
fill-missed-stop
fill-rule
filter-bilinear-extents
finer-grained-fallbacks
font-matrix-translation
ft-show-glyphs-positioning
ft-show-glyphs-table
ft-text-vertical-layout-type1
ft-text-vertical-layout-type3
glyph-cache-pressure
gradient-alpha
gradient-constant-alpha
gradient-zero-stops
gradient-zero-stops-mask
group-unaligned
halo
halo-transform
hatchings
huge-linear
huge-radial
inverse-text
joins
joins-loop
joins-retrace
joins-star
large-font
large-twin-antialias-mixed
leaky-dashed-stroke
linear-gradient
linear-gradient-one-stop
linear-gradient-reflect
linear-gradient-subset
line-width-overlap-offset
line-width-scale
long-dashed-lines
mask
mask-alpha
mask-ctm
mask-surface-ctm
mask-transformed-image
mask-transformed-similar
mesh-pattern
mesh-pattern-conical
mesh-pattern-control-points
mesh-pattern-fold
mesh-pattern-overlap
mesh-pattern-transformed
new-sub-path
nil-surface
operator-source
over-around-source
overlapping-boxes
overlapping-dash-caps
overlapping-glyphs
paint-source-alpha
paint-with-alpha
paint-with-alpha-clip
paint-with-alpha-clip-mask
partial-coverage-reference
partial-coverage-three-quarter-reference
pass-through
path-append
path-stroke-twice
pdf-isolated-group
pixman-downscale-best-24
pixman-downscale-best-96
pixman-downscale-bilinear-96
pixman-downscale-fast-96
pixman-downscale-good-96
pixman-downscale-nearest-96
pixman-rotate
pthread-same-source
pthread-show-text
push-group
push-group-color
radial-gradient
radial-gradient-mask
radial-gradient-mask-source
radial-gradient-source
random-clip
random-intersections-curves-eo
random-intersections-curves-nz
random-intersections-eo
random-intersections-nonzero
raster-source
record1414x-fill-alpha
record1414x-paint-alpha
record1414x-paint-alpha-clip
record1414x-paint-alpha-clip-mask
record1414x-select-font-face
record1414x-text-transform
record2x-fill-alpha
record2x-paint-alpha
record2x-paint-alpha-clip
record2x-paint-alpha-clip-mask
record2x-select-font-face
record2x-text-transform
record90-fill-alpha
record90-paint-alpha
record90-paint-alpha-clip
record90-paint-alpha-clip-mask
record90-select-font-face
record90-text-transform
record-fill-alpha
recordflip-fill-alpha
recordflip-paint-alpha
recordflip-paint-alpha-clip
recordflip-paint-alpha-clip-mask
recordflip-select-font-face
recordflip-text-transform
recordflip-whole-fill-alpha
recordflip-whole-paint-alpha
recordflip-whole-paint-alpha-clip
recordflip-whole-paint-alpha-clip-mask
recordflip-whole-select-font-face
recordflip-whole-text-transform
recording-surface-extend-none
recording-surface-extend-reflect
recording-surface-extend-repeat
recording-surface-over
recording-surface-source
record-mesh
record-neg-extents-bounded
record-neg-extents-unbounded
record-paint-alpha
record-paint-alpha-clip
record-paint-alpha-clip-mask
record-replay-extend-none
record-replay-extend-pad
record-replay-extend-reflect
record-replay-extend-repeat
record-select-font-face
record-text-transform
rectilinear-dash-scale-unaligned
reflected-stroke
rel-path
rotate-clip-image-surface-paint
rotated-clip
rounded-rectangle-fill
rounded-rectangle-stroke
scale-offset-image
scale-offset-similar
scale-source-surface-paint
select-font-face
set-source
shape-general-convex
shape-sierpinski
shifted-operator
show-glyphs-advance
show-text-current-point
simple-edge
smask
smask-fill
smask-mask
smask-paint
smask-stroke
smask-text
source-surface-scale-paint
spline-decomposition
stroke-ctm-caps
stroke-image
stroke-pattern
subsurface
subsurface-outside-target
subsurface-scale
surface-pattern
surface-pattern-operator
surface-pattern-scale-down
surface-pattern-scale-up
text-antialias-gray
text-antialias-subpixel
text-antialias-subpixel-bgr
text-antialias-subpixel-rgb
text-antialias-subpixel-vbgr
text-antialias-subpixel-vrgb
text-glyph-range
text-pattern
text-rotate
text-transform
text-unhinted-metrics
thin-lines
tiger
tighten-bounds
transforms
trap-clip
twin
twin-antialias-gray
twin-antialias-mixed
twin-antialias-subpixel
unbounded-operator
unclosed-strokes
user-font
user-font-mask
user-font-proxy
user-font-rescale
world-map
world-map-fill
world-map-stroke
xcb-huge-image-shm
xcb-huge-subimage
xcomposite-projection
