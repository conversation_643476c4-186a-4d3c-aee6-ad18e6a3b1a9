a1-clip-fill-rule
aliasing
alpha-similar
arc-looping-dash
big-empty-box
big-empty-triangle
big-little-box
bitmap-font
bug-84115
bug-extents
bug-image-compositor
bug-spline
caps-joins-curve
clip-device-offset
clip-disjoint
clip-fill-rule-pixel-aligned
clip-operator
clip-shape
clip-stroke
clip-stroke-unbounded
clip-text
close-path-current-point
coverage-column-triangles
coverage-rhombus
culled-glyphs
dash-caps-joins
dash-curve
dash-scale
degenerate-curve-to
degenerate-pen
degenerate-rel-curve-to
device-offset
device-offset-positive
drunkard-tails
extended-blend-alpha-mask
fill-and-stroke
fill-and-stroke-alpha
fill-and-stroke-alpha-add
fill-empty
fill-image
ft-color-font
ft-show-glyphs-positioning
ft-text-vertical-layout-type1
ft-text-vertical-layout-type3
gradient-alpha
gradient-constant-alpha
gradient-zero-stops
gradient-zero-stops-mask
halo
halo-transform
hatchings
image-surface-source
joins
joins-loop
joins-retrace
large-twin-antialias-mixed
leaky-dashed-stroke
linear-gradient-one-stop
line-width-scale
line-width-tolerance
long-dashed-lines
mask-ctm
mask-surface-ctm
new-sub-path
nil-surface
operator-source
overlapping-boxes
overlapping-dash-caps
overlapping-glyphs
pass-through
path-stroke-twice
pdf-surface-source
pixman-downscale-best-24
pixman-rotate
ps-surface-source
pthread-show-text
radial-gradient
radial-gradient-mask
radial-gradient-mask-source
radial-gradient-source
radial-outer-focus
random-clip
random-intersections-curves-eo
random-intersections-curves-nz
random-intersections-eo
random-intersections-nonzero
record1414x-fill-alpha
record1414x-select-font-face
record1414x-self-intersecting
record1414x-text-transform
record2x-fill-alpha
record2x-paint-alpha-clip-mask
record2x-select-font-face
record2x-text-transform
record90-paint-alpha-clip
record90-paint-alpha-solid-clip
record90-select-font-face
record90-self-intersecting
record90-text-transform
recordflip-paint-alpha-clip-mask
recordflip-select-font-face
recordflip-self-intersecting
recordflip-text-transform
recordflip-whole-fill-alpha
recordflip-whole-paint-alpha-clip-mask
recordflip-whole-select-font-face
recordflip-whole-self-intersecting
recordflip-whole-text-transform
recording-surface-extend-pad
record-neg-extents-bounded
record-neg-extents-unbounded
record-replay-extend-none
record-replay-extend-pad
record-replay-extend-reflect
record-replay-extend-repeat
record-select-font-face
record-self-intersecting
record-text-transform
reflected-stroke
rel-path
rounded-rectangle-stroke
scale-offset-image
scale-offset-similar
scale-source-surface-paint
set-source
shape-general-convex
shape-sierpinski
shifted-operator
show-glyphs-advance
simple-edge
smask
smask-stroke
smask-text
source-surface-scale-paint
spline-decomposition
stroke-clipped
stroke-image
stroke-pattern
subsurface
subsurface-outside-target
subsurface-scale
text-antialias-subpixel
text-antialias-subpixel-bgr
text-antialias-subpixel-rgb
text-antialias-subpixel-vbgr
text-antialias-subpixel-vrgb
text-pattern
text-rotate
text-unhinted-metrics
thin-lines
tighten-bounds
twin
twin-antialias-gray
twin-antialias-mixed
twin-antialias-none
twin-antialias-subpixel
unclosed-strokes
user-font
user-font-proxy
world-map
world-map-stroke
xcb-surface-source
xlib-surface-source
