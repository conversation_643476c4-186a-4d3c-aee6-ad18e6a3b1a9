a1-clip-fill-rule
alpha-similar
big-empty-box
big-empty-triangle
big-little-box
bitmap-font
bug-431
bug-spline
clip-device-offset
clip-fill-rule-pixel-aligned
clip-operator
coverage-column-triangles
coverage-intersecting-quads
coverage-intersecting-triangles
coverage-rectangles
coverage-row-triangles
coverage-triangles
culled-glyphs
device-offset
device-offset-positive
extended-blend-alpha-mask
fill-empty
fill-image
ft-color-font
ft-text-vertical-layout-type1
gradient-alpha
gradient-constant-alpha
gradient-zero-stops
gradient-zero-stops-mask
halo
halo-transform
image-surface-source
linear-gradient-one-stop
mask-ctm
mask-surface-ctm
nil-surface
operator-source
overlapping-boxes
pass-through
pdf-surface-source
pixman-downscale-best-24
pixman-rotate
ps-surface-source
push-group
push-group-color
radial-outer-focus
record1414x-fill-alpha
record1414x-self-intersecting
record2x-fill-alpha
record2x-paint-alpha-clip-mask
record90-paint-alpha-clip
record90-paint-alpha-solid-clip
record90-self-intersecting
recordflip-paint-alpha-clip-mask
recordflip-self-intersecting
recordflip-whole-fill-alpha
recordflip-whole-paint-alpha-clip-mask
recordflip-whole-self-intersecting
recording-surface-extend-pad
record-neg-extents-bounded
record-neg-extents-unbounded
record-replay-extend-none
record-replay-extend-pad
record-replay-extend-reflect
record-replay-extend-repeat
record-self-intersecting
rotated-clip
scale-source-surface-paint
set-source
simple-edge
source-surface-scale-paint
stroke-clipped
stroke-image
subsurface-outside-target
text-antialias-subpixel
text-antialias-subpixel-bgr
text-antialias-subpixel-rgb
text-antialias-subpixel-vbgr
text-antialias-subpixel-vrgb
text-pattern
text-rotate
thin-lines
tighten-bounds
twin-antialias-none
unantialiased-shapes
xcb-surface-source
xlib-surface-source
