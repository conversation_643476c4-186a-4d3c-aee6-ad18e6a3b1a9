<PERSON> <josh<PERSON><PERSON>@gmail.com> Memory leak fix for quartz backend
<PERSON> <<EMAIL>> Many (magic) floating-point optimizations
Shawn <PERSON> <<EMAIL>> Build fix
<PERSON> <<EMAIL>> PNG backend
<PERSON> <<EMAIL>> Bug fix for clipping
<PERSON> <<EMAIL>> Build fixes, Debian packaging
<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> SVG bug fixes
<PERSON> <<EMAIL>> BeOS backend
<PERSON> <<EMAIL>> Pixman code merge. Optimization. Fixes for subtle rendering bugs.
<PERSON> <<EMAIL>> win32 bug fixes, build fixes, and improvements
<PERSON> <<EMAIL>> Flag bug in Sun's X server
<PERSON> <<EMAIL>> libspectre integration into the test-suite
Andrea <PERSON> <<EMAIL>> Bugs, quartz backend improvements and type 6/7 patterns.
<PERSON> <<EMAIL>> Build fixes
<PERSON> <<EMAIL>> Adding const where needed
<PERSON> <<EMAIL>> Bug fixes for PNG reading
<PERSON><PERSON> <<EMAIL>> Bug fixes
Manu Cornet <<EMAIL>> SVG build fix
Frederic Crozat <<EMAIL>> Fix test suite for OPD platforms (IA64 or PPC64)
Julien Danjou <<EMAIL>> XCB fixes
Radek Doulík <<EMAIL>> Bug report and test case
John Ehresman <<EMAIL>> Build fixes for win32
John Ellson <<EMAIL>> First font/glyph extents functions
Michael Emmel <<EMAIL>> DirectFB backend
Miklós Erdélyi <<EMAIL>> Fix typo leading to a crash
Behdad Esfahbod <<EMAIL>> Huge piles of bug fixes, improvements, and general maintenance
Gilles Espinasse <<EMAIL>> Font related fixes
Larry Ewing <<EMAIL>> Test case for group-clip
Brian Ewins <<EMAIL>> ATSUI maintenance (first success at making it really work)
Bertram Felgenhauer <<EMAIL>> Fixes for subtle arithmetic errors
Damian Frank <<EMAIL>> Build system improvements for win32
Bdale Garbee <<EMAIL>> Provided essential support for cairo architecture sessions
Jens Granseuer <<EMAIL>> Fixes to generate proper compiler flags
Laxmi Harikumar <<EMAIL>> Build fix
J. Ali Harlow <<EMAIL>> win32 backend updates
Bryce Harrington <<EMAIL>> Test cases, bug/typo fixes
Mathias Hasselmann <<EMAIL>> Significant reduction of calls to malloc
Richard Henderson <<EMAIL>> "slim" macros for better shared libraries 
James Henstridge <<EMAIL>> Build fixes related to freetype
Graydon Hoare <<EMAIL>> Support for non-render X server, first real text support
Thomas Hunger <<EMAIL>> Initial version of cairo_in_stroke/fill
Thomas Jaeger <<EMAIL>> Extended repeat modes for X
Björn Lindqvist <<EMAIL>> Performance test cases
Kristian Høgsberg <<EMAIL>> PDF backend, PS backend with meta-surfaces
Amaury Jacquot <<EMAIL>> Documentation review, application testing
Adrian Johnson <<EMAIL>> PDF backend improvement
Michael Johnson <<EMAIL>> Bug fix for pre-C99 compilers
Jonathon Jongsma <<EMAIL>> Fix documentation typos
Øyvind Kolås <<EMAIL>> OpenVG backend, Bug fixes. Better default values.
Martin Kretzschmar <<EMAIL>> Arithmetic fix for 64-bit architectures
Mathieu Lacage <<EMAIL>> several bug/typo fixes
Dominic Lachowicz <<EMAIL>> PDF conformance fix, fix image surface to zero out contents
Alexander Larsson <<EMAIL>> Profiling and performance fixes.
Sylvestre Ledru <<EMAIL>> Static analysis fixes.
Tor Lillqvist <<EMAIL>> win32 build fixes, build scripts
Jinghua Luo <<EMAIL>> Add bitmap glyph transformation, many freetype and glitz fixes
Luke-Jr <<EMAIL>> Build fix for cross-compiling
Kjartan Maraas <<EMAIL>> Several fixes for sparse, lots of debug help for multi-thread bugs
Nis Martensen <<EMAIL>> Bug fix for sub paths
Jordi Mas <<EMAIL>> Bug fix for cairo_show_text
Nicholas Miell <<EMAIL>> Fixes for linking bugs on AMD64
Eugeniy Meshcheryakov <<EMAIL>> PS/PDF font subsetting improvements
Zakharov Mikhail <<EMAIL>> Build fix for HP-UX
Christopher (Monty) Montgomery <<EMAIL>> Performance fix (subimage_copy), multi-thread testing
Tim Mooney <<EMAIL>> Fix test suite to compile with Solaris compiler
Jeff Muizelaar <<EMAIL>> Patient, painful, pixman code merge. Many fixes for intricacies of dashing.
Yevgen Muntyan <<EMAIL>> win32 build fix
Ravi Nanjundappa <<EMAIL>> Static analysis fixes, test cases, skia backend update/fixes
Declan Naughton <<EMAIL>> Fix documentation typos
Peter Nilsson <<EMAIL>> Glitz backend
Henning Noren <<EMAIL>> Fix memory leak
Geoff Norton <<EMAIL>> Build fixes
Robert O'Callahan <<EMAIL>> Const-correctness fixes, several new API functions for completeness (and to help mozilla)
Ian Osgood <<EMAIL>> XCB backend maintenance
Benjamin Otte <<EMAIL>> Refinements to cairo/perf timing, OpenGL backend fixups, random fixes
Mike Owens <<EMAIL>> Bug fixes
Emmanuel Pacaud <<EMAIL>> SVG backend
Keith Packard <<EMAIL>> Original concept, polygon tessellation, dashing, font metrics rewrite
Stuart Parmenter <<EMAIL>> Original GDI+ backend, win32 fixes
Alfred Peng <<EMAIL>> Fixes for Sun compilers and for a memory leak
Christof Petig <<EMAIL>> Build fixes related to freetype
Joonas Pihlaja <<EMAIL>> Huge improvements to the tessellator performance
Mart Raudsepp <<EMAIL>> Build fixes
David Reveman <<EMAIL>> New pattern API, glitz backend
Calum Robinson <<EMAIL>> Quartz backend
Pavel Roskin <<EMAIL>> Several cleanups to eliminate warnings
Tim Rowley <<EMAIL>> Quartz/ATSUI fixes, X server workarounds, win32 glyph path support, test case to expose gradient regression
Soeren Sandmann <<EMAIL>> Lots of MMX love for pixman compositing
Uli Schlachter <<EMAIL>> Some more XCB fixes
Torsten Schönfeld <<EMAIL>> Build fixes
Jamey Sharp <<EMAIL>> Surface/font backend virtualization, XCB backend
Jason Dorje Short <<EMAIL>> Build fixes and bug fixes
Jeff Smith <<EMAIL>> Fixes for intricacies of stroking code
Travis Spencer <<EMAIL>> XCB backend fix
Bill Spitzak <<EMAIL>> Build fix to find Xrender.h without xrender.pc, downscaling support
Zhe Su <<EMAIL>> Add support for fontconfig's embeddedbitmap option
Owen Taylor <<EMAIL>> Font rewrite, documentation, win32 backend
Pierre Tardy <<EMAIL>> EGL support and testing, OpenVG backend
Karl Tomlinson <<EMAIL>> Optimisation and obscure bug fixes (mozilla)
Alp Toker <<EMAIL>> Fix several code/comment typos
Malcolm Tredinnick <<EMAIL>> Documentation fixes
David Turner <<EMAIL>> Optimize gradient calculations
Kalle Vahlman <<EMAIL>> Allow perf reports to be compared across different platforms
Sasha Vasko <<EMAIL>> Build fix to compile without xlib backend
Vladimir Vukicevic <<EMAIL>> Quartz backend rewrite, win32/quartz maintenance
Jonathan Watt <<EMAIL>> win32 fixes
Peter Weilbacher <<EMAIL>> OS/2 backend
Dan Williams <<EMAIL>> Implement MMX function to help OLPC
Chris Wilson <<EMAIL>> Large-scale robustness improvements, (warn_unsed_result and malloc failure injection)
Carl Worth <<EMAIL>> Original library, support for paths, images
Richard D. Worth <<EMAIL>> Build fixes for cygwin
Kent Worsnop <<EMAIL>> Fix PDF dashing bug
Dave Yeo <<EMAIL>> Build fix for win32

(please let us know if we have missed anyone)
